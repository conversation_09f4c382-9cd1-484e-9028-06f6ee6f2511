<script setup>
import { ref, computed } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticationCard from '@/Components/AuthenticationCard.vue';
import AuthenticationCardLogo from '@/Components/AuthenticationCardLogo.vue';
import Checkbox from '@/Components/Checkbox.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import Banner from '@/Components/Banner.vue';
import { Button } from '@/components/ui/button';

const form = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    terms: false,
    affiliateCode: '',
});

const showPassword = ref(false);
const showPasswordConfirmation = ref(false);
 
const passwordStrength = computed(() => {
    const password = form.password;
    let strength = 0;
    if (password.length > 0) strength += 1; 
    if (password.length >= 8) strength += 1;
    if (/[A-Za-z]/.test(password) && /\d/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1; 
    return strength;
});

const strengthBarStyles = computed(() => {
    const strength = passwordStrength.value;
    const width = strength * 25;
    const colorClass = strength === 0 ? 'bg-gray-200' : strength <= 2 ? 'bg-red-500' : strength === 3 ? 'bg-yellow-500' : 'bg-green-500';
    return { width: `${width}%`, colorClass };
});

const passwordsMatch = computed(() => {
    return form.password && form.password_confirmation && form.password === form.password_confirmation;
});

const submit = () => {
    form.post(route('sign-up'), {
        onFinish: () => form.reset('password', 'password_confirmation'),
    });
};
</script>

<template>
    <Head title="Register" />

    <Banner />

    <div class="pt-12">

    </div>
    <AuthenticationCard>
        <template #logo>
            <AuthenticationCardLogo />
        </template>

        <template #formtitle>
            <div class="text-center text-sm text-gray-600">
                Already have an account?
                <Link :href="route('login')" class="underline font-medium text-zata-primary-dark hover:text-zata-primary-light">
                    Log in
                </Link>
            </div>
        </template>

        <form @submit.prevent="submit" class="space-y-8">
            <!-- Name -->
            <div>
                <InputLabel for="name" value="Name" class="text-gray-700" />
                <TextInput
                    id="name"
                    v-model="form.name"
                    type="text"
                    class="mt-1 block w-full rounded-md border-gray-300 focus:ring-opacity-10"
                    required
                    autofocus
                />
                <InputError class="mt-2" :message="form.errors.name" v-if="form.errors.name" />
            </div>

            <!-- Email -->
            <div>
                <InputLabel for="email" value="Email" class="text-gray-700" />
                <TextInput
                    id="email"
                    v-model="form.email"
                    type="email"
                    class="mt-1 block w-full rounded-md border-gray-300focus:ring-opacity-10"
                    required
                    autocomplete="username"
                />
                <InputError class="mt-2" :message="form.errors.email" v-if="form.errors.email" />
            </div>

            <!-- Password -->
            <div class="mt-6">
                <InputLabel for="password" value="Password" class="text-gray-700" />
                <div class="relative">
                    <TextInput
                        id="password"
                        v-model="form.password"
                        :type="showPassword ? 'text' : 'password'"
                        class="mt-1 block w-full rounded-md border-gray-300  focus:ring-opacity-50"
                        required
                    />
                    <button
                        type="button"
                        class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500 hover:text-gray-700"
                        @click="showPassword = !showPassword"
                    >
                        <svg v-if="showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.542 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                        </svg>
                    </button>
                </div>
                <!-- Password Strength Indicator -->
                <div class="mt-2 h-1 rounded-full bg-gray-200 overflow-hidden">
                    <div
                        class="h-full rounded-full transition-all duration-300"
                        :class="strengthBarStyles.colorClass"
                        :style="{ width: strengthBarStyles.width }"
                    />
                </div>
                <InputError class="mt-2" :message="form.errors.password" />
            </div>

            <!-- Password Confirmation -->
            <div>
                <InputLabel for="password_confirmation" value="Confirm Password" class="text-gray-700" />
                <div class="relative">
                    <TextInput
                        id="password_confirmation"
                        v-model="form.password_confirmation"
                        :type="showPasswordConfirmation ? 'text' : 'password'"
                        class="mt-1 block w-full rounded-md border-gray-300 focus:ring-opacity-10"
                        required
                        autocomplete="new-password"
                    />
                    <button
                        type="button"
                        class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500 hover:text-gray-700"
                        @click="showPasswordConfirmation = !showPasswordConfirmation"
                    >
                        <svg v-if="showPasswordConfirmation" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.542 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                        </svg>
                    </button>
                </div>
                <!-- Password Matching Indicator -->
                <div v-if="form.password_confirmation" class="mt-2 text-sm" :class="passwordsMatch ? 'text-green-600' : 'text-red-600'">
                    {{ passwordsMatch ? 'Passwords match' : 'Passwords do not match' }}
                </div>
                <InputError class="mt-2" :message="form.errors.password_confirmation" />
            </div>

            <!-- Affiliate Code (Hidden) -->
            <div v-if="$page.props.jetstream.hasApiFeatures" class="hidden">
                <InputLabel for="affiliateCode" value="Affiliate Code" class="text-gray-700" />
                <TextInput
                    id="affiliateCode"
                    v-model="form.affiliateCode"
                    type="text"
                    class="mt-1 block w-full rounded-md border-gray-300"
                />
                <InputError class="mt-2" :message="form.errors.affiliateCode" />
            </div>

            <!-- Terms and Privacy Policy -->
            <div v-if="$page.props.jetstream.hasTermsAndPrivacyPolicyFeature">
                <InputLabel for="terms">
                    <div class="flex items-center">
                        <Checkbox id="terms" v-model:checked="form.terms" name="terms" required />
                        <div class="ml-2 text-sm text-gray-600">
                            I agree to the
                            <a target="_blank" :href="route('terms.show')" class="underline text-zata-primary-dark hover:text-zata-primary-light focus:outline-none focus:ring-2 focus:ring-zata-primary-light focus:ring-offset-2">Terms of Service</a>
                            and
                            <a target="_blank" :href="route('policy.show')" class="underline text-zata-primary-dark hover:text-zata-primary-light focus:outline-none focus:ring-2 focus:ring-zata-primary-light focus:ring-offset-2">Privacy Policy</a>
                        </div>
                    </div>
                    <InputError class="mt-2" :message="form.errors.terms" />
                </InputLabel>
            </div>

            <!-- Submit Button -->
            <div>
                <Button
                    class="w-full bg-zata-primary-dark hover:bg-zata-primary-light text-white font-semibold py-2 rounded-md transition duration-200"
                    :class="{ 'opacity-50 cursor-not-allowed': form.processing }"
                    :disabled="form.processing"
                >
                    Register
                </Button>
            </div>

            <!-- Google Sign-Up -->
            <div class="mt-6">
                <Link
                    :href="route('google-auth')"
                    class="flex items-center justify-center p-3 border border-gray-300 rounded-md bg-gray-50 hover:bg-gray-100 transition duration-200"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" viewBox="0 0 48 48">
                        <defs>
                            <path id="a" d="M44.5 20H24v8.5h11.8C34.7 33.9 30.1 37 24 37c-7.2 0-13-5.8-13-13s5.8-13 13-13c3.1 0 5.9 1.1 8.1 2.9l6.4-6.4C34.6 4.1 29.6 2 24 2 11.8 2 2 11.8 2 24s9.8 22 22 22c11 0 21-8 21-22 0-1.3-.2-2.7-.5-4z" />
                        </defs>
                        <clipPath id="b">
                            <use xlink:href="#a" overflow="visible" />
                        </clipPath>
                        <path clip-path="url(#b)" fill="#FBBC05" d="M0 37V11l17 13z" />
                        <path clip-path="url(#b)" fill="#EA4335" d="M0 11l17 13 7-6.1L48 14V0H0z" />
                        <path clip-path="url(#b)" fill="#34A853" d="M0 37l30-23 7.9 1L48 0v48H0z" />
                        <path clip-path="url(#b)" fill="#4285F4" d="M48 48L17 24l-4-3 35-10z" />
                    </svg>
                    <span class="ml-3 font-semibold text-gray-700">Sign up with Google</span>
                </Link>
            </div>
        </form>
    </AuthenticationCard>
</template>