<?php

namespace App\Http\Controllers;

use App\Services\InsuranceService;
use Inertia\Inertia;

class InsuranceController extends Controller
{
    public function __construct(protected InsuranceService $service)
    {
        
    }
    public function index()
    {

        $company =  companyData();
        $supportedInsurance = $this->service->supportedInsurance($company->id);
        return Inertia::render('Insurance/Index',[
            'Insurance' => $supportedInsurance
        ]);
    }
}
