<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use DateTime;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use DateTimeInterface;

class TransactionAccount extends Model
{
    /** @use HasFactory<\Database\Factories\TransactionAccountFactory> */
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'TransactionAccount';

    protected $fillable = [
        'branch_id',
        'payment_mode_id',
        'user_id',
        'amount',
        'type',
        'sourceType',
        'source_id',
        'date',
        'description'
    ];

    protected $casts = [
        'date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected function serializeDate(DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }
    
    public function getDateAttribute($value)
    {
        return \Carbon\Carbon::createFromTimestamp($value)->format('Y-m-d H:i:s');
    }

    public function paymentMode(): BelongsTo
    {
        return $this->belongsTo(PaymentMode::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeWhereTransactionType($query, $type)
    {

        if (!is_null($type)) {
            return $query->where('sourceType', $type);
        }

        return $query;
    }


    public function scopeFilterByDate($query, $startDate, $endDate)
    {

        $startDateTime = DateTime::createFromFormat('Y-m-d', $startDate);
        $isValidStartDate = $startDateTime && $startDateTime->format('Y-m-d') === $startDate;

        $endDateTime = DateTime::createFromFormat('Y-m-d', $endDate);
        $isValidEndDate = $endDateTime && $endDateTime->format('Y-m-d') === $endDate;

        if ($isValidStartDate && $isValidEndDate) {
            $query->whereBetween('date', [$startDate, $endDate]);
        } elseif ($isValidStartDate) {
            $query->where('date', '>=', $startDate);
        } elseif ($isValidEndDate) {
            $query->where('date', '<=', $endDate);
        }

        return $query;
    }
}
