<?php

use App\Models\User;
use App\Models\AccountSubscription;
use App\Models\Affiliate;
use Laravel\Jetstream\Jetstream;
use Illuminate\Support\Facades\Notification;
use Illuminate\Auth\Notifications\VerifyEmail;

test('login screen can be rendered', function () {
    $response = $this->get('/login');
    $response->assertStatus(200);
});

test('users can authenticate using the login screen', function () {
    $user = User::factory()->create();
    $response = $this->post('/login', [
        'email' => $user->email,
        'password' => 'password',
    ]);
    $this->assertAuthenticated();
    $response->assertRedirect(route('dashboard', absolute: false));
});

test('users can not authenticate with invalid password', function () {
    $user = User::factory()->create();
    $this->post('/login', [
        'email' => $user->email,
        'password' => 'wrong-password',
    ]);
    $this->assertGuest();
});

it('can render landing page', function () {
    $response = $this->get('/');
    $response->assertStatus(200);
});

it('can signup user with valid data', function () {
    Notification::fake();

    $data = [
        'name' => fake()->name(),
        'email' => fake()->unique()->safeEmail(),
        'password' => 'password',
        'password_confirmation' => 'password',
        'terms' => Jetstream::hasTermsAndPrivacyPolicyFeature(),
    ];

    $response = $this->post('/sign-up', $data);
    $response->assertRedirect(route('login', absolute: false));

    $this->assertDatabaseHas('users', [
        'name' => $data['name'],
        'email' => $data['email'],
    ]);

    $user = User::where('email', $data['email'])->first();

    $this->assertDatabaseHas('AccountSubscription', [
        'user_id' => $user->id,
        'isActive' => true,
        'subscriptionType' => 'basic',
    ]);

    Notification::assertSentTo($user, VerifyEmail::class);
});

it('can signup user with valid affiliate code', function () {

    $affiliate = Affiliate::factory()->create(['affiliateCode' => 'TEST123']);

    Notification::fake();

    $data = [
        'name' => fake()->name(),
        'email' => fake()->unique()->safeEmail(),
        'password' => 'password',
        'password_confirmation' => 'password',
        'affiliateCode' => 'TEST123',
        'terms' => Jetstream::hasTermsAndPrivacyPolicyFeature(),
    ];

    $response = $this->post('/sign-up', $data);

    $response->assertRedirect(route('login', absolute: false));

    $this->assertDatabaseHas('users', [
        'name' => $data['name'],
        'email' => $data['email'],
        'affiliateCode' => 'TEST123',
    ]);

    $user = User::where('email', $data['email'])->first();

    $this->assertDatabaseHas('AccountSubscription', [
        'user_id' => $user->id,
        'isActive' => true,
        'subscriptionType' => 'basic',
    ]);

    Notification::assertSentTo($user, VerifyEmail::class);
});

it('cannot signup user with invalid affiliate code', function () {
    $data = [
        'name' => fake()->name(),
        'email' => fake()->unique()->safeEmail(),
        'password' => 'password',
        'password_confirmation' => 'password',
        'affiliateCode' => 'INVALID123',
        'terms' => Jetstream::hasTermsAndPrivacyPolicyFeature(),
    ];

    $response = $this->post('/sign-up', $data);

    $response->assertStatus(404);

    $this->assertDatabaseMissing('users', [
        'email' => $data['email'],
    ]);
});

it('cannot signup user without accepting terms', function () {

    if (Jetstream::hasTermsAndPrivacyPolicyFeature()) {
        $data = [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'password' => 'password',
            'password_confirmation' => 'password',
            'terms' => false,
        ];

        $response = $this->post('/sign-up', $data);
        $response->assertSessionHasErrors('terms');

        $this->assertDatabaseMissing('users', [
            'email' => $data['email'],
        ]);
    }
});

it('cannot signup user with mismatched passwords', function () {
    $data = [
        'name' => fake()->name(),
        'email' => fake()->unique()->safeEmail(),
        'password' => 'password',
        'password_confirmation' => 'different-password',
        'terms' => Jetstream::hasTermsAndPrivacyPolicyFeature(),
    ];

    $response = $this->post('/sign-up', $data);

    $response->assertSessionHasErrors('password');

    $this->assertDatabaseMissing('users', [
        'email' => $data['email'],
    ]);
});
