<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from "@inertiajs/vue3";
import RadioOptionGroup from '@/Components/RadioOptionGroup.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import SelectOption from '@/Components/SelectOption.vue';
import FormSection from '@/Components/FormSection.vue';

const props = defineProps({
    'Roles': Array,
    'Branches': Array,
});

const form = useForm({
    'email': null,
    'name': null,
    'permissionID': null,
    'branchID': null,
});

function inviteUser() {
    form.post(route('user.storeInvitation'), {

    });
}

</script>

<template>
    <AppLayout title="Create Invitation ">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Create Invitation
            </h2>
        </template>

        <FormSection>
            <template #title>
                Create Invitation
            </template>

            <template #form>
                <form class="" @submit.prevent="inviteUser">
                    <div class="pb-4">
                        <InputLabel for="name" value="Name" />
                        <TextInput id="name" v-model="form.name" type="text" />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>

                    <div class="pb-4">
                        <InputLabel for="email" value="Email" />
                        <TextInput id="email" v-model="form.email" type="email" />
                        <InputError class="mt-2" :message="form.errors.email" />
                    </div>

                    <div class="grid grid-cols-6 gap-6">

                        <div class="col-span-6 sm:col-span-3">
                            <SelectOption id="permissionID" v-model="form.permissionID" :options="Roles" label="Role"
                                placeholder="Select a role" valueKey="id" labelKey="name" />
                            <InputError class="mt-2" :message="form.errors.permissionID" />
                        </div>

                        <div class="col-span-6 sm:col-span-3">
                            <SelectOption id="branchID" v-model="form.branchID" :options="Branches" label="Branch"
                                placeholder="Select a branch" valueKey="id" labelKey="name" />
                            <InputError class="mt-2" :message="form.errors.branchID" />
                        </div>
                    </div>
                    <div class="flex justify-end mt-4">
                        <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                            Invite user
                        </PrimaryButton>
                    </div>
                </form>
            </template>
        </FormSection>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>