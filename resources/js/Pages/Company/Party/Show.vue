<script setup>
import ButtonGroupOption from '@/Components/ButtonGroupOption.vue';
import ListOption from '@/Components/ListOption.vue';
import PreviewSection from '@/Components/PreviewSection.vue';
import PrimaryLink from '@/Components/PrimaryLink.vue';
import ProfileSection from '@/Components/ProfileSection.vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";

const props = defineProps({
    Party: Object,
});

const deatils = [

    { title: 'Email', value: props.Party.email },
    { title: 'TIN', value: props.Party.tin },
    { title: 'Phone', value: props.Party.phone },
    { title: 'Address', value: props.Party.address },
    { title: 'Last updated at', value: props.Party.updated_at },
];
</script>

<template>
    <AppLayout title="party">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                party
            </h2>
        </template>

        <PreviewSection>

            <template #content>

                <ProfileSection :image="Party.image" :title="Party.name" :description="Party.email" />

                <ButtonGroupOption>
                    <PrimaryLink :href="route('party.edit', Party.id)">Edit</PrimaryLink>
                </ButtonGroupOption>
                <ListOption :details="deatils" />
            </template>
        </PreviewSection>


        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>
