<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CompanyBranchUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:25|min:3',
            'phone' => 'nullable|string|max:12|min:10',
            'email' => 'nullable|string|max:255|min:3|email',
            'address' => 'required|string|max:255|min:3',
            'topMessage' => 'nullable|string|max:150|min:3',
            'bottomMessage' => 'nullable|string|max:150|min:3',
        ];
    }
}
