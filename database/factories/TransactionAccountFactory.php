<?php

namespace Database\Factories;

use App\Models\CompanyBranch;
use App\Models\PaymentMode;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TransactionAccount>
 */
class TransactionAccountFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'branch_id' => CompanyBranch::factory(),
            'payment_mode_id' => PaymentMode::factory(),
            'user_id' => User::factory(),
            'amount' => $this->faker->randomElement([12,1212,334,2323]),
            'type' =>  $this->faker->randomElement(['credit', 'debit']),
            'sourceType' => 'sales',
            'source_id' => 1,
            'date' => '',
            'description' => fake()->word()
        ];
    }
}
