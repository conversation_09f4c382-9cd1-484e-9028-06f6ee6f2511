<?php

namespace App\Services\Admin;

use App\Data\AdminUpdateCompanyBranchData;
use App\Data\AdminUpdateCompanyData;
use App\Exceptions\NotFound;
use App\Models\Company;
use App\Models\CompanyBranch;
use App\Models\InvoiceSequences;
use App\Services\EbmService;

class CompanyService
{

    public function __construct(protected EbmService $ebmService)
    {
    }

    public function company(): array
    {

        return Company::with('branches')
            ->get()
            ->toArray();
    }

    public function companyById(int $companyId): Company
    {
        $company  =  Company::with('branches')
            ->where('id', $companyId)
            ->with([
                'branches'
            ])
            ->first();

        throw_if(is_null($company), NotFound::class, 'Company not found');

        return $company;
    }

    public function updateCompany(AdminUpdateCompanyData $data, int $companyId): void
    {
        $company = Company::where('id', $companyId)->first();

        throw_if(is_null($company), NotFound::class, 'Company not found');

        $company->update([
            'name' => $data->name,
            'phone' => $data->phone,
            'email' => $data->email,
            'address' => $data->address,
            'tin' => $data->tin,
            'isActive' => $data->isActive,
            'isEBM' => $data->isEBM
        ]);


        if ($data->isEBM) {
            $company->branches()->update(['isEBM' => true]);
        }

        if (!$data->isEBM) {
            $company->branches()->update(['isEBM' => false]);
        }

        if ($data->isActive) {
            $company->branches()->update(['isActive' => true]);
        }

        if (!$data->isActive) {
            $company->branches()->update(['isActive' => false]);
        }

        $company->save();
    }

    public function branchById(int $companyId, int $branchId): CompanyBranch
    {
        $company = Company::where('id', $companyId)->first();

        throw_if(is_null($company), NotFound::class, 'Company not found');

        $branch = $company->branches()->where('id', $branchId)
        ->with('invoiceNumber')
        ->first();

        throw_if(is_null($branch), NotFound::class, 'Branch not found');

        return $branch;
    }

    public function updateBranch(AdminUpdateCompanyBranchData $data, int $companyId, int $branchId): void
    {
        $company = Company::where('id', $companyId)->first();

        throw_if(is_null($company), NotFound::class, 'Company not found');

        $branch = $company->branches()->where('id', $branchId)->first();

        throw_if(is_null($branch), NotFound::class, 'Branch not found');

        $branch->update([
            'name' => $data->name,
            'phone' => $data->phone,
            'email' => $data->email,
            'address' => $data->address,
            'branchCode' => $data->branchCode,
            'mode' => $data->mode,
            'topMessage' => $data->topMessage,
            'bottomMessage' => $data->bottomMessage,
            'deviceSerial' => $data->deviceSerial,
            'mrc' => $data->mrc,
            'cluster' => $data->cluster,
            'clusterName' => $data->clusterName,
            'isInitialised' => false
        ]);

        $branchInvoiceNumber = InvoiceSequences::where('branch_id', $branchId)->first();
       
        if(is_null($branchInvoiceNumber)){
            InvoiceSequences::create([
                'branch_id' => $branchId,
                'last_number' => $data->invoiceNumber
            ]);
        }else{
            $branchInvoiceNumber->update([
                'last_number' => $data->invoiceNumber
            ]);
        }

    }
}
