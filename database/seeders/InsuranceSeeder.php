<?php

namespace Database\Seeders;

use App\Models\Insurance;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class InsuranceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {


        $data = [
            [
                'name' => 'RSSB',
                'code' => 'RSSB',
                'rate' => 85,
                'isActive' => true,
                'image' => 'rssb-insurance.webp'
            ],
            [
                'name' => 'MUA Insurance Rwanda',
                'code' => 'Sanlam',
                'rate' => 80,
                'isActive' => true,
                'image' => 'mua-insurance.webp'
            ],
            [
                'name' => 'Radiant Insurance Company',
                'code' => 'Radiant',
                'rate' => 80,
                'isActive' => true,
                'image' => 'radiant-insurance.webp'
            ],
            [
                'name' => 'Eden Care Medical',
                'code' => 'Eden',
                'rate' => 80,
                'isActive' => true,
                'image' => 'edencare-insurance.webp'
            ]
        ];

        Insurance::insert($data);
    }
}
