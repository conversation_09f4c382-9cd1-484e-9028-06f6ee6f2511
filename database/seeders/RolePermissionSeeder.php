<?php

namespace Database\Seeders;

use App\Models\RolePermission as RolesPermission;
use Illuminate\Database\Seeder;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            [
                'name' => 'Admin',
                'description' => 'Company Adminstrator',
                'company_id' => null,
                'permissions' => [
                    [
                        'admin' => ['all']
                    ]
                ]
            ],
            [
                'name' => 'Warehouse Manager',
                'description' => 'Warehouse Manager',
                'company_id' => null,
                'permissions' => [
                    [
                        'warehouse' => ['all']
                    ]
                ]
            ],
            [
                'name' => 'Sales Manager',
                'description' => 'Sales Manager, Waiter and Cashier',
                'company_id' => null,
                'permissions' => [
                    [
                        'warehouse' => ['single']
                    ]
                ]
            ]
        ];

        foreach ($data as $item) {
            RolesPermission::create($item);
        }
    }
}
