<?php

use App\Models\AccountSubscription;
use App\Models\Company;
use App\Models\PaymentHistory;
use App\Models\User;
use App\Models\Role;
use App\Models\RolePermission;
use Laravel\Sanctum\Sanctum;
use Carbon\Carbon;
use Illuminate\Support\Facades\RateLimiter;


beforeEach(function () {
    $this->user = User::factory()->create();
    $this->company = Company::factory()->create(['user_id' => $this->user->id, 'isEBM' => true]);
    $this->permission = RolePermission::factory()->create();
    $this->role = Role::factory()->create([
        'user_id' => $this->user->id,
        'company_id' => $this->company->id,
        'role_permission_id' => $this->permission,
    ]);
    $this->subscription = AccountSubscription::factory()->create([
        'user_id' => $this->user->id,
        'isActive' => false,
    ]);

    Sanctum::actingAs($this->user);

    config(['services.paypack.webhook_secret' => 'test-secret']);

    RateLimiter::clear('webhook:' . request()->ip());
    cache()->flush();
});

it('processes successful webhook callback', function () {
    $payload = [
        'event_id' => '9346978a-40c0-11ed-84d0-dead0b5d6103',
        'event_kind' => 'transaction:processed',
        'created_at' => '2022-09-30T13:05:36.707853Z',
        'data' => [
            'ref' => '598f7582-ab43-4c90-9575-820806ab9107',
            'kind' => 'CASHIN',
            'fee' => 2.3,
            'merchant' => 'XXXXX',
            'client' => '07XXXXXXXX',
            'amount' => 100,
            'provider' => 'mtn',
            'status' => 'successful',
            'created_at' => '2022-09-30T12:53:50.880947395Z',
            'processed_at' => '2022-09-30T13:05:36.706109277Z',
        ],
    ];

    $payment = PaymentHistory::factory()->create([
        'user_id' => $this->user->id,
        'key' => $payload['data']['ref'],
        'amount' => $payload['data']['amount'],
        'currency' => 'RWF',
        'gateway' => 'Paypack',
        'plan' => 'Premium',
        'date' => Carbon::now()->timestamp,
        'status' => 'pending',
    ]);

    $rawBody = json_encode($payload);
    $signature = base64_encode(hash_hmac('sha256', $rawBody, 'test-secret', true));

    $response = $this->withHeaders(['x-paypack-signature' => $signature])
        ->postJson('/zata-callback', $payload);

    $response->assertStatus(200);
    $response->assertJson(['status' => 'success']);
    $this->assertDatabaseHas('PaymentHistory', [
        'id' => $payment->id,
        'status' => 'success',
    ]);
    $this->assertDatabaseHas('AccountSubscription', [
        'user_id' => $this->user->id,
        'isActive' => true,
        'subscriptionType' => 'premium',
        'billingPeriod' => 1,
    ]);

    $subscription = AccountSubscription::where('user_id', $this->user->id)->first();

    expect(cache()->has("webhook_event:{$payload['event_id']}"))->toBeTrue();
});

it('rejects webhook with invalid signature', function () {
    $payload = [
        'event_id' => '9346978a-40c0-11ed-84d0-dead0b5d6103',
        'event_kind' => 'transaction:processed',
        'created_at' => '2022-09-30T13:05:36.707853Z',
        'data' => [
            'ref' => '598f7582-ab43-4c90-9575-820806ab9107',
            'kind' => 'CASHIN',
            'fee' => 2.3,
            'merchant' => 'XXXXX',
            'client' => '07XXXXXXXX',
            'amount' => 100,
            'provider' => 'mtn',
            'status' => 'successful',
            'created_at' => '2022-09-30T12:53:50.880947395Z',
            'processed_at' => '2022-09-30T13:05:36.706109277Z',
        ],
    ];

    $response = $this->withHeaders(['x-paypack-signature' => 'invalid-signature'])
        ->postJson('/zata-callback', $payload);

    $response->assertStatus(403);
    $response->assertJson(['message' => 'Invalid signature']);
});

it('skips duplicate webhook event', function () {
    $eventId = '9346978a-40c0-11ed-84d0-dead0b5d6103';
    cache()->put("webhook_event:$eventId", true, now()->addDays(7));

    $payload = [
        'event_id' => $eventId,
        'event_kind' => 'transaction:processed',
        'created_at' => '2022-09-30T13:05:36.707853Z',
        'data' => [
            'ref' => '598f7582-ab43-4c90-9575-820806ab9107',
            'kind' => 'CASHIN',
            'fee' => 2.3,
            'merchant' => 'XXXXX',
            'client' => '07XXXXXXXX',
            'amount' => 100,
            'provider' => 'mtn',
            'status' => 'successful',
            'created_at' => '2022-09-30T12:53:50.880947395Z',
            'processed_at' => '2022-09-30T13:05:36.706109277Z',
        ],
    ];

    $payment = PaymentHistory::factory()->create([
        'user_id' => $this->user->id,
        'key' => $payload['data']['ref'],
        'status' => 'pending',
    ]);

    $rawBody = json_encode($payload);
    $signature = base64_encode(hash_hmac('sha256', $rawBody, 'test-secret', true));

    $response = $this->withHeaders(['x-paypack-signature' => $signature])
        ->postJson('/zata-callback', $payload);

    $response->assertStatus(200);
    $response->assertJson(['status' => 'success']);
    $this->assertDatabaseHas('PaymentHistory', [
        'id' => $payment->id,
        'status' => 'pending',
    ]);
    $this->assertDatabaseHas('AccountSubscription', [
        'user_id' => $this->user->id,
        'isActive' => false,
    ]);
});

it('rejects webhook with missing ref', function () {
    $payload = [
        'event_id' => '9346978a-40c0-11ed-84d0-dead0b5d6103',
        'event_kind' => 'transaction:processed',
        'created_at' => '2022-09-30T13:05:36.707853Z',
        'data' => [
            'kind' => 'CASHIN',
            'status' => 'successful',
        ],
    ];

    $rawBody = json_encode($payload);
    $signature = base64_encode(hash_hmac('sha256', $rawBody, 'test-secret', true));

    $response = $this->withHeaders(['x-paypack-signature' => $signature])
        ->postJson('/zata-callback', $payload);

    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['data.ref']);
});

it('rejects webhook for rate limit violation', function () {
    $key = 'webhook:127.0.0.1';
    RateLimiter::hit($key, 60);
    for ($i = 0; $i < 100; $i++) {
        RateLimiter::hit($key, 60);
    }

    $payload = [
        'event_id' => '9346978a-40c0-11ed-84d0-dead0b5d6103',
        'event_kind' => 'transaction:processed',
        'created_at' => '2022-09-30T13:05:36.707853Z',
        'data' => [
            'ref' => '598f7582-ab43-4c90-9575-820806ab9107',
            'kind' => 'CASHIN',
            'fee' => 2.3,
            'merchant' => 'XXXXX',
            'client' => '07XXXXXXXX',
            'amount' => 100,
            'provider' => 'mtn',
            'status' => 'successful',
            'created_at' => '2022-09-30T12:53:50.880947395Z',
            'processed_at' => '2022-09-30T13:05:36.706109277Z',
        ],
    ];

    $rawBody = json_encode($payload);
    $signature = base64_encode(hash_hmac('sha256', $rawBody, 'test-secret', true));

    $response = $this->withHeaders(['x-paypack-signature' => $signature])
        ->postJson('/zata-callback', $payload);

    $response->assertStatus(403);
    $response->assertJson(['message' => 'Too many requests']);
});


afterEach(function () {
    cache()->flush();
    RateLimiter::clear('webhook:' . request()->ip());
});