<?php

namespace Database\Seeders;

use App\Models\Tax;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TaxSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $tax = [
            [
                'code' => 'A',
                'rate' => 0,
                'forEBM' => true,
                'name' => 'A',
                'description' => 'VAT exempted product'
            ],
            [
                'code' => 'B', 
                'rate' => 18, 
                'forEBM' => true, 
                'name' => 'B 18%',
                'description' => 'VAT  product'
            ],
            [
                'code' => 'C', 
                'rate' => 0, 
                'forEBM' => true, 
                'name' => 'C',
                'description' => 'Export product'
            ],
            [
                'code' => 'D', 
                'rate' => 0, 
                'forEBM' => true, 
                'name' => 'D',
                'description' => 'Not registered for VAT'
            ],
            [
                'code' => 'No-Tax', 
                'rate' => 0, 
                'forEBM' => false, 
                'name' => 'No-Tax',
                'description' => 'No Tax'
            ],
        ];

        foreach ($tax as $tax) {
            Tax::create($tax);
        }
    }
}
