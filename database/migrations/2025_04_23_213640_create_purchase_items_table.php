<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('PurchaseItems', function (Blueprint $table) {
            $table->id();
            $table->foreignId("purchase_id")->references("id")->on("Purchases")->onUpdate("cascade")->onDelete("restrict");
            $table->string('itemSeq');
            $table->string('itemCd')->default('N/A')->nullable();
            $table->string('itemClsCd');
            $table->string('itemNm');
            $table->string('bcd')->nullable();
            $table->string('pkgUnitCd');
            $table->string('pkg')->default(0);
            $table->string('qtyUnitCd');
            $table->decimal('qty');
            $table->decimal('prc', 14)->default(0);
            $table->decimal('splyAmt',14)->default(0);
            $table->decimal('dcRt', 14)->default(0);
            $table->decimal('dcAmt',14)->default(0);
            $table->string('taxTyCd');
            $table->decimal('taxblAmt',14)->default(0);
            $table->decimal('taxAmt',14,2);
            $table->decimal('totAmt',14)->default(0);
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('PurchaseItems');
    }
};
