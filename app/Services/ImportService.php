<?php

namespace App\Services;

use App\Data\UpdateImportData;
use App\Exceptions\NotFound;
use App\Models\CompanyBranch;
use App\Models\Importation;
use App\Models\Product;
use App\Models\ProductDetail;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use App\Enums\ProductStockStatusEnums;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\ProductStockHistory;
use App\Enums\StockTypeEnums;

class ImportService
{

    public function __construct(
        protected EbmService $ebmService
    ) {}

    public function checkImportation($date, CompanyBranch $branch)
    {
        $importData = $this->ebmService->syncImport($date, $branch);

        foreach ($importData as $data) {
            $exists = Importation::where('dclNo', $data['dclNo'])
                ->where('itemSeq', $data['itemSeq'])
                ->where('branch_id', $branch->id)
                ->exists();

            if (!$exists) {
                Importation::create([
                    'dclNo' => $data['dclNo'],
                    'itemSeq' => $data['itemSeq'],
                    'branch_id' => $branch->id,
                    'taskCd' => $data['taskCd'],
                    'dclDe' => $data['dclDe'],
                    'hsCd' => $data['hsCd'],
                    'itemNm' => $data['itemNm'],
                    'orgnNatCd' => $data['orgnNatCd'],
                    'exptNatCd' => $data['exptNatCd'],
                    'pkg' => $data['pkg'],
                    'pkgUnitCd' => $data['pkgUnitCd'],
                    'qty' => $data['qty'],
                    'qtyUnitCd' => $data['qtyUnitCd'],
                    'totWt' => $data['totWt'],
                    'netWt' => $data['netWt'],
                    'spplrNm' => $data['spplrNm'],
                    'agntNm' => $data['agntNm'],
                    'invcFcurAmt' => $data['invcFcurAmt'],
                    'invcFcurCd' => $data['invcFcurCd'],
                    'invcFcurExcrt' => $data['invcFcurExcrt'],
                    'status' => $data['status'],
                    'created_at' => $data['created_at'],
                    'updated_at' => $data['updated_at'],
                ]);
            }
        }
    }

    public function getImportationById(CompanyBranch $branch, $importationId): Importation
    {

        $import = Importation::where('id',  $importationId)
            ->where('branch_id', $branch->id)
            ->select([
                'id',
                'dclNo',
                'taskCd',
                'hsCd',
                'itemNm',
                'qty',
                'invcFcurAmt',
                'invcFcurCd',
                'invcFcurExcrt',
                'status',
                'spplrNm'
            ])
            ->first();

        throw_if(is_null($import), NotFound::class, 'Import not found');

        return $import;
    }

    public function updateImportation(CompanyBranch $branch, UpdateImportData $data, int $userId, int $importId): void
    {

        $importProduct = Importation::where('id', $importId)
            ->where('branch_id', $branch->id)
            ->first();

        throw_if(!$importProduct, NotFound::class, 'Importation not found');

        $action = $data->status ? 3 : 4;

        $selectedProduct = Product::where('id', $data->productID)
            ->where('branch_id', $branch->id)
            ->with(['productClass'])
            ->first();

        throw_if(is_null($selectedProduct), NotFound::class, 'Importation not found');

        $datax = [
            'tin' => $branch->company->tin,
            'bhfId' => '00',
            'taskCd' => $importProduct->taskCd,
            'dclDe' => $importProduct->dclDe,
            'itemSeq' => (int) $importProduct->itemSeq,
            'hsCd' => $importProduct->hsCd,
            'itemClsCd' => '5059690800', // todo
            'itemCd' => $selectedProduct->itemCode,
            'imptItemSttsCd' => (string) $action,
            'remark' => 'remark',
            'modrNm' => 'Admin',
            'modrId' => 'Admin',
        ];

        $this->ebmService->approveRejectImport($branch, $datax);

        $importProduct->status = $action == 3 ? 'approved' : 'rejected';
        $importProduct->save();

        if ($action == 3) {
        
            $productDetail = ProductDetail::where('product_id', $data->productID)
            ->where('batchNumber', $data->batchNumber)
            ->first();

            try {
                DB::beginTransaction();
    
                if (!is_null($productDetail)) {
                    $productDetail->update(array_filter([
                        'currentStock' => $productDetail->currentStock + $importProduct->qty,
                        'purchasePrice' => $data->purchasePrice ?? 0,
                        'expireDate' => strtotime($data->expireDate),
                        'status' => ProductStockStatusEnums::IN_STOCK->value,
                        'discountRate' => $data->discountRate,
                    ], fn($value) => !is_null($value)));
                } else {
                    ProductDetail::create([
                        'product_id' => $data->productID,
                        'currentStock' => $importProduct->qty,
                        'purchasePrice' => $data->purchasePrice ?? 0,
                        'salesPrice' => $data->salePrice,
                        'discountRate' => $data->discountRate ?? 0,
                        'expireDate' => strtotime($data->expireDate),
                        'status' => ProductStockStatusEnums::IN_STOCK->value,
                        'batchNumber' => $data->batchNumber,
                    ]);
                }
    
                ProductStockHistory::create([
                    'branch_id' => $branch->id,
                    'product_id' => $data->productID,
                    'user_id' => $userId,
                    'quantity' => $importProduct->qty,
                    'oldQuantity' => '0', // to be revised
                    'stockType' => StockTypeEnums::IN->value,
                    'orderType' => 'import',
                    'description' => 'Received Imports',
                    'date' => strtotime(now()),
                    'confirmationDate' => strtotime(now()),
                    'productCode' => $selectedProduct->itemCode,
                    'batchNumber' => $data->batchNumber,
                    'expiryDate' => $data->expireDate,
                    'party_id' => null
                ]);
    
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error($e->getMessage());
            }

        }

        //     $response = Http::post($this->updateImportationUrl, $data);

        //     if ($response->successful() || $response->json('resultCd') == '000' || $response->json('resultMsg') == 'It is succeeded') {
        //         $importProduct->status = $action == 3 ? 'approved' : 'rejected';
        //         $importProduct->save();

        //         if ($action == 3) {
        //             $productDetails = ProductDetail::where('product_id', $selectedProduct->id)->first();
        //             $productDetails->update([
        //                 'current_stock' => $productDetails->current_stock + $importProduct->qty
        //             ]);

        //             $this->stockService->stockMasterSave(StockMasterSaveData::from([
        //                 'tin' => userCompany()->tin,
        //                 'bhfId' => '00',
        //                 'itemCd' => $selectedProduct->item_code,
        //                 'rsdQty' => $selectedProduct->details->current_stock + $importProduct->qty,
        //                 'regrId' => 'Admin',
        //                 'regrNm' => 'Admin',
        //                 'modrNm' => 'Admin',
        //                 'modrId' => 'Admin'
        //             ]));

        //             $stockItem = [
        //                 'itemSeq' => 1,
        //                 'itemCd' =>  $selectedProduct->item_code,
        //                 'itemClsCd' =>  '5059690800',
        //                 'itemNm' => $selectedProduct->name,
        //                 'bcd' => '2',
        //                 'pkgUnitCd' => 'NT',  // $importProduct->pkgUnitCd,
        //                 'pkg' => $importProduct->pkg,
        //                 'qtyUnitCd' => 'U',
        //                 'qty' => $importProduct->qty,
        //                 'itemExprDt' => $importProduct->dclDe,
        //                 'prc' => $importProduct->invcFcurAmt,  //
        //                 'splyAmt' => $importProduct->invcFcurAmt, //
        //                 'totDcAmt' => 0, //
        //                 'taxblAmt' => $importProduct->invcFcurAmt, //
        //                 'taxTyCd' => 'A',
        //                 'taxAmt' => 0, //
        //                 'totAmt' => $importProduct->invcFcurAmt //
        //             ];

        //             $stockdata = StockUpdateData::from([
        //                 'tin' => userCompany()->tin,
        //                 'bhfId' => userCompany()->branch,
        //                 'sarNo' => stockTrack(),
        //                 'orgSarNo' => 0, //
        //                 'regTyCd' => 'M',
        //                 'custTin' => null,
        //                 'custNm' => null,
        //                 'custBhfId' => null,
        //                 'sarTyCd' => '01',
        //                 'ocrnDt' => Carbon::now()->format('Ymd'),
        //                 'totItemCnt' => 1,
        //                 'totTaxblAmt' => $importProduct->invcFcurAmt, //
        //                 'totTaxAmt' => 0, //
        //                 'totAmt' =>  $importProduct->invcFcurAmt, //
        //                 'remark' => null,
        //                 'regrId' => 'Admin',
        //                 'regrNm' => 'Admin',
        //                 'modrNm' => 'Admin',
        //                 'modrId' => 'Admin',
        //                 'itemList' => [$stockItem]
        //             ]);

        //             $this->stockService->updateStock($stockdata);
        //         }
        //     }

        //  Log::info('Importation Responce ', ['response' => $response->json()]);
    }

    public function getImport($branch, $perPage, $page): LengthAwarePaginator
    {

        return  Importation::where('branch_id', $branch->id)
            ->latest()
            ->paginate(perPage: $perPage, page: $page)
            ->through(fn($import)  => [
                'id' => $import->id,
                'name' => $import->itemNm,
                'qty' => $import->qty,
                'qtyUnitCd' => $import->qtyUnitCd,
                'pkgUnitCd' => $import->pkgUnitCd,
                'spplrNm' => $import->spplrNm,

                'dclNo' => $import->dclNo,
                'dclDe' => $import->dclDe,

                'invcFcurAmt' => $import->invcFcurAmt,
                'invcFcurCd' => $import->invcFcurCd,
                'status' => $import->status,
            ]);
    }
}
