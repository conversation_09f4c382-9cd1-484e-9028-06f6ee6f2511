<?php

namespace App\Services;

use App\Models\Role;
use App\Models\RolePermission;
use App\Exceptions\NotFound;

class RoleService
{

    public function createAdminRole(int $userId, int $companyId, $branchId): void
    {

        Role::where('user_id', $userId)->update(['isDefault' => false]);

        Role::create([
            'user_id' => $userId,
            'company_id' => $companyId,
            'isDefault' => true,
            'role_permission_id' => RolePermission::where('name', 'Admin')->first()->id,
            'default_branch_id' => $branchId
        ]);
    }

    public function createRole(int $userId, int $companyId, int $permissionId, int $branchId): void
    {
        Role::create([
            'user_id' => $userId,
            'company_id' => $companyId,
            'role_permission_id' => $permissionId,
            'default_branch_id' => $branchId
        ]);
    }

    public function setDefaultBranch(int  $userId, int $companyId, int $branchId): void
    {

        $role =  Role::where('user_id', $userId)->where('company_id', $companyId)->first();

        throw_if(is_null($role), NotFound::class, 'Role not found');

        $role->default_branch_id = $branchId;
        $role->save();
    }

    public function userDefaultRole(int $userId): Role | null
    {
        return Role::where('user_id', $userId)->where('isDefault', true)->first();
    }

    public  function userRoleByCompanyId(int $userId, int $companyId): Role | null
    {
        return Role::where('user_id', $userId)->where('company_id', $companyId)->first();
    }

    public function userRoles(int $userId): array
    {
        return Role::where('user_id', $userId)->get()->toArray();
    }

    public function getCompanyUsers(int $companyId)
    {
        return Role::where('company_id', $companyId)
            ->with([
                'user',
                'permission',
                'branch'
            ])
            ->get();
    }

    public function getCompanyUser(int $companyId, $userId)
    {
        return Role::where('company_id', $companyId)
            ->where('user_id', $userId)
            ->with([
                'user',
                'permission',
                'branch'
            ])
            ->first();
    }

    public function getCompanyPermissions(int $companyId): array
    {
        return RolePermission::where('name', '!=', 'Admin')
            ->whereCompanyId($companyId)
            ->get()
            ->map(function ($permission) {
                return [
                    'id' => $permission->id,
                    'name' => $permission->name,
                    'description' => $permission->description,
                ];
            })->toArray();
    }
}
