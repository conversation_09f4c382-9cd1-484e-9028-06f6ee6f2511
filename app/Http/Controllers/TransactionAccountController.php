<?php

namespace App\Http\Controllers;

use App\Services\TransactionAccountService;
use Inertia\Inertia;
use App\Enums\AccountTypeEnums;

class TransactionAccountController extends Controller
{

    public function __construct(protected TransactionAccountService $service) {}

    public function account()
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $defaultBranch = branchData();
        $defaultCompany = companyData();
        $response = $this->service->account($defaultBranch->id, $defaultCompany->id);

        return Inertia::render('Transaction/Account', [
            'Accounts' => $response
        ]);
    }

    public function payment()
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $defaultBranch = branchData();
        $defaultCompany = companyData();
        $perPage = \request()->query('perPage', 20);
        $page = \request()->query('page', 1);
        $sourceType = \request()->query('sourceType');
        $startDate = \request()->query('startDate');
        $endDate = \request()->query('endDate');
        $searchQuery = \request()->query('searchQuery');

        $accounts = $this->service->paymentTransaction($defaultBranch->id, $defaultCompany->id, $perPage, $page, $sourceType, $startDate, $endDate);
        $response = $this->pagination($accounts);

        return Inertia::render('Transaction/Payments', [
            'Accounts' => $response['data'],
            'currentPage' => $response['currentPage'],
            'lastPage' => $response['lastPage'],
            'itemsPerPage' => $response['itemsPerPage'],
            'pageItems' => $response['pageItems'],
            'total' => $response['total'],
            'searchQuery' => $searchQuery,
        ]);
    }
}
