<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('Role', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('restrict');
            $table->foreignId('company_id')->constrained('Company')->onDelete('restrict');
            $table->foreignId('role_permission_id')->nullable()->references('id')->on('RolePermission')->onDelete('restrict');
            $table->foreignId('default_branch_id')->nullable()->references('id')->on('CompanyBranch')->onDelete('restrict');
            $table->boolean('isDefault')->default(true);
            $table->boolean('isActive')->default(true);
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();

            $table->unique(['user_id', 'company_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     */
    public function down(): void
    {
        Schema::dropIfExists('Role');
    }
};
