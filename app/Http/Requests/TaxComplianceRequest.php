<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TaxComplianceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'userType' => ['required', Rule::in(['individual', 'company'])],
            'taxType' => ['required', Rule::in(['VAT', 'CIT_PIT'])],
            'timeScope' => ['required', 'string'],

            // Individual Fields
            'totalSales' => ['nullable', 'numeric', 'min:0', Rule::requiredIf($this->input('userType') === 'individual')],
            'taxableSupplies' => ['nullable', 'numeric', 'min:0'],
            'expenses' => ['nullable', 'numeric', 'min:0'],
            'losses' => ['nullable', 'numeric', 'min:0'],
            'inputVat' => ['nullable', 'numeric', 'min:0'],
            
            // Company Fields
            'companyExpenses' => ['nullable', 'numeric', 'min:0', Rule::requiredIf($this->input('userType') === 'company')],
            'companyLosses' => ['nullable', 'numeric', 'min:0', Rule::requiredIf($this->input('userType') === 'company')],

            // Nested Validation for Sales Data (Company)
            'salesData' => ['nullable', 'array', Rule::requiredIf($this->input('userType') === 'company')],
            'salesData.*.category' => ['required_with:salesData', 'string', 'max:100'],
            'salesData.*.amount' => ['required_with:salesData', 'numeric', 'min:0'],

            // Nested Validation for Employees (Company)
            'employees' => ['nullable', 'array', Rule::requiredIf($this->input('userType') === 'company')],
            'employees.*.name' => ['required_with:employees', 'string', 'max:100'],
            'employees.*.salary' => ['required_with:employees', 'numeric', 'min:0'],
        ];
    }
}