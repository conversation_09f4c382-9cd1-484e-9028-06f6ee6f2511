<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
           'name' => 'required|string|max:40',
           'branchProductCategoryID' => 'required|numeric|min:1',
        ];
    }

    public function messages()
    {
        return [
            'branchProductCategoryID.required' => 'Branch Product Category is required',
            
        ];
    }
}
