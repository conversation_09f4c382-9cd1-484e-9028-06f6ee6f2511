<script setup>
import ButtonGroupOption from '@/Components/ButtonGroupOption.vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";
import QrcodeVue from 'qrcode.vue';
import SecondaryLink from '@/Components/SecondaryLink.vue';
import { ref } from 'vue';
import { Button } from '@/components/ui/button';

const props = defineProps({
    Transaction: Object,
    Company: Object,
    Branch: Object,
    Copy: Boolean,
});

const downloading = ref(false);
const downloading2 = ref(false);
const errorMessage = ref('');

function formatSignature(signature) {
    if (!signature) return ''; 
    return signature.match(/.{1,4}/g).join('-');
}

async function downloadFile(routeName, id, fileType) {
    downloading.value = true;
    errorMessage.value = '';

    try {
        const response = await fetch(route(routeName, id), {
            method: 'GET',
            headers: {
                'Accept': 'application/pdf',
            },
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Download failed');
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `zata-point-${fileType}-${new Date().toISOString().replace(/[:.]/g, '-')}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    } catch (error) {
        errorMessage.value = error.message;
    } finally {
        downloading.value = false;
    }
}

async function downloadFile2(routeName, id, fileType) {
    downloading2.value = true;
    errorMessage.value = '';

    try {
        const response = await fetch(route(routeName, id), {
            method: 'GET',
            headers: {
                'Accept': 'application/pdf',
            },
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Download failed');
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `zata-point-${fileType}-${new Date().toISOString().replace(/[:.]/g, '-')}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    } catch (error) {
        errorMessage.value = error.message;
    } finally {
        downloading2.value = false;
    }
}

function formatExpireDate(expireDate) {
    if (!expireDate) return 'N/A';
    return expireDate.split(' ')[0];
}
</script>

<template>
    <AppLayout title="Transaction">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Transaction Details
            </h2>
        </template>

        <div v-if="errorMessage" class="text-red-600 mb-4">
            {{ errorMessage }}
        </div>

        <ButtonGroupOption class="px-8 ">
            <Button  :disabled="downloading" @click="downloadFile('transaction.download', Transaction.id, 'invoice')">
                {{ downloading ? 'Downloading...' : 'Download A4' }}
            </Button>

            <Button  :disabled="downloading" @click="downloadFile('transaction.download.small', Transaction.id, 'invoice')">
                {{ downloading ? 'Downloading...' : 'Download 80mm' }}
            </Button>

              <Button  :disabled="downloading" @click="downloadFile('transaction.download.small58', Transaction.id, 'invoice')">
                {{ downloading ? 'Downloading...' : 'Download 58mm' }}
            </Button>



            <Button :disabled="downloading2" @click="downloadFile2('transaction.download.deliveryNote', Transaction.id, 'delivery-note')"
                v-if="!Transaction.isRefunded && (Transaction.transactionType == 'sales' || Transaction.transactionType == 'proforma')">
                {{ downloading2 ? 'Downloading...' : 'Delivery note' }}
            </Button>

            <SecondaryLink :href="route('transaction.proforma.convert', Transaction.id)"
                v-if="!Transaction.isRefunded && Transaction.type == 'PS'">
                Convert to sale
            </SecondaryLink>

            <Button as-child variant="secondary">
                <Link :href="route('transaction.refundSale', Transaction.id)"
                    v-if="!Transaction.isRefunded && Transaction.transactionType == 'sales'">
                    Refund
                </Link>
            </Button>
        </ButtonGroupOption>

        <section id="print-section" class="justify-center p-4 print-only mx-auto bg-white hidden lg:flex">
            <div class="w-full bg-white rounded mx-auto">
                <div class="border border-gray-400 m-5 p-5 rounded">
                    <section class="flex justify-between">
                        <!-- Invoice Header -->
                        <div class="py-2 border-gray-600 flex justify-start">
                            <div class="">
                                <img src="/ebm_logo_2.png" alt="logo" class="w-44">
                            </div>
                            <div class="py-8">
                                <p class="font-bold text-lg text-left">
                                    {{ Company.name }}
                                </p>
                                <p class="text-left">
                                    <span class="font-bold"> {{ Company.address }} </span>
                                </p>
                                <p aria-colspan="2" class="text-left ">
                                    <span class="font-bold"> TEL: {{ Company.phone }} </span>
                                </p>
                                <p aria-colspan="2" class="text-left hidden ">
                                    <span class="font-bold"> EMAIL : {{ Company.email }} </span>
                                </p>
                                <p class="text-left ">
                                    <span class="font-bold"> TIN : {{ Company.tin }} </span>
                                </p>
                                <p class="text-left py-2" v-if="Copy">
                                    <span class="font-bold"> COPY </span>
                                </p>
                                <p class="text-left py-1">
                                    <span v-if="Transaction.type == 'NS'" class="hidden">SALES</span>
                                    <span v-if="Transaction.type == 'TS'">TRAINING SALES</span>
                                    <span v-if="Transaction.type == 'TR'">TRAINING REFUND</span>
                                    <span v-if="Transaction.type == 'PS'">PROFORMA</span>
                                </p>
                                <div class="text-left font-bold uppercase"
                                    v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">
                                    <span v-if="Transaction.type == 'NR'">REFUND</span> <br>
                                    REF. NORMAL RECEIPT : {{ Transaction.originalInvoiceNumber }} <br>
                                    <hr class="border-dotted border-black" />
                                    <p>REFUND IS APPROVED ONLY FOR ORIGINAL SALES RECEIPT</p>
                                </div>
                            </div>

                        </div>
                        <div>
                            <div class="flex justify-between">
                                <img src="/ebm_logo_1.png" alt="logo" class="w-40">
                            </div>
                        </div>
                    </section>
                    <div v-if="Transaction.party.patient_details != null">
                        <p v-if="Transaction.party.patient_details.hasAffiliation"> Affiliate No: {{
                            Transaction.party.patient_details.affiliationNumber }} </p>
                        <p v-if="Transaction.party.patient_details.hasAffiliation"> Affiliate Name: {{
                            Transaction.party.patient_details.affiliateFirstName }} {{
                                Transaction.party.patient_details.affiliateLastName }} </p>
                        <p v-if="Transaction.party.patient_details.hasAffiliation"> Relationship: {{
                            Transaction.party.patient_details.relationship }} </p>

                        <p> Beneficial No: {{ Transaction.party.patient_details.beneficiaryNumber }}</p>
                        <p> Beneficial Name: {{ Transaction.party.patient_details.beneficiaryFirstName }} {{
                            Transaction.party.patient_details.beneficiaryLastName }} </p>
                        <p> Date of birth: {{ Transaction.party.patient_details.dateOfBirth }} </p>
                        <p> Department: {{ Transaction.party.patient_details.department }} </p>
                        <p v-if="Transaction.prescriptionNumber != null"> Prescription No: {{
                            Transaction.prescriptionNumber }}
                        </p>
                        <p> Insurance: {{ Transaction.party.patient_details.insurance.name }} </p>
                        <p v-if="Transaction.insuranceTin != null"> Insurance TIN: {{ Transaction.insuranceTin }} </p>
                    </div>

                    <div class="">
                        <p class="font-semibold text-center">
                            {{ Branch.topMessage }}
                        </p>
                    </div>

                    <div>
                        <p>
                            <span class="font-bold">
                                Reference :
                            </span>
                            <span>
                                Served by {{ Transaction.user.name }}
                            </span>
                        </p>
                    </div>

                    <!-- Client Info -->
                    <div class="py-2 flex justify-between">
                        <div>
                            <p class="text-left py-3"> INVOICE TO </p>
                            <div class="border border-1 border-gray-500 text-left px-3 py-4 w-80">
                                <p> TIN : {{ Transaction.clientTin }}</p>
                                <p> Name: {{ Transaction.clientName }} </p>
                                <p class="hidden"> Phone : {{ Transaction.clientPhoneNumber }} </p>
                            </div>

                        </div>
                        <div>
                            <p class="py-3 text-white">.</p>
                            <div class="border border-1 border-gray-500 text-left px-3 py-4 w-80">
                                <p> Invoice No : {{ Transaction.invoiceNumber }} </p>
                                <p> Date : {{ Transaction.confirmationDate }}</p>
                            </div>
                        </div>

                    </div>

                    <div class="w-full overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-300" style="min-height: 400px;">
                            <thead class="bg-gray-200">
                                <tr>
                                    <th class="text-left px-4 py-2 border border-gray-300">Item code</th>
                                    <th class="text-left px-4 py-2 border border-gray-300">Item Desc</th>
                                    <th class="text-left px-4 py-2 border border-gray-300">Batch</th>
                                    <th class="text-left px-4 py-2 border border-gray-300">Exp</th>
                                    <th class="text-left px-4 py-2 border border-gray-300">Qty</th>
                                    <th class="text-left px-4 py-2 border border-gray-300">Tax</th>
                                    <th class="text-left px-4 py-2 border border-gray-300">Unit Price</th>
                                    <th class="text-left px-4 py-2 border border-gray-300">Total Price</th>
                                </tr>
                            </thead>
                            <tbody class="h-full">
                                <tr v-for="(item, index) in Transaction.items" :key="index"
                                    :class="{ 'h-full': index === Transaction.items.length - 1 }" class="">
                                    <td class="text-left px-4 py-2 border border-gray-300 content-start text-xs">
                                        {{ item.productCode }}
                                    </td>
                                    <td class="text-left px-4 py-2 border border-gray-300 content-start text-xs">
                                        {{ item.productName }}
                                    </td>
                                    <td class="text-left px-4 py-2 border border-gray-300 content-start text-xs">
                                        {{ item.batchNumber }}
                                    </td>
                                    <td class="text-left px-4 py-2 border border-gray-300 content-start text-xs">
                                        
                                          {{ formatExpireDate(item.expireDate) }}
                                    </td>
                                    <td class="text-left px-4 py-2 border border-gray-300 content-start text-xs">
                                        {{ item.quantity }}
                                    </td>
                                    <td class="text-left px-4 py-2 border border-gray-300 content-start text-xs">
                                        {{ item.taxName }}
                                    </td>
                                    <td class="text-left px-4 py-2 border border-gray-300 content-start text-xs">
                                        <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span>
                                        {{ $zataCurrency(item.price) }}
                                    </td>
                                    <td class="text-left px-4 py-2 border border-gray-300 content-start text-xs">
                                        <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span>
                                        {{ $zataCurrency(item.totalAmount) }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>


                    <div class="border-b border-dotted py-2 border-gray-600"
                        v-if="Transaction.type == 'PS' || Transaction.type == 'TS' || Transaction.type == 'TR' || Copy">
                        <p class="text-center font-black">
                            THIS IS NOT AN OFFICIAL RECEIPT
                        </p>
                    </div>

                    <div class="flex justify-between mt-12">
                        <!-- VSDC Info -->
                        <div>
                            <!-- SDC Information -->
                            <div class="border-b border-dotted py-2 border-gray-600"
                                v-if="Transaction.signature != null && Transaction.signature !== ''">
                                <div class="font-bold text-left">SDC INFORMATION</div>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="text-gray-600">
                                        Date: {{ new
                                            Date(Transaction.signature.vsdcReceiptPublishDate).toLocaleDateString() }}
                                    </div>
                                    <div class="text-gray-600">
                                        Time: {{ new
                                            Date(Transaction.signature.vsdcReceiptPublishDate).toLocaleTimeString() }}
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="font-semibold text-gray-600">SDC ID:</div>
                                    <div class="text-gray-600">
                                        {{ Transaction.signature.sdcId }}
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="font-semibold text-gray-600">RECEIPT NUMBER:</div>
                                    <div class="text-gray-600">
                                        {{ Transaction.signature.receiptNumber }} / {{
                                            Transaction.signature.totalReceiptNumber
                                        }} &nbsp;
                                        &nbsp;
                                        <!-- <span v-if="Copy">
                                    <span v-if="Order.type == 'NS'"> CS</span>
                                    <span v-if="Order.type == 'NR'"> CR</span>
                                </span>
                                <span v-else>
                                    {{ Order.type }}
                                </span> -->
                                        <span>
                                            {{ Transaction.type }}
                                        </span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center mt-2">
                                    <div class="font-semibold text-gray-600">Internal Data:</div>
                                    <div class="text-gray-600 text-sm">
                                        <span
                                            v-if="Transaction.signature.internalData && Transaction.signature.internalData != null">
                                            {{ formatSignature(Transaction.signature.internalData) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="font-semibold text-gray-600">Receipt Signature:</div>
                                    <div class="text-gray-600 text-sm">
                                        <span
                                            v-if="Transaction.signature.receiptSignature && Transaction.signature.receiptSignature != null">
                                            {{ formatSignature(Transaction.signature.receiptSignature) }}
                                        </span>
                                    </div>
                                </div>

                            </div>
                            <div class="border-b border-dotted py-2 border-gray-600"
                                v-if="Transaction.signature != null">
                                <div class="flex justify-between items-center mt-2">
                                    <div class="text-gray-600">RECEIPT NUMBER</div>
                                    <div class="text-gray-600"> {{ Transaction.signature.invoiceNumber }}</div>
                                </div>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="text-gray-600">
                                        Date: {{ new
                                            Date(Transaction.signature.vsdcReceiptPublishDate).toLocaleDateString() }}
                                    </div>
                                    <div class="text-gray-600">
                                        Time: {{ new
                                            Date(Transaction.signature.vsdcReceiptPublishDate).toLocaleTimeString() }}
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="text-gray-600">MRC:</div>
                                    <div class="text-gray-600">
                                        {{ Branch.mrc }}
                                    </div>
                                </div>

                            </div>

                            <!-- Thank You Message -->
                            <div class="py-1">
                                <p class="text-left"> Version V1.0.0 Powered by RRA_VSDC </p>
                            </div>

                        </div>

                        <!-- QR Code -->
                        <div>
                            <div class="flex justify-center p-1 mt-2"
                                v-if="Transaction.salesTypeCode !== 'P' && Transaction.signature != null">
                                <qrcode-vue
                                    v-if="Transaction.signature.internalData != null && Transaction.signature.receiptSignature != null"
                                    :value="`#${new Date(Transaction.signature.vsdcReceiptPublishDate).toLocaleDateString()} #${new Date(Transaction.signature.vsdcReceiptPublishDate).toLocaleTimeString()} #${Transaction.signature.sdcId} #${Transaction.signature.receiptNumber} / ${Transaction.signature.totalReceiptNumber} ${Transaction.type} #${formatSignature(Transaction.signature.internalData)} #${formatSignature(Transaction.signature.receiptSignature)}`"
                                    :size="130" level="H" />
                            </div>
                            <div class="font-semibold text-center max-w-lg mt-3">{{ Branch.bottomMessage }}</div>
                        </div>

                        <!-- Totals -->
                        <div>
                            <div class="border-b border-dotted py-2 border-gray-600 w-64">
                                <div class="flex justify-between items-center">
                                    <div class="font-bold">TOTAL</div>
                                    <div class="font-semibold">
                                        <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span>
                                        {{ $zataCurrency(Transaction.totAmt) }}
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-2" v-if="Transaction.taxblAmtA > 0">
                                    <div class="">TOTAL A-EX</div>
                                    <div class="text-gray-600">
                                        <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span>
                                        {{ $zataCurrency(Transaction.taxblAmtA) }}
                                    </div>
                                </div>

                                <div class="flex justify-between items-center mt-2" v-if="Transaction.taxAmtB > 0">
                                    <div class="">TOTAL B-18%</div>
                                    <div class="text-gray-600">
                                        <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span>
                                        {{ $zataCurrency(Transaction.taxblAmtB) }}
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-2"
                                    v-if="Transaction.taxAmtB > 0 || Transaction.taxAmtB < 0">
                                    <div class="">TOTAL TAX B</div>
                                    <div class="text-gray-600">
                                        <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span>
                                        {{ $zataCurrency(Transaction.taxAmtB) }}
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-2" v-if="Transaction.taxblAmtC > 0">
                                    <div class="">TOTAL C</div>
                                    <div class="text-gray-600">
                                        <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span>
                                        {{ $zataCurrency(Transaction.taxblAmtC) }}
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-2" v-if="Transaction.taxblAmtD > 0">
                                    <div class="">TOTAL D</div>
                                    <div class="text-gray-600">
                                        <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span>
                                        {{ $zataCurrency(Transaction.taxblAmtD) }}
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="">TOTAL TAX</div>
                                    <div class="text-gray-600">
                                        <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span>
                                        {{ $zataCurrency(Transaction.totTaxAmt) }}
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 border p-4" v-if="Transaction.party.patient_details != null">
                                <p> 
                                    <span class="font-bold"> Insurance</span> : <span> {{ Transaction.party.patient_details.percentage }} % </span>  </p>
                                <p> <span class="font-bold"> Insurance Discount</span> : {{ $zataCurrency(Transaction.totAmt * Transaction.party.patient_details.percentage / 100) }} </p>
                             
                                <p> <span class="font-bold"> Amount to be paid</span> : {{ $zataCurrency(Transaction.totAmt - Transaction.totAmt * Transaction.party.patient_details.percentage / 100) }} </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <template #footer />
    </AppLayout>
</template>
