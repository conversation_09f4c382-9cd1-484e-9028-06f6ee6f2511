<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {

        $this->call([
            UserSeeder::class,
            ProductTypeSeeder::class,
            ProductCategorySeeder::class,
            ProductClassSeeder::class,
            PaymentModeSeeder::class,
            ProductPackagingUnitSeeder::class,
            ProductQuantityUnitSeeder::class,
            TaxSeeder::class,
            CountrySeeder::class,
            ExpenseCategorySeeder::class,
            BranchCategorySeeder::class,
            CompanySeeder::class,
            CompanyBranchSeeder::class,
            PartyTypeSeeder::class,
            CompanyPartySeeder::class,
            RolePermissionSeeder::class,
            RoleSeeder::class,
            BranchProductCategorySeeder::class,
            InsuranceSeeder::class,
            CompanyInsuranceSeeder::class,
            ProductSeeder::class,
            AffiliateSeeder::class,
            CompanySubscriptionSeeder::class
        ]);

    }
}
