<?php

namespace App\Http\Controllers;

use App\Services\DpoPaymentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class DpoController extends Controller
{

    public function __construct(protected DpoPaymentService $dpoService) {}

    /**
     * Create a new payment transaction
     */
    public function createPayment(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'required|string|size:3',
            'company_ref' => 'required|string|max:50',
            'redirect_url' => 'nullable|url',
            'back_url' => 'nullable|url',
            'service_type' => 'required|integer',
            'service_description' => 'required|string',
            'service_date' => 'nullable|date_format:Y/m/d H:i',
        ]);

        try {
            $transaction = $this->dpoService::formatTransactionData(
                $validated['amount'],
                $validated['currency'],
                $validated['company_ref'],
                $validated['redirect_url'] ?? null,
                $validated['back_url'] ?? null
            );

            $services = [
                $this->dpoService::formatServiceData(
                    $validated['service_type'],
                    $validated['service_description'],
                    $validated['service_date'] ?? null
                )
            ];

            $result = $this->dpoService->createToken($transaction, $services);

            if ($this->dpoService->isTransactionSuccessful($result)) {
                return response()->json([
                    'success' => true,
                    'token' => $result['TransToken'] ?? null,
                    'reference' => $result['TransRef'] ?? null,
                    'payment_url' => "https://secure.3gdirectpay.com/payv2.php?ID=" . ($result['TransToken'] ?? '')
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => $this->dpoService->getErrorMessage($result)
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify payment status
     */
    public function verifyPayment(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'transaction_token' => 'required_without:company_ref|string',
            'company_ref' => 'required_without:transaction_token|string',
        ]);

        try {
            $result = $this->dpoService->verifyToken(
                $validated['transaction_token'] ?? null,
                $validated['company_ref'] ?? null
            );

            $isPaid = in_array($result['Result'], ['000', '001']);

            return response()->json([
                'success' => true,
                'paid' => $isPaid,
                'result_code' => $result['Result'],
                'result_explanation' => $result['ResultExplanation'] ?? '',
                'customer_name' => $result['CustomerName'] ?? null,
                'transaction_amount' => $result['TransactionAmount'] ?? null,
                'transaction_currency' => $result['TransactionCurrency'] ?? null,
                'fraud_alert' => $result['FraudAlert'] ?? null,
                'customer_phone' => $result['CustomerPhone'] ?? null,
                'customer_country' => $result['CustomerCountry'] ?? null,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process mobile payment
     */
    public function processMobilePayment(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'transaction_token' => 'required|string',
            'phone_number' => 'required|string',
            'mno' => 'required|string',
            'mno_country' => 'required|string',
        ]);

        try {
            $result = $this->dpoService->chargeTokenMobile(
                $validated['transaction_token'],
                $validated['phone_number'],
                $validated['mno'],
                $validated['mno_country']
            );

            return response()->json([
                'success' => true,
                'status_code' => $result['StatusCode'] ?? null,
                'instructions' => $result['instructions'] ?? null,
                'redirect_required' => ($result['RedirectOption'] ?? 0) == 1
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get mobile payment options
     */
    public function getMobileOptions(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'transaction_token' => 'required|string',
        ]);

        try {
            $result = $this->dpoService->getMobilePaymentOptions($validated['transaction_token']);

            return response()->json([
                'success' => true,
                'payment_options' => $result['paymentoptions'] ?? []
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process refund
     */
    public function processRefund(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'transaction_token' => 'required|string',
            'refund_amount' => 'required|numeric|min:0.01',
            'refund_details' => 'required|string',
            'refund_ref' => 'nullable|string',
            'refund_approval' => 'nullable|boolean'
        ]);

        try {
            $result = $this->dpoService->refundToken(
                $validated['transaction_token'],
                $validated['refund_amount'],
                $validated['refund_details'],
                $validated['refund_ref'] ?? null,
                $validated['refund_approval'] ?? false
            );

            return response()->json([
                'success' => $this->dpoService->isTransactionSuccessful($result),
                'result' => $result['ResultExplanation'] ?? 'Refund processed'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get company balance
     */
    public function getBalance(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'currency' => 'required|string|size:3',
        ]);

        try {
            $result = $this->dpoService->getBalance($validated['currency']);

            return response()->json([
                'success' => true,
                'balance' => $result['CompanyBalance'] ?? 0,
                'exchange_rate' => $result['ExchangeRate'] ?? 1,
                'currency' => $validated['currency']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
