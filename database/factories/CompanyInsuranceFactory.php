<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\Insurance;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CompanyInsurance>
 */
class CompanyInsuranceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'code' => $this->faker->word,
            'company_id' => Company::factory(),
            'insurance_id' => Insurance::factory(),
        ];
    }
}
