<script setup>
defineProps({
    modelValue: {
        type: [String, Number, null],
        required: true,
    },
    options: {
        type: Array,
        required: true,
    },
    name: {
        type: String,
        required: true,
    },
    label: {
        type: String,
        required: true,
    },
});

const emit = defineEmits(['update:modelValue']);
</script>

<template>
    <div>

        <label  :for="name" class="block text-sm font-medium text-black mb-2">
            {{ label }}
        </label>

        <div class="grid grid-cols-2 mb-2 w-full gap-2">
            <div v-for="option in options" :key="option.value"
                class="flex flex-col items-start pl-4 rounded border border-gray-300 w-full p-2">
                <div class="flex items-center w-full">
                    <input :id="option.value" :value="option.value" :name="name" type="radio"
                        :checked="modelValue === option.value"
                        @change="$emit('update:modelValue', option.value)"
                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2" />
                    <label :for="option.value" class="ml-2 text-sm font-medium text-black">
                        {{ option.label }}
                    </label>
                </div>
                <p v-if="option.description" class="text-xs text-gray-500 ml-6">
                    {{ option.description }}
                </p>
            </div>
        </div>
    </div>
</template>
