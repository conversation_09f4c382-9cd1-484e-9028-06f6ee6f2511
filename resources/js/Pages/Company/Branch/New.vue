<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from "@inertiajs/vue3";
import FormSection from '@/Components/FormSection.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import SelectOption from '@/Components/SelectOption.vue';

const props = defineProps({
    BranchCategories: Array,
});

const form = useForm({
    name: null,
    email: null,
    phone: null,
    address: null,
    branchCategoryID: null,

});

function createCompanyBranch() {
    form.post(route('branch.store'));
}
</script>

<template>
    <AppLayout title="Create new branch ">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Create new branch
            </h2>
        </template>
        <FormSection>
            <template #title>
                Create new branch
            </template>

            <template #form>
                <form class="" @submit.prevent="createCompanyBranch">
                    <div class="pb-4">
                        <InputLabel for="name" value="Name" />
                        <TextInput id="name" v-model="form.name" type="text" />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>
                    <div class="grid grid-cols-6 gap-6 pb-4">

                        <div class="col-span-6 sm:col-span-3">

                            <InputLabel for="email" value="Email" />
                            <TextInput id="email" v-model="form.email" type="email" />
                            <InputError class="mt-2" :message="form.errors.email" />
                        </div>
                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="phone" value="Phone" />
                            <TextInput id="phone" v-model="form.phone" type="text" />
                            <InputError class="mt-2" :message="form.errors.phone" />
                        </div>
                    </div>
                    <div class="col-span-6 sm:col-span-3 pb-4">
                        <InputLabel for="company_address" value="Address" />
                        <TextInput id="company_address" v-model="form.address" type="text" />
                        <InputError class="mt-2" :message="form.errors.address" />
                    </div>
                    <div class="col-span-6 sm:col-span-3">

                        <SelectOption id="branchCategoryID" v-model="form.branchCategoryID" :options="BranchCategories"
                            label="Branch Category" placeholder="Select a category" valueKey="id" labelKey="name" />
                        <InputError class="mt-2" :message="form.errors.branchCategoryID" />
                    </div>
                    <div class="flex justify-end mt-4">
                        <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                            Save
                        </PrimaryButton>
                    </div>
                </form>
            </template>
        </FormSection>

        <template #footer />
    </AppLayout>
</template>