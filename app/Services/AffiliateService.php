<?php

namespace App\Services;

use App\Models\Affiliate;

class AffiliateService
{


    public function storeAffiliate(int $userId, $data): void
    {

        $code = $data->name . $userId;
        $data->affiliateCode = substr($code, 0, 10);

        Affiliate::create([
            'name' => $data->name,
            'description' => $data->description,
            'isActive' => $data->isActive,
            'account_id' => $userId,
            'affiliateCode' => $data->affiliateCode
        ]);
    }

    public function getAffiliateByUserId(int $userId): Affiliate
    {
        return  Affiliate::where('account_id', $userId)
            ->select('id', 'name', 'description', 'affiliateCode')
            ->first();
    }

    public function createAffiliateCode(int $userId){
        do {
            $code = 'hiq-' . mt_rand(1000, 9999);
            $exists = Affiliate::where('affiliateCode', $code)->exists();
        } while ($exists);

        Affiliate::create([
            'account_id' => $userId,
            'affiliateCode' => $code,
            'isActive' => true,
            'name' => 'Affiliate Code',
            'description' => 'Account Affiliate code'
        ]);
    }
}
