<?php

namespace Database\Factories;

use App\Models\CompanyParty;
use App\Models\Insurance;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PatientDetails>
 */
class PatientDetailsFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'party_id' => CompanyParty::factory(),
            'insurance_id' => Insurance::factory(),
            'code' => $this->faker->word,
            'affiliationNumber' => $this->faker->word,
            'affiliateLastName' => $this->faker->word,
            'affiliateFirstName' => $this->faker->word,
            'relationship' => $this->faker->word,
            'beneficiaryLastName' => $this->faker->word,
            'beneficiaryFirstName' => $this->faker->word,
            'dateOfBirth' => $this->faker->date(),
            'gender' => $this->faker->randomElement(['Male', 'Female', 'Other']),
            'affiliationLocation' => $this->faker->word,
            'beneficiaryNumber' => $this->faker->word,
            'percentage' => $this->faker->randomNumber(),
            'expirationDate' => $this->faker->date(),
            'department' => $this->faker->word,
            'phoneNumber' => $this->faker->word,
            'status' => $this->faker->randomElement(['Activated', 'Deactivated']),
            'pin' => $this->faker->word,
            'globalInezaId' => $this->faker->word,
        ];
    }
}
