{"private": true, "type": "module", "scripts": {"build": "vite build && vite build --ssr", "dev": "vite"}, "devDependencies": {"@inertiajs/vue3": "^2.0.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@vitejs/plugin-vue": "^5.0.0", "@vue/server-renderer": "^3.3.13", "autoprefixer": "^10.4.16", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "vite": "^5.0", "vue": "^3.3.13"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.4.0", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^13.3.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lodash": "^4.17.21", "lucide-vue-next": "^0.479.0", "qrcode.vue": "^3.3.3", "reka-ui": "^2.3.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vee-validate": "^4.15.0", "vue-chartjs": "^5.3.2", "zod": "^3.24.2"}}