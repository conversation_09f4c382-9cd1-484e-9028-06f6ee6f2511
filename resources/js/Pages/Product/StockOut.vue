<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { computed } from 'vue';
import { useForm } from '@inertiajs/vue3';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  RadioGroup,
  RadioGroupItem,
} from '@/components/ui/radio-group';

const props = defineProps({
  Product: Object,
});

const form = useForm({
  name: props.Product.name,
  quantity: 0,
  description: 'Expired',
  movingUnit: 'sub',
  soldInSubUnit: props.Product.soldInSubUnit,
  batchNumber: '',
});

function updateProduct() {
  form.put(route('product.stockOutStore', props.Product.id), {});
}

const options = computed(() => [
  { value: 'sub', label: `Sub Units / ${props.Product.quantityUnit}` },
  { value: 'main', label: `Main Units / ${props.Product.packagingUnit}` },
]);

const batchOptions = computed(() =>
  props.Product.productDetails.map(detail => ({
    value: detail.batchNumber,
    label: `Batch: ${detail.batchNumber} | Expire: ${detail.expireDate} | Quantity: ${detail.currentStock}`,
  }))
);

const reasons = [
  { value: 'Expired', label: 'Expired' },
  { value: 'Damaged', label: 'Damaged' },
  { value: 'Lost', label: 'Lost' },
  { value: 'Other', label: 'Other' },
];
</script>

<template>
  <AppLayout title="Stock Out">
    <template #header>
      <h2 class="font-semibold text-2xl text-gray-900 leading-tight">
        Stock Out Product
      </h2>
    </template>

    <div class="py-6 px-4 sm:px-6 lg:px-8">
      <Card class="shadow-lg border border-gray-200 max-w-3xl mx-auto">
        <CardHeader class="bg-gray-50 border-b">
          <CardTitle class="text-xl font-semibold text-gray-800">
            Stock Out for <span class="font-bold">{{ Product.name }}</span>
          </CardTitle>
        </CardHeader>
        <CardContent class="p-6">
          <form @submit.prevent="updateProduct" class="space-y-6">
            <div class="space-y-2">
              <Label for="quantity">Quantity</Label>
              <Input id="quantity" v-model="form.quantity" type="number" />
              <p v-if="form.errors.quantity" class="text-red-500 text-sm mt-1">{{ form.errors.quantity }}</p>
            </div>

            <div v-if="form.soldInSubUnit" class="space-y-2">
              <Label>Load Unit</Label>
              <RadioGroup v-model="form.movingUnit" class="flex space-x-4">
                <div v-for="option in options" :key="option.value" class="flex items-center space-x-2">
                  <RadioGroupItem :value="option.value" :id="`moving-unit-${option.value}`" />
                  <Label :for="`moving-unit-${option.value}`">{{ option.label }}</Label>
                </div>
              </RadioGroup>
              <p v-if="form.errors.movingUnit" class="text-red-500 text-sm mt-1">{{ form.errors.movingUnit }}</p>
            </div>

            <div class="space-y-2">
              <Label>Select Batch</Label>
              <Select v-model="form.batchNumber">
                <SelectTrigger>
                  <SelectValue placeholder="Select batch" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="option in batchOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
              <p v-if="form.errors.batchNumber" class="text-red-500 text-sm mt-1">{{ form.errors.batchNumber }}</p>
            </div>

            <div class="space-y-2">
              <Label>Reason</Label>
              <RadioGroup v-model="form.description" class="flex flex-wrap gap-4">
                <div v-for="reason in reasons" :key="reason.value" class="flex items-center space-x-2">
                  <RadioGroupItem :value="reason.value" :id="`reason-${reason.value}`" />
                  <Label :for="`reason-${reason.value}`">{{ reason.label }}</Label>
                </div>
              </RadioGroup>
              <p v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</p>
            </div>

            <div class="flex justify-end gap-4">
              <Button 
                type="submit" 
                :disabled="form.processing"
                :class="{ 'opacity-25': form.processing }"
              >
                Stock Out
              </Button>
            </div>

            <p v-if="form.recentlySuccessful" class="text-green-600 text-sm">
              Successfully updated.
            </p>
          </form>
        </CardContent>
      </Card>

      <div class="flex justify-center mt-6">
        <p class="text-center text-gray-500 text-lg">
          Current stock: <span class="font-bold">{{ Product.totalStock }}</span>
        </p>
      </div>
    </div>

    <template #sidenav />
    <template #footer />
  </AppLayout>
</template>