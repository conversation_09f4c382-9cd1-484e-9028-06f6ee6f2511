<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('PaymentMode', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->nullable()->references('id')->on('Company')->onDelete('restrict');
            $table->string('name');
            $table->string('description');
            $table->string('code');
            $table->boolean('forEbm')->default(false);
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();

            $table->unique(['code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('PaymentMode');
    }
};
