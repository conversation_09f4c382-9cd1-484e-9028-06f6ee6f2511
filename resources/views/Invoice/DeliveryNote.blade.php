<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Zata Point Delivery Note</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <style>
        @media print {
            .no-print { display: none; }
            .print-only { display: block; }
        }
    </style>
</head>
<body class="font-sans antialiased">
    <section id="print-section" class="flex justify-center p-4 print-only mx-auto bg-white text-xs">
        <div class="w-full max-w-4xl bg-white rounded mx-auto">
            <div class="border border-gray-400 m-5 p-5 rounded">
                <!-- Centered Logo and Top Message -->
                <div class="text-center mb-6">
                    <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('zata_logo.png'))) }}"
                         alt="Zata Logo" class="w-44 mx-auto">
                    <p class="mt-4 text-gray-600">{{ $branch->topMessage }}</p>
                </div>

                <!-- Client and Delivery Info -->
                <div class="flex justify-between mb-6">
                    <div>
                        <p class="font-semibold mb-2">DELIVER TO</p>
                        <div class="border border-gray-500 p-3 w-80">
                            <p><span class="font-bold">TIN:</span> {{ $transaction->clientTin }}</p>
                            <p><span class="font-bold">Name:</span> {{ $transaction->clientName }}</p>
                            <p class="hidden"><span class="font-bold">Phone:</span> {{ $transaction->clientPhoneNumber }}</p>
                        </div>
                    </div>
                    <div>
                        <p class="font-semibold mb-2">DELIVERY DETAILS</p>
                        <div class="border border-gray-500 p-3 w-80">
                            <p><span class="font-bold">Delivery Note No:</span> {{ $transaction->id }}</p>
                            <p><span class="font-bold">Date:</span> {{ $transaction->salesDate }}</p>
                            <p><span class="font-bold">Served by:</span> {{ $transaction->user->name }}</p>
                        </div>
                    </div>
                </div>

                <!-- Items Table -->
                <div class="w-full mb-6">
                    <table class="min-w-full border border-gray-300">
                        <thead class="bg-gray-200">
                            <tr>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/7 text-xs">Item Code</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-2/7 text-xs">Description</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/7 text-xs">Batch</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/7 text-xs">Expiry</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/7 text-xs">Quantity</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/7 text-xs">Total Price</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($transaction->items as $item)
                                <tr>
                                    <td class="px-2 py-2 border border-gray-300 text-xs">{{ $item->productCode }}</td>
                                    <td class="px-2 py-2 border border-gray-300 truncate max-w-xs text-xs">{{ $item->productName }}</td>
                                    <td class="px-2 py-2 border border-gray-300 text-xs">{{ $item->batchNumber }}</td>
                                    <td class="px-2 py-2 border border-gray-300 text-xs">{{ $item->expireDate }}</td>
                                    <td class="px-2 py-2 border border-gray-300 text-xs">{{ $item->quantity }}</td>
                                    <td class="px-2 py-2 border border-gray-300 text-xs">{{ $item->totalAmount }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Footer Section -->
                <div class="flex justify-between mt-8">
                    <div>
                        <p class="font-semibold text-center">{{ $branch->bottomMessage }}</p>
                    </div>
                    <div class="w-64">
                        <div class="flex justify-between">
                            <span class="font-bold">Total Items:</span>
                            <span>{{ $transaction->items->count() }}</span>
                        </div>
                    </div>
                </div>

                <!-- Signature Section -->
                <div class="mt-12 flex justify-between">
                    <div>
                        <p class="font-semibold">Prepared By:</p>
                        <p class="mt-8 border-t border-gray-600 w-64 text-center">Signature & Date</p>
                    </div>
                    <div>
                        <p class="font-semibold">Verified By:</p>
                        <p class="mt-8 border-t border-gray-600 w-64 text-center">Signature & Date</p>
                    </div>

                    <div>
                        <p class="font-semibold">Received By:</p>
                        <p class="mt-8 border-t border-gray-600 w-64 text-center">Signature & Date</p>
                    </div>
                </div>

                <div>
                        <p class="text-center mt-12">Version V1.0.0 Powered by RRA_VSDC</p>
                    </div>
            </div>
        </div>
    </section>
</body>
</html>