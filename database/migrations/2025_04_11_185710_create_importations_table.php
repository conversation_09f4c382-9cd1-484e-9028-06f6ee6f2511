<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('Importation', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->references('id')->on('CompanyBranch')->onDelete('restrict');
            $table->string('taskCd')->nullable(); // Task Code CHAR 50
            $table->string('dclDe')->nullable(); // Declaration Date CHAR 8
            $table->decimal('itemSeq' , 14,2)->nullable(); // Item Sequence NUMBER 10
            $table->string('dclNo')->nullable(); // Declaration Number CHAR 50
            $table->string('hsCd')->nullable(); // HS Code CHAR 17
            $table->longText('itemNm')->nullable(); // Item Name CHAR 500
            $table->string('orgnNatCd')->nullable(); // Import Item Status Code CHAR 5 See ‘4.4. Nation’
            $table->string('exptNatCd')->nullable(); // Origin Nation Code CHAR 5
            $table->decimal('pkg' , 14,2)->nullable(); //  Export Nation Code NUMBER 13,2
            $table->string('pkgUnitCd')->nullable(); // Package CHAR 5 See ‘4.5. Packaging Unit’
            $table->decimal('qty' , 14,2)->nullable(); // Packaging Unit Code NUMBER 13,2
            $table->string('qtyUnitCd')->nullable(); // Quantity CHAR 5 See ‘4.6. Unit of Quantity’
            $table->decimal('totWt' , 14,2)->nullable(); // Quantity Unit Code NUMBER 13,2
            $table->decimal('netWt' , 14,2)->nullable(); // Gross Weight NUMBER 13,2
            $table->string('spplrNm')->nullable(); // Net Weight CHAR 500
            $table->longText('agntNm')->nullable(); // Supplier's name CHAR 500
            $table->decimal('invcFcurAmt' , 14,2)->nullable(); // Agent name NUMBER 18,2 See ‘4.1. Tax Type’
            $table->string('invcFcurCd')->nullable(); // Invoice Foreign Currency Amount CHAR 5 See ‘4.7. Currency’
            $table->decimal('invcFcurExcrt' , 14,2)->nullable(); // Invoice Foreign Currency NUMBER 18,2
            $table->string('status')->default('pending')->nullable();
            $table->string('item_code')->nullable();
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('Importation');
    }
};
