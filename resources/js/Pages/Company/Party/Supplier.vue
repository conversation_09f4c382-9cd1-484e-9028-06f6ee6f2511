<script setup>
import PrimaryLink from '@/Components/PrimaryLink.vue';
import TableSection from '@/Components/TableSection.vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";
import { ref, computed } from "vue";

const props = defineProps({
    Parties: Array,
});

const tableHeaders = ["No", "Image", "Name", "Email", "Phone", "Address"];

const tableRows = computed(() => {
    return props.Parties.map((party) => ({
        id: party.id,
        image: party.image,
        name: party.name,
        email: party.email,
        phone: party.phone,
        address: party.address,
    }));
});
</script>

<template>
    <AppLayout title="Suppliers">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Company Suppliers
            </h2>
        </template>
        <div>
            <TableSection :headers="tableHeaders" :rows="tableRows" title="Suppliers"
                notFoundMessage="No suppliers found." :is-dynamic="true" :dynamic-url="'party.show'">
                <template #actions>
                    <PrimaryLink :href="route('supplier.new')">Add new</PrimaryLink>
                </template>
            </TableSection>

        </div>
        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>
