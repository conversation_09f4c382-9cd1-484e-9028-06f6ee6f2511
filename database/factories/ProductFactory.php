<?php

namespace Database\Factories;

use App\Models\CompanyBranch;
use App\Models\Country;
use App\Models\ProductCategory;
use App\Models\ProductClass;
use App\Models\ProductPackingUnit;
use App\Models\ProductQuantityUnit;
use App\Models\ProductType;
use App\Models\Tax;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'category_id' => ProductCategory::factory(),
            'quantity_unit_id' => ProductQuantityUnit::factory(),
            'packaging_unit_id' => ProductPackingUnit::factory(),
            'tax_id' => Tax::factory(),
            'type_id' => ProductType::factory(),
            'country_id' => Country::factory(),
            'class_id' => ProductClass::factory(),
            'branch_id' => CompanyBranch::factory(),
            'name' => $this->faker->name,
            'slug' => $this->faker->name,
            'itemCode' => $this->faker->name,
            'conversionFactor' => 0,
            'description' => $this->faker->text,
            'description_two' => $this->faker->text,
            'description_three' => $this->faker->text,
        ];
    }
}
