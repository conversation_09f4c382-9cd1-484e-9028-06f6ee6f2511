<?php

namespace App\Services;

use App\Models\CompanyInsurance;
use App\Models\Insurance;

class InsuranceService
{
    public function getInsurance(): array
    {
        return Insurance::where('isActive', true)
            ->get()
            ->map(function ($insurance) {
                $insurance->image = url($insurance->image);
                return $insurance;
            })->toArray();
    }

    public function supportedInsurance(int $companyId): array
    {
        return  CompanyInsurance::where('company_id', $companyId)
            ->with('details')
            ->get()
            ->map(function ($insurance) {
                return [
                    'id' => $insurance->details->id,
                    'name' => $insurance->details->name,
                    'code' => $insurance->code,
                    'rate' => $insurance->details->rate,
                    'image' => url($insurance->details->image),
                ];
            })->toArray();
    }
}
