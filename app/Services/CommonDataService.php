<?php

namespace App\Services;

use App\Models\BranchProductCategory;
use App\Models\CompanyBranch;
use App\Models\ProductType;
use App\Models\PaymentMode;
use App\Models\ProductCategory;
use App\Models\ProductClass;
use App\Models\ProductQuantityUnit;
use App\Models\ProductPackingUnit;
use App\Models\Tax;
use App\Models\Country;

class CommonDataService
{

    public function getBranchProductCategory(CompanyBranch $branch): array
    {
        return BranchProductCategory::select('id', 'name', 'slug', 'image', 'isCommon')
            ->whereCompanyId($branch->company_id)
            ->where('branch_category_id', $branch->branch_category_id)
            ->get()
            ->toArray();
    }

    public function getProductType(): array
    {

        return ProductType::select('id', 'name', 'description')
            ->get()
            ->toArray();
    }

    public function getPaymentMode(int $companyId, bool $forEbm = false): array
    {
        return PaymentMode::select('id', 'name', 'description', 'code', 'forEbm', 'company_id')
            ->when($forEbm, fn($query) => $query->where('forEbm', true))
            ->whereCompanyId($companyId)
            ->get()
            ->map(fn($payment) => [
                'id' => $payment->id,
                'name' => $payment->name,
                'description' => $payment->description,
                'code' => $payment->code,
            ])->toArray();
    }

    public function getProductCategory(): array
    {
        return ProductCategory::select('id', 'name')
            ->get()
            ->toArray();
    }

    public function getProductClass(): array
    {
        return ProductClass::select('id', 'name', 'code')
        ->limit(100)
            ->get()
         
            ->toArray();
    }

    public function getProductQuantityUnit(): array
    {
        return ProductQuantityUnit::select('id', 'name', 'code', 'description')
            ->get()
            ->toArray();
    }

    public function getProductPackagingUnit(): array
    {
        return ProductPackingUnit::select('id', 'name', 'code', 'description')
            ->get()
            ->toArray();
    }

    public function getProductTax(bool $forEbm): array
    {

        return Tax::select('id', 'code', 'rate', 'name', 'description')
            ->where('forEBM', $forEbm)
            ->get()
            ->toArray();
    }

    public function getProductCountry(): array
    {
        return Country::select('id', 'name', 'code')
            ->get()
            ->toArray();
    }
}
