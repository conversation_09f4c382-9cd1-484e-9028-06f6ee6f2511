<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use DateTimeInterface;

class UserInvitation extends Model
{
    /** @use HasFactory<\Database\Factories\UserInvitationFactory> */
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'UserInvitation';

    protected $fillable = [
        'email',
        'company_id',
        'permission_id',
        'isAccepted',
        'expireAt',
        'branch_id',
    ];


    protected $casts = [
        'isAccepted' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'expireAt' => 'datetime',
    ];

    protected function serializeDate(DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function getExpireAtAttribute($value)
    {
        return \Carbon\Carbon::createFromTimestamp($value)->format('Y-m-d H:i:s');
    }

    public function permission(): BelongsTo
    {
        return $this->belongsTo(RolePermission::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(CompanyBranch::class, 'branch_id');
    }
}
