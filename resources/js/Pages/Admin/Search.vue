<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";
import { ref, watch } from 'vue';
import axios from 'axios';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const props = defineProps({
    companies: Array,
    invoiceTypes: Array,
});

const searchQuery = ref('');
const selectedInvoiceType = ref(props.invoiceTypes[0] || '');
const selectedCompany = ref(props.companies[0]?.id || '');
const startDate = ref('');
const endDate = ref('');
const currentPage = ref(1);
const itemsPerPage = ref(10);
const searchResults = ref([]);
const lastPage = ref(1);
const totalItems = ref(0);
const isLoading = ref(false);

const fetchSearchResults = async (page = 1) => {
    isLoading.value = true;
    try {
        const response = await axios.get('/admin/search-json', {
            params: {
                invoiceType: selectedInvoiceType.value,
                companyId: selectedCompany.value,
                fromDate: startDate.value,
                toDate: endDate.value,
                searchQuery: searchQuery.value,
                page: page,
                perPage: itemsPerPage.value,
            }
        });

        searchResults.value = response.data.data;
        currentPage.value = response.data.currentPage;
        lastPage.value = response.data.lastPage;
        totalItems.value = response.data.total;
        itemsPerPage.value = response.data.itemsPerPage;
    } catch (error) {
        console.error('Error fetching search results:', error);
    } finally {
        isLoading.value = false;
    }
};

const debounce = (func, wait) => {
    let timeout;
    return (...args) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
};

watch([searchQuery, selectedInvoiceType, selectedCompany, startDate, endDate], debounce(() => {
    currentPage.value = 1;
    fetchSearchResults();
}, 300));

fetchSearchResults();

const goToPage = (page) => {
    if (page >= 1 && page <= lastPage.value) {
        currentPage.value = page;
        fetchSearchResults(page);
    }
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
};
</script>

<template>
    <AppLayout title="Search">
        <div class="container mx-auto p-4">
            <!-- Search Form -->
            <div class="mb-6">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-4">
                    <Card class="bg-white border-gray-200">
                        <CardHeader>
                            <CardTitle class="text-sm font-medium text-gray-900">Search</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Input
                                v-model="searchQuery"
                                placeholder="Search by client name or invoice number..."
                                class="bg-white text-gray-900 border-gray-300 focus:border-gray-500 focus:ring-gray-500"
                            />
                        </CardContent>
                    </Card>
                    <Card class="bg-white border-gray-200">
                        <CardHeader>
                            <CardTitle class="text-sm font-medium text-gray-900">Company</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Select v-model="selectedCompany">
                                <SelectTrigger class="bg-white text-gray-900 border-gray-300">
                                    <SelectValue :placeholder="companies[0]?.name || 'Select Company'" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="company in companies" :key="company.id" :value="company.id">
                                        {{ company.name }}
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </CardContent>
                    </Card>
                    <Card class="bg-white border-gray-200">
                        <CardHeader>
                            <CardTitle class="text-sm font-medium text-gray-900">Invoice Type</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Select v-model="selectedInvoiceType">
                                <SelectTrigger class="bg-white text-gray-900 border-gray-300">
                                    <SelectValue :placeholder="invoiceTypes[0] || 'Select Type'" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="type in invoiceTypes" :key="type" :value="type">
                                        {{ type }}
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </CardContent>
                    </Card>
                    <Card class="bg-white border-gray-200 lg:col-span-2">
                        <CardHeader>
                            <CardTitle class="text-sm font-medium text-gray-900">Date Range</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-900 mb-1">Start Date</label>
                                    <Input
                                        v-model="startDate"
                                        type="date"
                                        class="bg-white text-gray-900 border-gray-300 focus:border-gray-500 focus:ring-gray-500"
                                    />
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-900 mb-1">End Date</label>
                                    <Input
                                        v-model="endDate"
                                        type="date"
                                        class="bg-white text-gray-900 border-gray-300 focus:border-gray-500 focus:ring-gray-500"
                                    />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>

            <!-- Search Results -->
            <div v-if="isLoading" class="text-center py-4 text-gray-900">
                Loading...
            </div>
            <div v-else-if="searchResults.length === 0" class="text-center py-4 text-gray-900">
                No results found.
            </div>
            <div v-else class="bg-white border border-gray-200 rounded-lg overflow-x-auto">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead class="text-gray-900">Invoice Number</TableHead>
                            <TableHead class="text-gray-900">Client Name</TableHead>
                            <TableHead class="text-gray-900">Company</TableHead>
                            <TableHead class="text-gray-900">Branch</TableHead>
                            <TableHead class="text-gray-900">User</TableHead>
                            <TableHead class="text-gray-900">Sales Date</TableHead>
                            <TableHead class="text-gray-900">Total Amount</TableHead>
                            <TableHead class="text-gray-900">Items</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="result in searchResults" :key="result.id">
                            <TableCell class="text-gray-900">{{ result.invoiceNumber }}</TableCell>
                            <TableCell class="text-gray-900">{{ result.clientName }}</TableCell>
                            <TableCell class="text-gray-900">{{ result.companyName }}</TableCell>
                            <TableCell class="text-gray-900">{{ result.branchName }}</TableCell>
                            <TableCell class="text-gray-900">{{ result.userName }}</TableCell>
                            <TableCell class="text-gray-900">{{ formatDate(result.salesDate) }}</TableCell>
                            <TableCell class="text-gray-900">{{ result.totalAmount }}</TableCell>
                            <TableCell class="text-gray-900">
                                <div v-for="item in result.items" :key="item.productName" class="text-sm">
                                    {{ item.productName }} ({{ item.units }} units)
                                </div>
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>

            <!-- Pagination -->
            <div v-if="searchResults.length > 0" class="mt-4 flex justify-between items-center">
                <div class="text-sm text-gray-900">
                    Showing {{ (currentPage - 1) * itemsPerPage + 1 }} to {{ Math.min(currentPage * itemsPerPage, totalItems) }} of {{ totalItems }} results
                </div>
                <div class="flex space-x-2">
                    <Button
                        :disabled="currentPage === 1"
                        @click="goToPage(currentPage - 1)"
                        variant="outline"
                        class="border-gray-300 text-gray-900 hover:bg-gray-100 disabled:opacity-50"
                    >
                        Previous
                    </Button>
                    <Button
                        v-for="page in Math.min(lastPage, 5)"
                        :key="page"
                        @click="goToPage(page)"
                        :variant="currentPage === page ? 'default' : 'outline'"
                        class="border-gray-300 text-gray-900"
                        :class="{ 'bg-gray-900 text-white hover:bg-gray-700': currentPage === page, 'hover:bg-gray-100': currentPage !== page }"
                    >
                        {{ page }}
                    </Button>
                    <Button
                        :disabled="currentPage === lastPage"
                        @click="goToPage(currentPage + 1)"
                        variant="outline"
                        class="border-gray-300 text-gray-900 hover:bg-gray-100 disabled:opacity-50"
                    >
                        Next
                    </Button>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style scoped>
</style>