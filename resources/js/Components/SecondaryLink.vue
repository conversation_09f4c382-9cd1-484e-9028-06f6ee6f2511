<script setup>
import { <PERSON> } from '@inertiajs/vue3';
const props = defineProps({
    href: String,
    active: <PERSON>olean,
});

</script>

<template>
    <Link :href="href"  class="items-center text-center px-4 py-2 bg-zata-background hover:bg-zata-background-dark border border-transparent rounded-md font-medium  text-zata-primary-dark tracking-widest  focus:outline-none disabled:opacity-50 transition ease-in-out duration-150 ">
        <slot />
    </Link>
</template>
