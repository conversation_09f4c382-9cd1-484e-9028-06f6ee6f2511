<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref, computed } from 'vue';

import AuthenticationCardLogo from '@/Components/AuthenticationCardLogo.vue';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';

import { Bar } from 'vue-chartjs';
import { Chart as ChartJS, Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale } from 'chart.js';

import { CircleAlert, Info, Trash2, FileDown, LogIn } from 'lucide-vue-next';

ChartJS.register(Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale);

const props = defineProps({
    result: Object,
    auth: Object,
});

const form = useForm({
    userType: 'individual', taxType: 'VAT', timeScope: 'ytd', totalSales: null,
    taxableSupplies: null, expenses: null, losses: null, inputVat: null,
    companyExpenses: null, companyLosses: null, salesData: [], employees: [],
});

const showLoginModal = ref(false);

const companyTotalSales = computed(() => form.salesData.reduce((total, item) => total + (Number(item.amount) || 0), 0));
const companyTotalSalaries = computed(() => form.employees.reduce((total, item) => total + (Number(item.salary) || 0), 0));

const addSaleRow = () => form.salesData.push({ category: '', amount: null });
const removeSaleRow = (index) => form.salesData.splice(index, 1);
const addEmployeeRow = () => form.employees.push({ name: '', salary: null });
const removeEmployeeRow = (index) => form.employees.splice(index, 1);

function calculateTax() {
    if (form.taxType === 'VAT' && !form.taxableSupplies) {
        form.taxableSupplies = form.userType === 'individual' ? form.totalSales : companyTotalSales.value;
    }
    form.post(route('tax-compliance.calculate'), { preserveScroll: true, preserveState: true });
}

function clearForm() {
    form.reset();
    form.get(route('tax-compliance.index'), { preserveScroll: true });
}

function handleExportClick() {
    if (props.auth.user) {
        alert("Generating your PDF report...");
    } else {
        showLoginModal.value = true;
    }
}

const chartOptions = { responsive: true, maintainAspectRatio: false, plugins: { legend: { display: false } } };
</script>

<template>
  <Head title="Tax Compliance Calculator" />
  <div class="min-h-screen bg-gray-50 text-gray-800 flex flex-col">
    <header class="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b">
      <nav class="flex items-center justify-between p-4 max-w-7xl mx-auto" aria-label="Global">
        <div class="flex lg:flex-1">
          <Link href="/">
            <AuthenticationCardLogo class="h-8 w-auto" />
          </Link>
        </div>
        <div class="flex lg:flex-1 justify-end items-center space-x-6">
          <Link :href="route('tax-compliance.index')" class="text-sm font-semibold leading-6 text-zata-primary-dark ">
            Tax Calculator
          </Link>
          <template v-if="auth.user">
            <Link :href="route('dashboard')" class="text-sm font-semibold leading-6 text-gray-900">
              Dashboard
            </Link>
          </template>
          <template v-else>
            <Link :href="route('login')" class="text-sm font-semibold leading-6 text-gray-900">
              Log in
            </Link>
            <Button as-child size="sm">
              <Link :href="route('register')">Register</Link>
            </Button>
          </template>
        </div>
      </nav>
    </header>

    <main class="flex-grow">
      <div class="max-w-7xl mx-auto py-10 px-4 sm:px-6 lg:px-8 grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
        <Card class="w-full">
          <CardHeader>
            <CardTitle class="text-2xl font-bold">Tax Compliance Calculator</CardTitle>
            <CardDescription>Select user type and enter your financial data to estimate tax liability.</CardDescription>
          </CardHeader>
          <CardContent>
            <form @submit.prevent="calculateTax" class="space-y-6">
              <Tabs v-model="form.userType" class="w-full">
                <TabsList class="grid w-full grid-cols-2">
                  <TabsTrigger value="individual">For Individuals</TabsTrigger>
                  <TabsTrigger value="company">For Companies</TabsTrigger>
                </TabsList>
                <TabsContent value="individual" class="space-y-4 pt-4">
                  <div>
                    <Label for="totalSales" class="block mb-1">Total Sales</Label>
                    <Input id="totalSales" v-model="form.totalSales" type="number" placeholder="Total Sales (RWF)" />
                    <p v-if="form.errors.totalSales" class="text-red-500 text-sm mt-1">
                      {{ form.errors.totalSales }}
                    </p>
                  </div>
                  <div>
                    <Label for="expenses" class="block mb-1">Total Expenses</Label>
                    <Input id="expenses" v-model="form.expenses" type="number" placeholder="Total Expenses (RWF)" />
                    <p v-if="form.errors.expenses" class="text-red-500 text-sm mt-1">
                      {{ form.errors.expenses }}
                    </p>
                  </div>
                  <div>
                    <Label for="losses" class="block mb-1">Reportable Losses</Label>
                    <Input id="losses" v-model="form.losses" type="number" placeholder="Reportable Losses (RWF)" />
                    <p v-if="form.errors.losses" class="text-red-500 text-sm mt-1">
                      {{ form.errors.losses }}
                    </p>
                  </div>
                </TabsContent>
                <TabsContent value="company" class="space-y-6 pt-4">
                  <div>
                    <Label class="block font-bold mb-1">Sales Data</Label>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Category</TableHead>
                          <TableHead>Amount (RWF)</TableHead>
                          <TableHead />
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow v-for="(sale, index) in form.salesData" :key="`sale-${index}`">
                          <TableCell>
                            <Input v-model="sale.category" placeholder="e.g., Product A" />
                          </TableCell>
                          <TableCell>
                            <Input v-model="sale.amount" type="number" />
                          </TableCell>
                          <TableCell>
                            <Button type="button" variant="ghost" size="icon" @click="removeSaleRow(index)">
                              <Trash2 class="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                    <p v-if="form.errors.salesData" class="text-red-500 text-sm mt-1">
                      {{ form.errors.salesData }}
                    </p>
                    <Button type="button" variant="outline" @click="addSaleRow" class="mt-2 w-full text-xs">
                      Add Sale
                    </Button>
                  </div>
                  <div>
                    <Label class="block font-bold mb-1">Employee Salaries (for PAYE)</Label>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Employee</TableHead>
                          <TableHead>Salary (RWF)</TableHead>
                          <TableHead />
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow v-for="(emp, index) in form.employees" :key="`emp-${index}`">
                          <TableCell>
                            <Input v-model="emp.name" placeholder="e.g., Jane Doe" />
                          </TableCell>
                          <TableCell>
                            <Input v-model="emp.salary" type="number" />
                          </TableCell>
                          <TableCell>
                            <Button type="button" variant="ghost" size="icon" @click="removeEmployeeRow(index)">
                              <Trash2 class="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                    <p v-if="form.errors.employees" class="text-red-500 text-sm mt-1">
                      {{ form.errors.employees }}
                    </p>
                    <Button type="button" variant="outline" @click="addEmployeeRow" class="mt-2 w-full text-xs">
                      Add Employee
                    </Button>
                  </div>
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <Label for="companyExpenses" class="block mb-1">Company Expenses</Label>
                      <Input
                        id="companyExpenses"
                        v-model="form.companyExpenses"
                        type="number"
                        placeholder="Company Expenses"
                      />
                      <p v-if="form.errors.companyExpenses" class="text-red-500 text-sm mt-1">
                        {{ form.errors.companyExpenses }}
                      </p>
                    </div>
                    <div>
                      <Label for="companyLosses" class="block mb-1">Company Losses</Label>
                      <Input
                        id="companyLosses"
                        v-model="form.companyLosses"
                        type="number"
                        placeholder="Company Losses"
                      />
                      <p v-if="form.errors.companyLosses" class="text-red-500 text-sm mt-1">
                        {{ form.errors.companyLosses }}
                      </p>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <Label for="taxType" class="block mb-1">Tax Type</Label>
                  <select
                    id="taxType"
                    v-model="form.taxType"
                    class="w-full block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                  >
                    <option value="VAT">VAT</option>
                    <option value="CIT_PIT">CIT/PIT</option>
                  </select>
                  <p v-if="form.errors.taxType" class="text-red-500 text-sm mt-1">
                    {{ form.errors.taxType }}
                  </p>
                </div>
                <div v-if="form.taxType === 'VAT'">
                  <Label for="taxableSupplies" class="block mb-1">Taxable Supplies</Label>
                  <Input
                    id="taxableSupplies"
                    v-model="form.taxableSupplies"
                    type="number"
                    placeholder="Taxable Supplies (RWF)"
                  />
                  <p v-if="form.errors.taxableSupplies" class="text-red-500 text-sm mt-1">
                    {{ form.errors.taxableSupplies }}
                  </p>
                </div>
              </div>
              <div v-if="form.taxType === 'VAT'">
                <Label for="inputVat" class="block mb-1">Input VAT</Label>
                <Input
                  id="inputVat"
                  v-model="form.inputVat"
                  type="number"
                  placeholder="Input VAT (RWF)"
                />
                <p v-if="form.errors.inputVat" class="text-red-500 text-sm mt-1">
                  {{ form.errors.inputVat }}
                </p>
              </div>
            </form>
          </CardContent>
          <CardFooter class="flex justify-between space-x-4">
            <Button type="button" variant="secondary" @click="clearForm" :disabled="form.processing" class="w-1/3">
              Clear
            </Button>
            <Button type="submit" @click="calculateTax" :disabled="form.processing" class="w-2/3">
              {{ form.processing ? 'Calculating...' : 'Calculate Tax' }}
            </Button>
          </CardFooter>
        </Card>

        <div class="w-full space-y-8">
          <Card v-if="props.result" class="transition-all duration-500 ease-in-out">
            <CardHeader>
              <CardTitle>Results Dashboard</CardTitle>
            </CardHeader>
            <CardContent class="space-y-6">
              <div class="text-center">
                <p class="text-gray-500">Total Tax Liability ({{ props.result.summary.taxType }})</p>
                <p class="text-4xl font-bold tracking-tight">
                  RWF {{ props.result.summary.taxAmount.toLocaleString() }}
                </p>
              </div>
              <div class="h-64">
                <Bar :data="props.result.chartsData" :options="chartOptions" />
              </div>
            </CardContent>
            <CardFooter>
              <Button @click="handleExportClick" class="w-full">
                <FileDown class="mr-2 h-4 w-4" />Export as PDF
              </Button>
            </CardFooter>
          </Card>
          <Card v-if="props.result && props.result.complianceReport.length > 0">
            <CardHeader>
              <CardTitle>RRA Compliance Report</CardTitle>
            </CardHeader>
            <CardContent class="space-y-3">
              <Alert v-for="(note, index) in props.result.complianceReport" :key="index"
                :variant="note.level === 'critical' ? 'destructive' : 'default'">
                <component :is="note.level === 'info' ? Info : CircleAlert" class="h-4 w-4" />
                <AlertTitle class="capitalize">{{ note.level }}</AlertTitle>
                <AlertDescription>{{ note.message }}</AlertDescription>
              </Alert>
            </CardContent>
          </Card>
          <div v-if="!props.result" class="text-center text-gray-500 border-2 border-dashed rounded-lg p-12">
            <p>Your calculation results and compliance dashboard will appear here.</p>
          </div>
        </div>
      </div>
    </main>

    <footer class="mt-auto max-w-7xl mx-auto w-full flex justify-center items-center my-8">
      <p class="text-sm text-gray-500">© {{ new Date().getFullYear() }} HIQ Africa. All rights reserved.</p>
    </footer>

    <Dialog :open="showLoginModal" @update:open="showLoginModal = false">
      <DialogContent>
        <DialogHeader>
          <DialogTitle class="flex items-center">
            <LogIn class="mr-2 h-5 w-5" />Unlock PDF Exports
          </DialogTitle>
          <DialogDescription>
            To save and export your tax report as a PDF, please log in or create an account. Registered users can
            save calculations and access more features.
          </DialogDescription>
        </DialogHeader>
        <div class="flex justify-end space-x-4 mt-4">
          <Button variant="ghost" @click="showLoginModal = false">Cancel</Button>
          <Button as-child>
            <Link :href="route('login')">Log In</Link>
          </Button>
          <Button as-child variant="outline">
            <Link :href="route('register')">Register</Link>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>