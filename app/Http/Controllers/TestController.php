<?php

namespace App\Http\Controllers;

use App\Services\PdfService;
use OpenApi\Attributes as OA;


class TestController extends Controller
{
    //

    public function __construct(private PdfService $pdfService) {}
    #[OA\Get(
        path: "/api/v1/test",
        description: "Test API",
        security: [["authentication" => []]],
        summary: "Test API",
        tags: ["Test"]
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                "message" => "Zata Point Global Service API",
                "status" => "Connected",
                "time" => "2021-09-29T14:48:00.000000Z",
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function index()
    {

        return response()->json([
            'message' => 'Zata Point Global Service API',
            'status' => 'Connected',
            'time' => now()->toDateTimeString(),
        ]);
    }

    public function pdf()
    {

        return response()->json([
            'message' => 'Zata Point Global Service API',
            'status' => 'Connected',
            'time' => now()->toDateTimeString(),
            
        ]);
    }
}
