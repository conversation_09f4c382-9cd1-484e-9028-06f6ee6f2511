<?php

namespace App\Services;

use App\Data\NewCompanyData;
use App\Data\Api\NewCompanyData as NewCompanyDataApi;
use App\Data\UpdateCompanyData;
use App\Exceptions\BadRequest;
use App\Exceptions\NotFound;
use App\Exceptions\ServerError;
use App\Models\Company;
use App\Models\CompanyInsurance;
use App\Models\Insurance;
use App\Models\Role;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CompanyService
{

    public function __construct(protected RoleService $roleService, protected CompanyBranchService $branchService) {}

    public function getCompanyProfile(int $companyId): array
    {

        $company = Company::where('id', $companyId)
            ->select('id', 'name', 'address', 'phone', 'email', 'tin', 'updated_at')
            ->with('branches:id,name,email,address,company_id')
            ->first();

        return [
            'id' => $company->id,
            'name' => $company->name,
            'address' => $company->address,
            'phone' => $company->phone,
            'email' => $company->email,
            'tin' => $company->tin,
            'updated_at' => $company->updated_at,
            'image' => 'https://ui-avatars.com/api/?name=' . urlencode($company->name) . '&color=7F9CF5&background=EBF4FF',
            'branches' => $company->branches->map(fn($branch) => [
                'id' => $branch->id,
                'name' => $branch->name,
                'email' => $branch->email,
                'address' => $branch->address,
                'image' => 'https://ui-avatars.com/api/?name=' . urlencode($branch->name) . '&color=7F9CF5&background=EBF4FF',
            ]),
        ];
    }

    public function  storeCompany(NewCompanyData | NewCompanyDataApi $data, $userId): void
    {

        $userRoles = $this->roleService->userRoles($userId);

        $verifyCompany = Company::whereIn('id', collect($userRoles)->pluck('company_id')->toArray())->get();

        throw_if($verifyCompany->contains('name', $data->name), BadRequest::class, 'Company name already exists');

        if ($data->isEBM) {

            throw_if(is_null($data->tin), BadRequest::class, 'Tin is required for EBM company');
        }

        if (!is_null($data->tin)) {
            throw_if($verifyCompany->contains('tin', $data->tin), BadRequest::class, 'Company tin already exists');
        }

        try {

            DB::beginTransaction();
            $company = Company::create([
                'user_id' => $userId,
                'name' => $data->name,
                'phone' => $data->phone,
                'email' => $data->email,
                'address' => $data->address,
                'tin' => $data->tin,
                'isEBM' => $data->isEBM,
            ]);

            $branch = $this->branchService->storeDefaultBranch($company);

            $insurance = Insurance::all();

            foreach ($insurance as $ins) {
                CompanyInsurance::create([
                    'company_id' => $company->id,
                    'insurance_id' => $ins->id,
                ]);
            }

            $this->roleService->createAdminRole($userId, $company->id, $branch->id);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage());
            throw new ServerError('Failed to create company');
        }
    }

    public function updateCompany(UpdateCompanyData $date, int  $userId, int $companyId): void
    {

        $userRoles = $this->roleService->userRoleByCompanyId($userId, $companyId);

        throw_if(is_null($userRoles), NotFound::class, 'Company not found');

        $company = Company::where('id', $companyId)->first();

        throw_if(is_null($company), NotFound::class, 'Company not found');

        if ($company->isEBM) {
            throw_if(is_null($date->tin), BadRequest::class, 'Tin is required for EBM company');
        }

        $userRoles = $this->roleService->userRoles($userId);

        $verifyCompany = Company::whereIn('id', collect($userRoles)->pluck('company_id')->toArray())
            ->where('id', '!=', $companyId)
            ->get();

        throw_if($verifyCompany->contains('name', $date->name), BadRequest::class, 'Company name already exists');

        if (!is_null($date->tin)) {
            throw_if($verifyCompany->contains('tin', $date->tin), BadRequest::class, 'Company tin already exists');
        }

        if ($date->supportedInsurance) {
            $insuranceIds = collect($date->supportedInsurance)->pluck('id')->toArray();
            $insurance = Insurance::whereIn('id', $insuranceIds)->get();
            throw_if($insurance->count() != count($insuranceIds), BadRequest::class, 'Invalid insurance added');
        }

        try {

            DB::beginTransaction();

            $company->update([
                'name' => $date->name,
                'phone' => $date->phone,
                'email' => $date->email,
                'address' => $date->address,
                'tin' => $date->tin,
            ]);

            if ($date->supportedInsurance) {
                CompanyInsurance::where('company_id', $companyId)->whereNotIn('insurance_id', $insuranceIds)->delete();

                foreach ($date->supportedInsurance as $insurance) {
                    CompanyInsurance::updateOrCreate(
                        ['company_id' => $companyId, 'insurance_id' => $insurance->id],

                    );
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage());
            throw new ServerError('Failed to update company');
        }
    }

    public function getCompany(int $userId, int $perPage, int $page, bool  $isEBM = false): LengthAwarePaginator
    {

        $userRoles = $this->roleService->userRoles($userId);
        $companyIds = collect($userRoles)->pluck('company_id')->toArray();

        $companies = Company::whereIn('id', $companyIds)
            ->when($isEBM, fn($query) => $query->where('isEBM', true))
            ->select([
                'id',
                'name',
                'address',
                'phone',
                'email',
                'tin'
            ])
            ->withCount('branches as branchesCount')
            ->paginate(perPage: $perPage, page: $page);

        $companies->getCollection()->transform(function ($company) use ($userRoles) {
            $company->isDefault = collect($userRoles)->firstWhere('company_id', $company->id)['isDefault'] ?? false;
            return $company;
        });

        return $companies->through(function ($company) {
            return [
                'id' => $company->id,
                'name' => $company->name,
                'address' => $company->address,
                'phone' => $company->phone,
                'email' => $company->email,
                'tin' => $company->tin,
                'branchesCount' => $company->branchesCount,
                'isDefault' => $company->isDefault,
                'image' => 'https://ui-avatars.com/api/?name=' . urlencode($company->name) . '&color=7F9CF5&background=EBF4FF',
            ];
        });
    }

    public function switchCompany(int $userId, int $companyId): void
    {

        $userRoles = $this->roleService->userRoles($userId);

        $company = collect($userRoles)->firstWhere('company_id', $companyId);

        throw_if(!$company, NotFound::class, 'Company not found');

        Role::where('user_id', $userId)->update(['isDefault' => false]);

        Role::where('user_id', $userId)
            ->where('company_id', $companyId)
            ->update(['isDefault' => true]);
    }

    public function getCompanyById(int $userId, int $companyId): Company
    {
        $userRoles = $this->roleService->userRoleByCompanyId($userId, $companyId);

        throw_if(is_null($userRoles), NotFound::class, 'Company not found');

        $company =  Company::where('id', $companyId)
            ->select('id', 'name', 'address', 'phone', 'email', 'tin', 'updated_at')
            ->with(['insurance.details:id,name,code,rate'])
            ->first();

        $insurance = $company->insurance->map(fn($insurance) => [
            'id' => $insurance->id,
            'name' => $insurance->details->name,
            'code' => $insurance->details->code,
            'rate' => $insurance->details->rate,
        ]);
        unset($company->insurance);
        $company->insurance = $insurance;
        return $company;
    }

    public function checkIfUserIsInCompanyByEmail(string $email, int  $companyId): bool
    {
        $user = User::where('email', $email)->first();

        if (!$user) {
            return false;
        }

        $company = Company::where('id', $companyId)->first();

        if (!$company) {
            return false;
        }
        return  $company->roles()->where('user_id', $user->id)->exists();
    }
}
