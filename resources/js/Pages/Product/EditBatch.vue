<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { computed } from 'vue';
import { useForm } from '@inertiajs/vue3';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { format } from 'date-fns';

const props = defineProps({
  Product: Object,
  Batch: Object,
});

const initialExpireDate = props.Batch.expireDate
  ? new Date(props.Batch.expireDate).toISOString().split('T')[0]
  : null;

const form = useForm({
  name: props.Product.name,
  quantity: props.Batch.currentStock,
  salePrice: props.Batch.salesPrice,
  purchasePrice: props.Batch.purchasePrice,
  batchNumber: props.Batch.batchNumber,
  expireDate: initialExpireDate,
  discountRate: props.Batch.discountRate,
});

const minDate = computed(() => {
  const today = new Date();
  return today.toISOString().split('T')[0];
});

const expireDateInWords = computed(() => {
  return form.expireDate
    ? format(new Date(form.expireDate), 'MMMM d, yyyy')
    : 'No expiry date set';
});

function updateProduct() {
  if (form.expireDate && new Date(form.expireDate) < new Date(minDate.value)) {
    form.errors.expireDate = 'Please select a future date';
    return;
  }

  form.post(route('product.updateBatch',[ props.Product.id, props.Batch.id]), {});
}

if (props.Product.hasStock === 'no') {
  form.expireDate = null;
}
</script>
<template>
    <AppLayout title="Update product">
      <div class="py-6 px-4 sm:px-6 lg:px-8">
        <Card class="shadow-lg border border-gray-200 max-w-3xl mx-auto">
          <CardHeader class="bg-gray-50 border-b">
            <CardTitle class="text-xl font-semibold text-gray-800">
              Update <span class="font-bold">{{ Product.name }}</span> - <span class="font-bold text-zata-primary-dark">{{ Batch.batchNumber }}</span> (batch)
            </CardTitle>
          </CardHeader>
          <CardContent class="p-6">
            <form @submit.prevent="updateProduct" class="space-y-6">
              <div class="space-y-2" v-if="Product.hasStock === 'yes'">
                <Label for="quantity">Stock Quantity</Label>
                <Input id="quantity" v-model="form.quantity" type="number" class="bg-gray-200" disabled />
                <p v-if="form.errors.quantity" class="text-red-500 text-sm mt-1">{{ form.errors.quantity }}</p>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                  <Label for="salePrice">Sale Price</Label>
                  <Input id="salePrice" v-model="form.salePrice" type="number" />
                  <p v-if="form.errors.salePrice" class="text-red-500 text-sm mt-1">{{ form.errors.salePrice }}</p>
                </div>
                <div class="space-y-2">
                  <Label for="purchasePrice">Purchase Price</Label>
                  <Input id="purchasePrice" v-model="form.purchasePrice" type="number" />
                  <p v-if="form.errors.purchasePrice" class="text-red-500 text-sm mt-1">{{ form.errors.purchasePrice }}</p>
                </div>
              </div>
  
              <div class="space-y-2">
                <Label for="discountRate">Discount Rate</Label>
                <Input id="discountRate" v-model="form.discountRate" type="number" />
                <p v-if="form.errors.discountRate" class="text-red-500 text-sm mt-1">{{ form.errors.discountRate }}</p>
              </div>
  
              <div class="space-y-2" >
                <Label for="batchNumber">Batch Number</Label>
                <Input id="batchNumber" v-model="form.batchNumber" type="text" class="bg-gray-200" disabled />
                <p v-if="form.errors.batchNumber" class="text-red-500 text-sm mt-1">{{ form.errors.batchNumber }}</p>
              </div>
  
              <div class="space-y-2"  v-if="Product.hasStock === 'yes'">
                <Label for="expireDate">Expiry Date</Label>
                <Input 
                  id="expireDate" 
                  v-model="form.expireDate" 
                  type="date" 
                  :min="minDate"
                />
                <p v-if="form.errors.expireDate" class="text-red-500 text-sm mt-1">{{ form.errors.expireDate }}</p>
              </div>
  
              <div class="flex justify-end gap-4">
                <Button 
                  type="submit" 
                  :disabled="form.processing"
                  :class="{ 'opacity-25': form.processing }"
                >
                  Update 
                </Button>
              </div>
            </form>
  
            <div class="mt-4 text-gray-600 text-sm" v-if="Product.hasStock === 'yes'">
              <p>Expiry Date: <span class="font-medium">{{ expireDateInWords }}</span></p>
            </div>
          </CardContent>
        </Card>
      </div>
  
      <template #sidenav />
      <template #footer />
    </AppLayout>
  </template>