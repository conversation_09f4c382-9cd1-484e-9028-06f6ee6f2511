<script setup>
import { onMounted, ref } from 'vue';

defineProps({
    modelValue: String,
});

defineEmits(['update:modelValue']);

const input = ref(null);

onMounted(() => {
    if (input.value.hasAttribute('autofocus')) {
        input.value.focus();
    }
});

defineExpose({ focus: () => input.value.focus() });
</script>

<template>
    <textarea 
        ref="input" 
        class="border-gray-300 focus:border-gray-400 focus:ring-gray-400 rounded-md mt-1 block w-full"
        :value="modelValue" 
        @input="$emit('update:modelValue', $event.target.value)" 
        rows="4"
    ></textarea>
</template>