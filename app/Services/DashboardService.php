<?php

namespace App\Services;

use App\Enums\TransactionTypeEnums;
use App\Models\Order;
use App\Models\OrderItems;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Models\TransactionAccount;

class DashboardService
{

    public function dashboardData(int $branchid)
    {

        $sales = Order::where('branch_id', $branchid)
            ->where('isRefunded', false)
            ->with(['items:id,order_id', 'user:id,name'])
            ->select('id', 'branch_id', 'isRefunded', 'transactionType', 'totAmt as totalAmount', 'salesDate', 'clientName', 'user_id', 'created_at')
            ->limit(10)
            ->get();

        $payments =TransactionAccount::where('branch_id', $branchid)
            ->with(['paymentMode', 'user'])
            ->latest()
            ->limit(6)
            ->get();

        return [

            'FirstRowData' => [
                [
                    'value' => Order::where('branch_id', $branchid)
                        ->where('isRefunded', false)
                        ->where('transactionType', TransactionTypeEnums::SALES->value)
                        ->where('created_at', '>=', strtotime('today'))
                        ->count(),
                    'label' => 'Today Sales',
                ],
                [
                    'value' => Order::where('branch_id', $branchid)
                        ->where('isRefunded', false)
                        ->where('transactionType', TransactionTypeEnums::SALES->value)
                        ->where('created_at', '>=', strtotime('this week'))
                        ->count(),
                    'label' => 'Weekly Sales',
                ],
                'productInStock' => [
                    'value' =>  Product::where('branch_id', $branchid)
                        ->whereInStock()
                        ->count(),
                    'label' => 'Product In Stock'
                ],
                'nearToExpire' => [
                    'value' => Product::where('branch_id', $branchid)->count(),
                    'label' => 'Total Products',
                ],
            ],
            'PaymentDetails' => $payments,

            'TopSellingProduct' =>

            OrderItems::whereHas('order', fn($query) => $query->where('branch_id', $branchid))
                ->selectRaw('SUM(quantity) as total_units_sold, SUM(totalAmount) as total_sales, product_id, productName')
                ->groupBy('product_id', 'productName')
                ->orderByDesc('total_units_sold')
                ->limit(6)
                ->get()
                ->map(fn($item) => [
                    'name' => $item->productName,
                    'unitsSold' => $item->total_units_sold,
                    'sales' => $item->total_sales,
                    'amount' => $item->total_sales
                ]),

            'SaleTransactions' => $sales,
            'expiringProducts' =>
            ProductDetail::whereHas('product', fn($query) => $query->where('branch_id', $branchid))
                ->where('currentStock', '>', 0)
                ->orderBy('expireDate', 'asc')
                ->limit(6)
                ->get()
                ->map(fn($product) => [
                    'name' => $product->product->name,
                    'expireDate' => $product->expireDate,
                    'currentStock' => $product->currentStock,
                    'batchNumber' => $product->batchNumber,
                    'code' => $product->product->itemCode
                ]),

        ];
    }
}
