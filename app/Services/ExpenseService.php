<?php

namespace App\Services;

use App\Data\NewExpenseData;
use App\Data\TransactionAccountData;
use App\Enums\TransactionAccountTypesEnum;
use App\Enums\TransactionTypeEnums;
use App\Exceptions\NotFound;
use App\Models\Expense;
use App\Models\ExpenseCategory;
use App\Models\PaymentMode;
use Illuminate\Pagination\LengthAwarePaginator;

class ExpenseService
{

    public function __construct(protected TransactionAccountService $transactionAccountService)
    {
        
    }

    public function expenseCategories(int $companyId): array
    {

        return ExpenseCategory::where('isActive', true)
            ->whereCompanyId($companyId)
            ->select('id', 'name', 'description')
            ->get()
            ->toArray();
    }

    public function expenses(int $branchId, int $perPage, int $page, string|int|null  $searchQuery): LengthAwarePaginator
    {

        if (is_null($searchQuery)) {
            return Expense::select('id', 'amount', 'description', 'date', 'category_id', 'payment_mode_id', 'branch_id')
                ->where('branch_id', $branchId)
                ->latest()
                ->with(['category:id,name', 'paymentMode:id,name'])
                ->paginate(perPage: $perPage, page: $page);
        }

        return Expense::search($searchQuery)
            ->where('branch_id', $branchId)
            ->query(fn($query) => $query->select('id', 'amount', 'description', 'date', 'category_id', 'payment_mode_id', 'branch_id'))

            ->query(fn($query) => $query->with(['category:id,name', 'paymentMode:id,name']))
            ->latest()
            ->paginate(perPage: $perPage, page: $page);
    }

    public function expenseById(int $id, int $branchId): Expense
    {

        $expense =  Expense::where('id', $id)
            ->select('id', 'amount', 'description', 'date', 'category_id', 'payment_mode_id', 'branch_id', 'created_at', 'updated_at')
            ->where('branch_id', $branchId)
            ->with('category:id,name')
            ->with('paymentMode:id,name')
            ->first();

        throw_if(!$expense, NotFound::class, 'Expense not found');

        return $expense;
    }


    public function storeExpense(NewExpenseData $data,int $userId, int $branchId, int $companyId): void
    {

        $validatePaymentMode = PaymentMode::where('id', $data->paymentModeID)
            ->whereCompanyId($companyId)
            ->exists();

        throw_if(!$validatePaymentMode, NotFound::class, 'Invalid Payment Mode');

        $validateExpenseCategory = ExpenseCategory::where('id', $data->categoryID)
            ->whereCompanyId($companyId)
            ->exists();

        throw_if(!$validateExpenseCategory, NotFound::class, 'Invalid Expense Category');

       $expense =  Expense::create([
            'amount' => $data->amount,
            'category_id' => $data->categoryID,
            'description' => $data->description,
            'date' => $data->date,
            'payment_mode_id' => $data->paymentModeID,
            'branch_id' => $branchId
        ]);

        $transactionAccountData = TransactionAccountData::from([
            'branch_id' => $branchId,
            'payment_mode_id' => $data->paymentModeID,
            'user_id' => $userId,
            'amount' => $data->amount,
            'type' => TransactionAccountTypesEnum::DEBIT->value,
            'sourceType' => TransactionTypeEnums::EXPENSE->value,
            'source_id' => $expense->id,
            'date' => $data->date,
            'description' => $data->description
        ]);

        $this->transactionAccountService->createTransaction($transactionAccountData);
    }

    public function updateExpense(NewExpenseData $data, int $id, int $branchId, int $companyId): void
    {

        $validatePaymentMode = PaymentMode::where('id', $data->paymentModeID)
            ->whereCompanyId($companyId)
            ->exists();

        throw_if(!$validatePaymentMode, NotFound::class, 'Invalid Payment Mode');

        $validateExpenseCategory = ExpenseCategory::where('id', $data->categoryID)
            ->whereCompanyId($companyId)
            ->exists();

        throw_if(!$validateExpenseCategory, NotFound::class, 'Invalid Expense Category');

        $expense = Expense::where('id', $id)
            ->where('branch_id', $branchId)
            ->first();

        throw_if(!$expense, NotFound::class, 'Expense not found');

        $expense->update([
            'amount' => $data->amount,
            'category_id' => $data->categoryID,
            'description' => $data->description,
            'date' => $data->date,
            'payment_mode_id' => $data->paymentModeID,
        ]);
    }
}
