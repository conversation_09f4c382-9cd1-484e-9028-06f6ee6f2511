<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Data;


/**
 * @param StockItemsListData[] $itemList
 */
class StockItemsData extends Data
{
    public function __construct(
        public string $tin,
        public string $bhfId,
        public float $sarNo,
        public float $orgSarNo,
        public string $regTyCd,
        public ?string $custTin,
        public ?string $custNm,
        public ?string $custBhfId,
        public string $sarTyCd,
        public string $ocrnDt,
        public float $totItemCnt,
        public float $totTaxblAmt,
        public float $totTaxAmt,
        public float $totAmt,
        public ?string $remark,
        public string $regrId,
        public string $regrNm,
        public string $modrNm,
        public string $modrId,
        public array $itemList

    ) {}
}
