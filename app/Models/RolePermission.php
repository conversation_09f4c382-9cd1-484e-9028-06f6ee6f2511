<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RolePermission extends Model
{
    /** @use HasFactory<\Database\Factories\RolePermissionFactory> */
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'RolePermission';

    protected $fillable = [
        'company_id',
        'name',
        'description',
        'isGlobal',
        'permissions',
    ];

    protected $casts = [
        'permissions' => 'array',
        'isGlobal' => 'boolean',
    ];

    public function scopeWhereCompanyId($query, int $companyId)
    {
        return $query->where(function ($query) use ($companyId) {
            $query->where('company_id', $companyId)
                ->orWhereNull('company_id');
        });
    }
}
