<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from "@inertiajs/vue3";
import FormSection from '@/Components/FormSection.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import TextAreaInput from '@/Components/TextAreaInput.vue';

const props = defineProps({
    Transaction: Object,
    isEBM: Boolean,
});

const form = useForm({
    purchaseCode: null,
    transactionDate: null,
    note: null,
});

function refundTransaction() {
    form.post(route('transaction.proforma.convert.store', props.Transaction.id));
}
</script>

<template>
    <AppLayout title="Convert Proforma to Sale">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Convert Proforma to Sale
            </h2>
        </template>

        <FormSection>
            <template #title>
                Convert Proforma to Sale
            </template>

            <template #form>
                <form class="" @submit.prevent="refundTransaction">

                    <div class="pb-4" v-if="(Transaction.clientTin !== null && Transaction.clientTin !== '') && isEBM">
                        <InputLabel for="purchaseCode" value="Purchase Code" />
                        <TextInput id="purchaseCode" v-model="form.purchaseCode" type="text" />
                        <InputError class="mt-2" :message="form.errors.purchaseCode" />
                    </div>

                    <div class="pb-4">
                        <InputLabel for="transactionDate" value="Transaction Date" />
                        <TextInput id="transactionDate" v-model="form.transactionDate" type="date" />
                        <InputError class="mt-2" :message="form.errors.transactionDate" />
                    </div>

                    <div class="pb-4">
                        <InputLabel for="note" value="Note" />
                        <TextAreaInput id="note" v-model="form.note" />
                        <InputError class="mt-2" :message="form.errors.note" />
                    </div>

                    <div class="flex justify-end mt-4">
                        <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                            Convert
                        </PrimaryButton>
                    </div>
                </form>
            </template>
        </FormSection>
        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>
