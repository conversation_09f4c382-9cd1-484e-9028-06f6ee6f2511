<script setup>
import { <PERSON>, <PERSON> } from '@inertiajs/vue3';
import AuthenticationCardLogo from '@/Components/AuthenticationCardLogo.vue';
import { But<PERSON> } from '@/components/ui/button';

import "@fortawesome/fontawesome-free/css/all.css";
import "@fortawesome/fontawesome-free/js/all.js";

</script>

<template>

    <Head title="Zata Point" />
    <div class="bg-white text-black flex flex-col relative overflow-hidden">
        <div class="flex justify-center items-center text-white py-12 relative z-10">
            <AuthenticationCardLogo />
        </div>
        <div class="relative flex flex-col items-center justify-center z-10">
            <div class="w-full px-6 text-center">
                <header class="py-10 w-full">
                    <h1 class="text-4xl font-extrabold text-gray-800 leading-tight mx-auto max-w-2xl">
                        A Simpler, Smarter Way to Manage, Track, and Trace Products in Rwanda
                    </h1>
                    <p class="mt-4 text-lg text-gray-600 mx-auto max-w-xl">
                        Accurate product tracking, stock transparency, and regulatory compliance for supply chain
                        professionals in Rwanda.
                    </p>
                </header>
                <nav class="flex justify-center mt-6 space-x-4">
                    <template v-if="$page.props.auth.user">
                        <Button as-child variant="outline">
                            <Link :href="route('dashboard')">
                            Dashboard
                            </Link>
                        </Button>
                    </template>
                    <template v-else>
                        <Button as-child variant="outline">
                            <Link :href="route('login')">
                            Login
                            </Link>
                        </Button>
                        <Button as-child>
                            <Link :href="route('register')">
                            Register
                            </Link>
                        </Button>
                    </template>
                </nav>
            </div>
        </div>
        <section class="py-16 relative z-10">
            <div class="max-w-6xl mx-auto px-6">
                <h2 class="text-3xl font-bold text-gray-800 text-center mb-12">
                    Why Choose Us?
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="rounded-lg text-center border border-gray-300 py-12 bg-gray-50 px-8 feature-card">
                        <i class="fa-solid fa-cart-plus h-8 text-zata-secondary-dark mb-2"></i>
                        <h3 class="text-xl font-bold text-gray-800">Smart ERP</h3>
                        <p class="mt-2 text-gray-600">Multi-branch ePOS system to streamline your operations.</p>
                    </div>
                    <div class="rounded-lg text-center border border-gray-300 py-12 bg-gray-50 px-8 feature-card">
                        <i class="fa-solid fa-landmark h-8 text-zata-secondary-dark mb-2"></i>
                        <h3 class="text-xl font-bold text-gray-800">EBM Certified</h3>
                        <p class="mt-2 text-gray-600">Seamless VSDC compliance to meet Rwanda's regulatory standards.
                        </p>
                    </div>
                    <div class="rounded-lg text-center border border-gray-300 py-12 bg-gray-50 px-8 feature-card">
                        <i class="fa-solid fa-credit-card h-8 text-zata-secondary-dark mb-2"></i>
                        <h3 class="text-xl font-bold text-gray-800">PCI DSS 4.0</h3>
                        <p class="mt-2 text-gray-600">Secure digital payments with industry-leading standards.</p>
                    </div>
                    <div class="rounded-lg text-center border border-gray-300 py-12 bg-gray-50 px-8 feature-card">
                        <i class="fa-solid fa-plug h-8 text-zata-secondary-dark mb-2"></i>
                        <h3 class="text-xl font-bold text-gray-800">Odoo & QuickBooks Integration</h3>
                        <p class="mt-2 text-gray-600">Seamlessly connect with Odoo and QuickBooks for enhanced
                            accounting and
                            ERP functionality.</p>
                    </div>
                    <div class="rounded-lg text-center border border-gray-300 py-12 bg-gray-50 px-8 feature-card">
                        <i class="fa-solid fa-prescription-bottle-medical h-8 text-zata-secondary-dark mb-2"></i>
                        <h3 class="text-xl font-bold text-gray-800">RSSB Pharmacy Compliance</h3>
                        <p class="mt-2 text-gray-600">Full support for RSSB requirements, ensuring compliance for
                            pharmacies in
                            Rwanda.</p>
                    </div>
                    <div class="rounded-lg text-center border border-gray-300 py-12 bg-gray-50 px-8 feature-card">
                        <i class="fa-solid fa-code h-8 text-zata-secondary-dark mb-2"></i>
                        <h3 class="text-xl font-bold text-gray-800">API for 3rd Party Tools</h3>
                        <p class="mt-2 text-gray-600">Flexible API options to integrate with your favorite third-party
                            tools and
                            systems.</p>
                    </div>
                </div>
            </div>
        </section>

        <footer class=" mt-auto max-w-7xl mx-auto w-full flex justify-center items-center my-8 relative z-10">
            <p class="text-sm">
                HIQ Africa, 2025 © All rights reserved.
            </p>
        </footer>
    </div>
</template>

<style>
.feature-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease, opacity 0.5s ease;
    opacity: 0.9;
}

.feature-card:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    opacity: 1;
}
</style>