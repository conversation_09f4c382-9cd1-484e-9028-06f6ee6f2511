<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";

const props = defineProps({

});
</script>

<template>
    <AppLayout title="404">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                404 Not Found
            </h2>
        </template>

        <div class="py-12">
            <div class="">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Page Not Found</h3>
                        <p class="mt-2 text-3xl font-semibold text-gray-900">The page you are looking for does not exist.</p>
                    </div>
                </div>
            </div>
        </div>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>
