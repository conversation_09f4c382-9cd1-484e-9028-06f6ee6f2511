<?php

namespace App\Http\Controllers;

use App\Data\NewAffiliateCodeData;
use App\Http\Requests\NewAffiliateCodeRequest;
use App\Services\AffiliateService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;

class AffiliateController extends Controller
{

    protected $user;
    public function __construct(protected AffiliateService $service)
    {
        $this->user = Auth::user();
    }

    public function index()
    {
        return  Inertia::render('Affiliate/Index', [
            'affiliateCode' => $this->service->getAffiliateByUserId($this->user->id)->affiliateCode
        ]);
    }
}
