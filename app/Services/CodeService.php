<?php

namespace App\Services;

use App\Models\CompanyBranch;
use App\Models\Country;
use App\Models\PaymentMode;
use App\Models\ProductClass;
use App\Models\ProductPackingUnit;
use App\Models\ProductQuantityUnit;
use App\Models\ProductType;
use App\Models\Tax;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CodeService
{
    protected array $reqData;
    protected string $itemCodesUrl;
    protected string $itemClassUrl;

    public function __construct(protected EbmService $ebmService)
    {
        $branch = CompanyBranch::with('company')
        ->where('isEbm', true)
        ->first();

        if (!$branch) {
            Log::info('No EBM branch found');
            return;
        }

        $this->itemCodesUrl = $this->ebmService->buildUrl($branch->cluster, $branch->clusterName, config('ebm.item_codes'));
        $this->itemClassUrl =$this->ebmService->buildUrl($branch->cluster, $branch->clusterName, config('ebm.item_class'));
        $this->reqData = [
            'tin' => $branch->company->tin,
            'bhfId' => $branch->branchCode,
            //  'lastReqDt' => now()->subDay()->format('Ymd') . '000000',
            'lastReqDt' => '20180101000000',
        ];
    }

    public function upsertProductsCountriesOfOrigin(): void
    {
        $response = Http::post($this->itemCodesUrl, $this->reqData);
        $responseData = $this->validateResponse($response, 'countries of origin');
        if (!$responseData) return;

        $clsList = $responseData['data']['clsList'] ?? [];
        if (empty($clsList)) {
            Log::info('No countries of origin found in response');
            return;
        }

        $countryItem = collect($clsList)->firstWhere('cdCls', '05');
        if (!$countryItem || !isset($countryItem['dtlList']) || empty($countryItem['dtlList'])) {
            Log::info('No detailed country data found for cdCls "05"');
            return;
        }

        $records = [];
        foreach ($countryItem['dtlList'] as $country) {
            $records[] = [
                'code' => $country['cd'] ?? '',
                'name' => $country['cdNm'] ?? '',
            ];
        }

        Country::upsert($records, ['code'], ['name']);
        Log::info('Countries of origin updated', ['count' => count($records), 'records' => $records]);
    }

    public function upsertProductPackingUnit(): void
    {
        $response = Http::post($this->itemCodesUrl, $this->reqData);
        $responseData = $this->validateResponse($response, 'packing units');
        if (!$responseData) return;

        $clsList = $responseData['data']['clsList'] ?? [];
        if (empty($clsList)) {
            Log::info('No packing units found in response');
            return;
        }

        $item = collect($clsList)->firstWhere('cdCls', '17');
        if (!$item || !isset($item['dtlList']) || empty($item['dtlList'])) {
            Log::info('No detailed packing unit data found for cdCls "17"');
            return;
        }

        $records = [];
        foreach ($item['dtlList'] as $data) {
            $records[] = [
                'code' => $data['cd'] ?? '',
                'name' => $data['cdNm'] ?? '',
                'description' => $data['cdDesc'] ?? '',
                //'use_yn' => $data['useYn'] ?? 'N',
            ];
        }

        ProductPackingUnit::upsert($records, ['code'], ['name', 'description']);

        Log::info('Packing units updated', ['count' => count($records), 'records' => $records]);
    }

    public function upsertProductQuantityUnit(): void
    {
        $response = Http::post($this->itemCodesUrl, $this->reqData);
        $responseData = $this->validateResponse($response, 'quantity units');
        if (!$responseData) return;

        $clsList = $responseData['data']['clsList'] ?? [];
        if (empty($clsList)) {
            Log::info('No quantity units found in response');
            return;
        }

        $item = collect($clsList)->firstWhere('cdCls', '10');
        if (!$item || !isset($item['dtlList']) || empty($item['dtlList'])) {
            Log::info('No detailed quantity unit data found for cdCls "10"');
            return;
        }

        $records = [];
        foreach ($item['dtlList'] as $data) {
            $records[] = [
                'code' => $data['cd'] ?? '',
                'name' => $data['cdNm'] ?? '',
                'description' => $data['cdDesc'] ?? '',
               // 'use_yn' => $data['useYn'] ?? 'N',
            ];
        }

        ProductQuantityUnit::upsert($records, ['code'], ['name', 'description']);
        Log::info('Quantity units updated', ['count' => count($records), 'records' => $records]);
    }

    public function upsertProductItemType(): void
    {
        $response = Http::post($this->itemCodesUrl, $this->reqData);
        $responseData = $this->validateResponse($response, 'item types');
        if (!$responseData) return;

        $clsList = $responseData['data']['clsList'] ?? [];
        if (empty($clsList)) {
            Log::info('No item types found in response');
            return;
        }

        $item = collect($clsList)->firstWhere('cdCls', '24');
        if (!$item || !isset($item['dtlList']) || empty($item['dtlList'])) {
            Log::info('No detailed item type data found for cdCls "24"');
            return;
        }

        $records = [];
        foreach ($item['dtlList'] as $data) {
            $records[] = [
                'code' => $data['cd'] ?? '',
                'name' => $data['cdNm'] ?? '',
                'description' => $data['cdDesc'] ?? '',
                //'use_yn' => $data['useYn'] ?? 'N',
            ];
        }

        ProductType::upsert($records, ['name'], ['description', 'code']);
        Log::info('Item types updated', ['count' => count($records), 'records' => $records]);
    }

    public function upsertProductTaxType(): void
    {
        $response = Http::post($this->itemCodesUrl, $this->reqData);
        $responseData = $this->validateResponse($response, 'tax types');
        if (!$responseData) return;

        $clsList = $responseData['data']['clsList'] ?? [];
        if (empty($clsList)) {
            Log::info('No tax types found in response');
            return;
        }

        $item = collect($clsList)->firstWhere('cdCls', '04');
        if (!$item || !isset($item['dtlList']) || empty($item['dtlList'])) {
            Log::info('No detailed tax type data found for cdCls "04"');
            return;
        }

        $records = [];
        foreach ($item['dtlList'] as $data) {
            $records[] = [
                'code' => $data['cd'] ?? '',
                'name' => $data['cdNm'] ?? '',
                'description' => $data['cdDesc'] ?? '',
                //'use_yn' => $data['useYn'] ?? 'N',
                'rate' => $data['userDfnCd1'] ?? '',
                'forEBM' => true, 
            ];
        }

        Tax::upsert($records, ['code'], ['name', 'description', 'rate', 'forEBM']);
        Log::info('Tax types updated', ['count' => count($records), 'records' => $records]);
    }

    public function upsertPaymentModes(): void
    {
        $response = Http::post($this->itemCodesUrl, $this->reqData);
        $responseData = $this->validateResponse($response, 'payment modes');
        if (!$responseData) return;

        $clsList = $responseData['data']['clsList'] ?? [];
        if (empty($clsList)) {
            Log::info('No payment modes found in response');
            return;
        }

        $item = collect($clsList)->firstWhere('cdCls', '07');
        if (!$item || !isset($item['dtlList']) || empty($item['dtlList'])) {
            Log::info('No detailed payment mode data found for cdCls "07"');
            return;
        }

        $records = [];
        foreach ($item['dtlList'] as $data) {
            $records[] = [
                'code' => $data['cd'] ?? '',
                'name' => $data['cdNm'] ?? '',
                'description' => $data['cdDesc'] ?? '',
                'forEbm' => true,
               // 'use_yn' => $data['useYn'] ?? 'N',
            ];
        }

        PaymentMode::upsert($records, ['code'], ['name', 'description', 'forEbm']);
        Log::info('Payment modes updated', ['count' => count($records), 'records' => $records]);
    }

    public function upsertItemClassCode(): void
    {
        $response = Http::timeout(120)->post($this->itemClassUrl, $this->reqData);

        if ($response->failed()) {
            Log::error("failed fetching codes from ebm server", [
                "payload" => $this->reqData,
                "status" => $response->status(),
                "response" => $response->json() ?? $response->body(),
                "url" => $this->itemClassUrl,
            ]);
            return;
        }

        $responseData = $response->json();
        if (!$responseData || !in_array($responseData['resultCd'] ?? null, ['000', '001'], true)) {
            Log::error("invalid result code while fetching codes", [
                "payload" => $this->reqData,
                "response" => $responseData,
            ]);
            return;
        }

        $itemClsList = $responseData['data']['itemClsList'] ?? [];
        if (empty($itemClsList)) {
            Log::info("no item classes found");
            return;
        }

        $classCodes = collect($itemClsList);
        $records = [];
        $batchSize = 500; // DV - we are using chunk of 500, because class exceeds MySQL prepared statement limit

        foreach ($classCodes as $item) {
            $records[] = [
                'code' => $item['itemClsCd'] ?? '',
                'name' => $item['itemClsNm'] ?? '',
                // Uncomment and adjust as needed:
                // 'code_level' => $item['itemClsLvl'] ?? '',
                // 'product_tax_type_id' => $productTaxType ? $productTaxType->id : null,
                // 'use_yn' => $item['useYn'] ?? 'N',
                // 'mjr_tg_yn' => $item['mjrTgYn'] ?? 'N',
            ];
        }

        foreach (array_chunk($records, $batchSize) as $batch) {
            ProductClass::upsert(
                $batch,
                ['code'],
                ['name']
            );
        }
    }

    /**
     * Validate the HTTP response and return parsed JSON data if valid.
     *
     * @param \Illuminate\Http\Client\Response $response
     * @param string $context
     * @return array|null
     */
    protected function validateResponse($response, string $context): ?array
    {
        if ($response->failed()) {
            Log::error("Failed fetching codes from EBM server for {$context}", [
                'payload' => $this->reqData,
                'status' => $response->status(),
                'response' => $response->json() ?? $response->body(),
            ]);
            return null;
        }

        $responseData = $response->json();
        if (!$responseData || !is_array($responseData) || !in_array($responseData['resultCd'] ?? null, ['000', '001'], true)) {
            Log::error("Invalid or unexpected result code while fetching codes for {$context}", [
                'payload' => $this->reqData,
                'response' => $responseData ?? $response->body(),
            ]);
            return null;
        }

        return $responseData;
    }
}