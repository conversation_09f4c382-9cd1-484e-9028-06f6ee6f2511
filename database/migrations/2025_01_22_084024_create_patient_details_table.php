<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('PatientDetails', function (Blueprint $table) {
            $table->id();
            $table->foreignId('party_id')->references('id')->on('CompanyParty')->onDelete('restrict');
            $table->foreignId('insurance_id')->references('id')->on('Insurance')->onDelete('restrict');
            $table->boolean('hasAffiliation')->default(false);
            $table->string('code')->nullable();
            $table->string('affiliationNumber')->nullable();
            $table->string('affiliateLastName')->nullable();
            $table->string('affiliateFirstName')->nullable();
            $table->string('relationship')->nullable();
            $table->string('beneficiaryLastName')->nullable();
            $table->string('beneficiaryFirstName')->nullable();
            $table->bigInteger('dateOfBirth')->nullable();
            $table->enum('gender', ['Male', 'Female', 'Other'])->default('Male');
            $table->string('affiliationLocation')->nullable();
            $table->string('beneficiaryNumber')->nullable();
            $table->bigInteger('percentage')->nullable();
            $table->bigInteger('expirationDate')->nullable();
            $table->string('department')->nullable();
            $table->enum('status', ['Activated', 'Deactivated'])->default('Activated');
            $table->string('pin')->nullable();
            $table->string('globalInezaId')->nullable();
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('PatientDetails');
    }
};
