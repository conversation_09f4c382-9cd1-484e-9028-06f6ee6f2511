<?php

namespace Database\Factories;

use App\Enums\TransactionTypeEnums;
use App\Models\CompanyBranch;
use App\Models\CompanyParty;
use App\Models\PaymentMode;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_party_id' => CompanyParty::factory(),
            'branch_id' => CompanyBranch::factory(),
            'user_id' => User::factory(),
            'invoiceNumber' => $this->faker->numberBetween(1000, 9000),
            'clientTin' => $this->faker->numberBetween(1000, 9000),
            'clientName' => $this->faker->name,
            'clientPhoneNumber' => $this->faker->phoneNumber,
            'salesTypeCode' => $this->faker->word,
            'receiptTypeCode' => $this->faker->word,
            'paymentTypeCode' => PaymentMode::factory()->create()->code,
            'salesStatusCode' => $this->faker->word,
            'confirmationDate' => strtotime(Carbon::now()),
            'type' => $this->faker->word,
            'salesDate' => strtotime(Carbon::now()),
            'remarks' => $this->faker->text,
            'synced' => $this->faker->boolean,
            'taxblAmtA' => $this->faker->randomFloat(2, 0, 1000),
            'taxblAmtB' => $this->faker->randomFloat(2, 0, 1000),
            'taxblAmtC' => $this->faker->randomFloat(2, 0, 1000),
            'taxblAmtD' => $this->faker->randomFloat(2, 0, 1000),
            'taxAmtA' => $this->faker->randomFloat(2, 0, 1000),
            'taxAmtB' => $this->faker->randomFloat(2, 0, 1000),
            'taxAmtC' => $this->faker->randomFloat(2, 0, 1000),
            'taxAmtD' => $this->faker->randomFloat(2, 0, 1000),
            'totTaxblAmt' => $this->faker->randomFloat(2, 0, 1000),
            'totTaxAmt' => $this->faker->randomFloat(2, 0, 1000),
            'totAmt' => $this->faker->randomFloat(2, 0, 1000),
            'demo' => false,
            "transactionType" => TransactionTypeEnums::SALES->value,
        ];
    }
}
