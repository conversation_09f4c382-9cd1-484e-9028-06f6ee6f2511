<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StockTracking extends Model
{

    use HasFactory;
    protected $dateFormat = 'U';

    protected $table = 'StockTrack';

    protected $fillable = [
        'branch_id',
        'trackingNumber'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
