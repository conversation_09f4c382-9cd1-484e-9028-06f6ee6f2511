<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm } from '@inertiajs/vue3';
import FormSection from '@/Components/FormSection.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';

const props = defineProps({
    Company: Object,
    Insurance: Array,
});

const form = useForm({
    name: props.Company?.name || '',
    email: props.Company?.email || '',
    phone: props.Company?.phone || '',
    address: props.Company?.address || '',
    tin: props.Company?.tin || '',
    supportedInsurance: Array.isArray(props.Company?.insurance)
        ? props.Company.insurance
              .map(companyIns => {
                  const matchingInsurance = props.Insurance.find(ins => ins.code === companyIns.code);
                  if (!matchingInsurance) {
                      console.warn(`No matching insurance found for code: ${companyIns.code}`);
                      return null;
                  }
                  return { id: matchingInsurance.id };
              })
              .filter(ins => ins !== null)
        : [],
});

console.log('Props:', {
    companyInsurance: props.Company?.insurance,
    allInsurance: props.Insurance,
    supportedInsurance: form.supportedInsurance,
});

function updateCompany() {
    form.put(route('company.update', props.Company.id), {
        onError: (errors) => {
            console.error('Form submission errors:', errors);
        },
    });
}

function toggleInsurance(id) {
    if (!props.Insurance.some(ins => ins.id === id)) {
        console.warn(`Invalid insurance ID: ${id}`);
        return;
    }

    if (form.supportedInsurance.some(ins => ins.id === id)) {
        form.supportedInsurance = form.supportedInsurance.filter(ins => ins.id !== id);
    } else {
        form.supportedInsurance = [...form.supportedInsurance, { id }];
    }
}
</script>

<template>
    <AppLayout title="Update your company">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Update your company
            </h2>
        </template>
        <FormSection>
            <template #title>
                Update <span class="font-bold">{{ Company.name }}</span>
            </template>

            <template #form>
                <form @submit.prevent="updateCompany">
                    <div class="pb-4">
                        <InputLabel for="name" value="Name" />
                        <TextInput id="name" v-model="form.name" type="text" />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>
                    <div class="grid grid-cols-6 gap-6">
                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="email" value="Email" />
                            <TextInput id="email" v-model="form.email" type="email" />
                            <InputError class="mt-2" :message="form.errors.email" />
                        </div>
                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="phone" value="Phone" />
                            <TextInput id="phone" v-model="form.phone" type="text" />
                            <InputError class="mt-2" :message="form.errors.phone" />
                        </div>
                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="tin" value="TIN" />
                            <TextInput id="tin" v-model="form.tin" type="text" />
                            <InputError class="mt-2" :message="form.errors.tin" />
                        </div>
                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="address" value="Address" />
                            <TextInput id="address" v-model="form.address" type="text" />
                            <InputError class="mt-2" :message="form.errors.address" />
                        </div>
                    </div>

                    <p class="pt-8 font-bold">
                        Supported Insurance
                    </p>

                    <div v-for="insurance in Insurance" :key="insurance.id"
                         class="flex items-center pl-4 rounded border border-gray-300 w-full mt-2">
                        <input :id="insurance.name" :value="insurance.id" type="checkbox"
                               :checked="form.supportedInsurance.some(ins => ins.id === insurance.id)"
                               @change="toggleInsurance(insurance.id)"
                               class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2 rounded-lg" />
                        <label :for="insurance.name" class="py-4 ml-2 w-full text-sm font-medium text-black p-2">
                            {{ insurance.name }}
                        </label>
                        <img :src="insurance.image" :alt="insurance.name" class="w-auto h-12 py-1 mx-4" />
                    </div>
                    <InputError class="mt-2" :message="form.errors.supportedInsurance" />

                    <div class="flex justify-end mt-4">
                        <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                            Save
                        </PrimaryButton>
                    </div>
                </form>
            </template>
        </FormSection>

        <template #footer />
    </AppLayout>
</template>