<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('Expense', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->references('id')->on('ExpenseCategory')->onDelete('restrict');
            $table->foreignId('branch_id')->references('id')->on('CompanyBranch')->onDelete('restrict');
            $table->foreignId('payment_mode_id')->references('id')->on('PaymentMode')->onDelete('restrict');
            $table->decimal('amount', 14);
            $table->text('description')->nullable();
            $table->bigInteger('date');
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('Expense');
    }
};
