<?php

use Database\Seeders\ProductCategorySeeder;
use Database\Seeders\BranchCategorySeeder;
use Database\Seeders\CompanyPartySeeder;
use Database\Seeders\PartyTypeSeeder;
use Database\Seeders\ProductClassSeeder;
use Database\Seeders\ProductTypeSeeder;

uses(
  Tests\TestCase::class,
  Illuminate\Foundation\Testing\RefreshDatabase::class,
)->beforeEach(function () {
  $this->refreshDatabase();
  $this->seed(
    [
      BranchCategorySeeder::class,
      ProductTypeSeeder::class,
      ProductCategorySeeder::class,
      ProductClassSeeder::class,
      PartyTypeSeeder::class,
      CompanyPartySeeder::class,
    ]
  );
})->in('Feature');
