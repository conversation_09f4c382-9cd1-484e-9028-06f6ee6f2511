<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle2, XCircle, Loader2 } from 'lucide-vue-next';
import { format } from 'date-fns';
import { computed } from 'vue';

const props = defineProps({
  Payment: Object,
});

// Format date from Unix timestamp to readable format
const formatPaymentDate = (timestamp) => {
  if (!timestamp) return 'N/A';
  return format(new Date(timestamp * 1000), 'MMMM d, yyyy HH:mm');
};

// Compute status icon based on payment status
const statusIcon = computed(() => {
  switch (props.Payment.status) {
    case 'successful':
      return { component: CheckCircle2, class: 'h-6 w-6 text-green-500' };
    case 'pending':
      return { component: Loader2, class: 'h-6 w-6 text-yellow-500 animate-spin' };
    case 'failed':
      return { component: XCircle, class: 'h-6 w-6 text-red-500' };
    default:
      return { component: null, class: '' };
  }
});
</script>

<template>
  <AppLayout title="Validate Payment">
    <template #header>
      <h2 class="font-semibold text-2xl text-zata-primary-dark leading-tight">
        Validate Payment
      </h2>
    </template>

    <div class="mt-8 max-w-3xl mx-auto">
      <Card class="bg-gray-100">
        <CardHeader class="border-b border-gray-300">
          <CardTitle class="text-xl text-zata-primary-dark">Payment Details</CardTitle>
        </CardHeader>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <p class="text-sm text-zata-secondary-dark uppercase tracking-wide">Payment ID</p>
              <p class="text-lg font-semibold text-zata-primary-dark">{{ Payment.id }}</p>
            </div>
            <div>
              <p class="text-sm text-zata-secondary-dark uppercase tracking-wide">User ID</p>
              <p class="text-lg font-semibold text-zata-primary-dark">{{ Payment.user_id }}</p>
            </div>
            <div>
              <p class="text-sm text-zata-secondary-dark uppercase tracking-wide">Amount</p>
              <p class="text-lg font-semibold text-zata-primary-dark">
                {{ $zataCurrency(Payment.amount) }} {{ Payment.currency }}
              </p>
            </div>
            <div>
              <p class="text-sm text-zata-secondary-dark uppercase tracking-wide">Gateway</p>
              <p class="text-lg font-semibold text-zata-primary-dark">{{ Payment.gateway }}</p>
            </div>
            <div>
              <p class="text-sm text-zata-secondary-dark uppercase tracking-wide">Plan</p>
              <p class="text-lg font-semibold text-zata-primary-dark">{{ Payment.plan }}</p>
            </div>
            <div>
              <p class="text-sm text-zata-secondary-dark uppercase tracking-wide">Date</p>
              <p class="text-lg font-semibold text-zata-primary-dark">{{ formatPaymentDate(Payment.date) }}</p>
            </div>
            <div>
              <p class="text-sm text-zata-secondary-dark uppercase tracking-wide">Status</p>
              <div class="flex items-center space-x-2">
                <component
                  v-if="statusIcon.component"
                  :is="statusIcon.component"
                  :class="statusIcon.class"
                />
                <Badge
                  :variant="Payment.status === 'successful' ? 'success' : Payment.status === 'pending' ? 'warning' : 'destructive'"
                >
                  {{ Payment.status.charAt(0).toUpperCase() + Payment.status.slice(1) }}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <template #sidenav />
    <template #footer />
  </AppLayout>
</template>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>