<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProductQuantityRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'quantity' => ['required', 'numeric', 'min:0'],
            'description' => ['nullable', 'string'],
            'movingUnit' => 'nullable|string|max:100|in:main,sub',
            'salePrice' => 'required|numeric|min:1',
            'purchasePrice' => 'nullable|numeric|min:1|lte:salePrice',
            'batchNumber' => 'nullable|string|max:100',
            'expireDate' => 'nullable|date|after:today',
            'discountRate' => 'nullable|min:0|max:100',
        ];
    }
}
