<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ProductCategory;

class ProductCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $productCategory = [
            ['name' => 'For Sale', 'srtOrd' => 1],
            ['name' => 'Raw material', 'srtOrd' => 2],
            ['name' => 'Own consumption', 'srtOrd' => 3],
            ['name' => 'Asset', 'srtOrd' => 4],
            ['name' => 'Expenses', 'srtOrd' => 5],
        ];

        foreach ($productCategory as $key => $value) {
            ProductCategory::factory()->create([
                'name' => $value['name'],
            ]);
        }
    }
}
