<?php

namespace Database\Factories;

use App\Enums\SubscriptionTypesEnums;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AccountSubscription>
 */
class AccountSubscriptionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'isActive' => true,
            'startDate' => strtotime(Carbon::parse(now())->format('Y-m-d h:i:s')),
            'endDate' => strtotime(Carbon::parse(now())->addMonth()->format('Y-m-d h:i:s')),
            'subscriptionType' => SubscriptionTypesEnums::BASIC->value,
            'billingPeriod' => 1,
            'price' => 0,
        ];
    }
}
