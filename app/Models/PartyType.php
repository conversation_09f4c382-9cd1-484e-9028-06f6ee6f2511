<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PartyType extends Model
{
    /** @use HasFactory<\Database\Factories\PartyTypeFactory> */
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'PartyType';

    protected $fillable = [
        'name',
        'company_id',
        'description',
        'isActive'
    ];

    public function scopeWhereCompanyId($query, int $companyId)
    {
        return $query->where(function ($query) use ($companyId) {
            $query->where('company_id', $companyId)
                ->orWhereNull('company_id');
        });
    }
}
