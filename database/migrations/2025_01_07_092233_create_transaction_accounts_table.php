<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('TransactionAccount', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->references('id')->on('CompanyBranch')->onDelete('restrict');
            $table->foreignId('payment_mode_id')->references('id')->on('PaymentMode')->onDelete('restrict');
            $table->foreignId('user_id')->references('id')->on('users')->onDelete('restrict');
            $table->decimal('amount', 14);
            $table->enum('type', ['credit', 'debit'])->default('credit');
            $table->string('sourceType', 20);
            $table->bigInteger('source_id')->unsigned();
            $table->bigInteger('date');
            $table->string('description')->nullable();
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('TransactionAccount');
    }
};
