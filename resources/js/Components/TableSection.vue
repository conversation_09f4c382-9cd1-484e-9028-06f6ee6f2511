<script setup>
import { reactive, watch } from 'vue';
import { useForm } from "@inertiajs/vue3";

const props = defineProps({
    headers: Array,
    rows: Array,
    currentPage: Number,
    lastPage: Number,
    itemsPerPage: Number,
    searchQuery: String,
    title: String,
    notFoundMessage: String,
    routeName: String,
    showSearch: { type: Boolean, default: true },
    showPagination: { type: Boolean, default: true },
    isDynamic: { type: Boolean, default: false },
    dynamicUrl : { type: String, default: null },

});

const paginationState = reactive({
    currentPage: props.currentPage,
    itemsPerPage: props.itemsPerPage,
    totalPages: props.lastPage,
});

const paginationForm = useForm({
    page: paginationState.currentPage,
    perPage: paginationState.itemsPerPage,
    searchQuery: props.searchQuery,
});

const handleSearchQueryChange = (event) => {
    const newQuery = event.target.value;
    paginationForm.searchQuery = newQuery;
    paginationState.currentPage = 1;
    updatePagination();
};

const handleItemsPerPageChange = () => {
    paginationState.currentPage = 1;
    updatePagination();
};

const prevPage = () => {
    if (paginationState.currentPage > 1) {
        paginationState.currentPage -= 1;
        updatePagination();
    }
};

const nextPage = () => {
    if (paginationState.currentPage < paginationState.totalPages) {
        paginationState.currentPage += 1;
        updatePagination();
    }
};

const updatePagination = () => {
    paginationForm.page = paginationState.currentPage;
    paginationForm.perPage = paginationState.itemsPerPage;

    paginationForm.get(route(props.routeName), {
        preserveState: true,
        preserveScroll: true,
    });
};

watch(
    () => [props.currentPage, props.lastPage],
    ([newCurrentPage, newLastPage]) => {
        paginationState.currentPage = newCurrentPage;
        paginationState.totalPages = newLastPage;
    }
);

watch(
    () => props.itemsPerPage,
    (newItemsPerPage) => {
        paginationState.itemsPerPage = newItemsPerPage;
    }
);

watch(
    () => props.searchQuery,
    (newQuery) => {
        paginationForm.searchQuery = newQuery;
        paginationState.currentPage = 1;
        updatePagination();
    }
);
</script>

<template>
    <div class="py-2">
        <div class="flex justify-between pb-3">
            <h3 class="text-xl font-semibold text-gray-800">{{ title }}</h3>
            <slot name="actions" />
        </div>

        <div v-if="showSearch || showPagination"
            class="flex flex-col md:flex-row sm:justify-between md:items-center mb-4">

            <div v-if="showSearch" class="mb-4 sm:mb-0">
                <input type="text" :value="props.searchQuery" placeholder="Search..."
                    class="w-full md:max-w-xs px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 transition duration-200"
                    @input="handleSearchQueryChange" />
            </div>

            <div v-if="showPagination" class="flex md:space-x-4 items-center">

                <div class="hidden sm:flex items-center space-x-2 px-4 py-1 rounded-lg border border-gray-200 hover:bg-gray-200">
                    <label for="itemsPerPage" class="text-sm font-medium text-gray-700">Items per page:</label>
                    <select v-model="paginationState.itemsPerPage" @change="handleItemsPerPageChange"
                        class="ml-2 mr-2 px-3 py-2 border-none focus:outline-none focus:ring-0 rounded-md w-16">
                        <option :value="5">5</option>
                        <option :value="10">10</option>
                        <option :value="20">20</option>
                        <option :value="50">50</option>
                        <option :value="100">100</option>
                    </select>
                </div>


                <div class="flex items-center space-x-2">
                    <button @click="prevPage" :disabled="paginationState.currentPage === 1"
                        class="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 disabled:opacity-50 transition duration-200">
                        Previous
                    </button>

                    <span class="text-sm text-gray-600">
                        Page {{ paginationState.currentPage }} of {{ paginationState.totalPages }}
                    </span>

                    <button @click="nextPage" :disabled="paginationState.currentPage === paginationState.totalPages"
                        class="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 disabled:opacity-50 transition duration-200">
                        Next
                    </button>
                </div>
            </div>
        </div>

        <div v-if="rows.length > 0"
            class="overflow-x-auto flex flex-col divide-y divide-gray-200 rounded-lg border border-gray-300"
            style="min-height: 500px;">
            <div class="flex-grow">
                <table class="min-w-full divide-y divide-gray-200 table-auto">
                    <thead class="bg-gray-100">
                        <tr>
                            <th v-for="(header, index) in headers" :key="index"
                                class="px-6 py-3 text-left text-sm font-semibold text-gray-600  tracking-wider">
                                {{ header }}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template v-if="!isDynamic">
                        <tr   v-for="(row, index) in rows" :key="index" class="hover:bg-gray-100">
                            <td v-for="(cell, cellIndex) in row" :key="cellIndex"
                                class="px-6 py-4 text-sm font-medium text-gray-900">
                                <img v-if="typeof cell === 'string' && (cell.startsWith('http') || /\.(jpg|jpeg|png|gif)$/i.test(cell))"
                                    :src="cell" alt="" class="w-10 object-cover rounded-full" />
                                <span v-else>{{ cell !== null && cell !== undefined ? cell : '-' }}</span>
                            </td>
                        </tr>
                        </template>
                        <template v-else>
                        <tr  v-for="(row, index) in rows" :key="index" class="hover:bg-gray-100 cursor-pointer"
                            @click="$inertia.get(route(dynamicUrl, row.id))">
                            <td v-for="(cell, cellIndex) in row" :key="cellIndex"
                                class="px-6 py-4 text-sm font-medium text-gray-900">
                                <img v-if="typeof cell === 'string' && (cell.startsWith('http') || /\.(jpg|jpeg|png|gif)$/i.test(cell))"
                                    :src="cell" alt="" class="w-10 object-cover rounded-full" />
                                <span v-else>{{ cell !== null && cell !== undefined ? cell : '-' }}</span>
                            </td>
                        </tr>
                    </template>
                    </tbody>
                </table>
            </div>
        </div>
        <div v-else class="mt-5 text-center text-gray-500 py-56 rounded-lg border border-gray-300">
            {{ notFoundMessage }}
        </div>
    </div>
</template>
