APP_NAME="Zata EBM Main"
APP_ENV=testing
APP_KEY=base64:UA4Y34fqMsAr8czoda8kvhvzkKR33kZQ7DE3AggujXo=
APP_DEBUG=true
APP_URL=http://localhost
APP_INSTACE_NAME=zata_ebm_main
APP_PORT=4000
OCTANE_SERVER=roadrunner

LOG_CHANNEL=stack
#LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug
SCOUT_DRIVER=database


#Github actions
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=zata_ebm_main
DB_USERNAME=user
DB_PASSWORD=password

# Sail: Uncomment this when running locally
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=testing
DB_USERNAME=sail
DB_PASSWORD=password

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=database
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

TWILIO_SID=""
TWILIO_AUTH_TOKEN=""
TWILIO_NUMBER=""

CONTABO_ACCESS_KEY_ID=""
CONTABO_SECRET_ACCESS_KEY=""
CONTABO_DEFAULT_REGION=""
CONTABO_BUCKET=""
CONTABO_USE_PATH_STYLE_ENDPOINT=false

L5_SWAGGER_GENERATE_ALWAYS=true

LOG_SLACK_WEBHOOK_URL=""

SCOUT_DRIVER=database

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URL=
VERIFIED_REDIRECTION_URL=
