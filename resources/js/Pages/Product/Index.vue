<script setup>
import AppLayout from "@/Layouts/AppLayout.vue";
import { ref, computed, reactive, watch } from "vue";
import { useForm } from "@inertiajs/vue3";
import { Link } from "@inertiajs/vue3";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { debounce } from 'lodash'; 

const props = defineProps({
    Products: Object,
    currentPage: Number,
    lastPage: Number,
    itemsPerPage: Number,
    searchQuery: String,
});

const tableHeaders = ["id", "Image", "Name", "Tax", "Quantity"];

const tableRows = computed(() => {
    return props.Products.map((product) => ({
        id: product.id,
        image: product.image,
        name: product.name,
        tax: product.tax,
        quantity: product.totalStock,
    }));
});

const paginationState = reactive({
    currentPage: props.currentPage,
    itemsPerPage: props.itemsPerPage,
    totalPages: props.lastPage,
});

const paginationForm = useForm({
    page: paginationState.currentPage,
    perPage: paginationState.itemsPerPage,
    searchQuery: props.searchQuery,
});

const handleSearchQueryChange = debounce((value) => {
    paginationForm.searchQuery = value;
    paginationState.currentPage = 1;
    updatePagination();
}, 300);

const handleItemsPerPageChange = (value) => {
    paginationState.itemsPerPage = value;
    paginationState.currentPage = 1;
    updatePagination();
};

const prevPage = () => {
    if (paginationState.currentPage > 1) {
        paginationState.currentPage -= 1;
        updatePagination();
    }
};

const nextPage = () => {
    if (paginationState.currentPage < paginationState.totalPages) {
        paginationState.currentPage += 1;
        updatePagination();
    }
};

const updatePagination = () => {
    paginationForm.page = paginationState.currentPage;
    paginationForm.perPage = paginationState.itemsPerPage;
    paginationForm.get(route('product.index'), {
        preserveState: true,
        preserveScroll: true,
    });
};

watch(() => [props.currentPage, props.lastPage], ([newCurrentPage, newLastPage]) => {
    paginationState.currentPage = newCurrentPage;
    paginationState.totalPages = newLastPage;
});

watch(() => props.itemsPerPage, (newItemsPerPage) => {
    paginationState.itemsPerPage = newItemsPerPage;
});

watch(() => props.searchQuery, (newQuery) => {
    paginationForm.searchQuery = newQuery;
});
</script>

<template>
    <AppLayout title="Products">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Products
            </h2>
        </template>

        <div class="py-2">
            <div class="flex justify-between pb-3">
                <h3 class="text-xl font-semibold text-gray-800">Products</h3>
                <div class="space-x-2">
                    <Button as-child>
                        <Link :href="route('product.new')">
                        Add new
                        </Link>
                    </Button>
                    <Button as-child>
                        <Link :href="route('product.syncEbm')">
                        Sync EBM
                        </Link>
                    </Button>
                </div>
            </div>

            <div class="flex flex-col md:flex-row justify-between items-center mb-4 space-y-4 md:space-y-0">
                <!-- Use v-model instead of :value and @input -->
                <Input v-model="paginationForm.searchQuery" placeholder="Search..." class="w-full md:w-64"
                    @update:modelValue="handleSearchQueryChange" />

                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-600">Items per page:</span>
                        <Select v-model="paginationState.itemsPerPage" @update:modelValue="handleItemsPerPageChange">
                            <SelectTrigger class="w-20">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="5">5</SelectItem>
                                <SelectItem value="10">10</SelectItem>
                                <SelectItem value="20">20</SelectItem>
                                <SelectItem value="50">50</SelectItem>
                                <SelectItem value="100">100</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div class="flex items-center space-x-2">
                        <Button variant="outline" @click="prevPage" :disabled="paginationState.currentPage === 1">
                            Previous
                        </Button>
                        <span class="text-sm text-gray-600">
                            Page {{ paginationState.currentPage }} of {{ paginationState.totalPages }}
                        </span>
                        <Button variant="outline" @click="nextPage"
                            :disabled="paginationState.currentPage === paginationState.totalPages">
                            Next
                        </Button>
                    </div>
                </div>
            </div>

            <div v-if="tableRows.length > 0" class="rounded-lg border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead v-for="header in tableHeaders" :key="header" class="font-semibold">
                                {{ header }}
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="row in tableRows" :key="row.id" class="hover:bg-muted cursor-pointer"
                            @click="$inertia.get(route('product.show', row.id))">
                            <TableCell v-for="(cell, index) in row" :key="index">
                                <img v-if="typeof cell === 'string' && (cell.startsWith('http') || /\.(jpg|jpeg|png|gif)$/i.test(cell))"
                                    :src="cell" alt="" class="w-10 object-cover rounded-full" />
                                <span v-else>{{ cell !== null && cell !== undefined ? cell : '-' }}</span>
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>

            <Card v-else class="mt-5">
                <CardContent class="text-center text-gray-500 py-56">
                    No products found.
                </CardContent>
            </Card>
        </div>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>