<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm } from "@inertiajs/vue3";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const props = defineProps({
  Branch: Object,
  companyId: Number,
});

const form = useForm({
  name: props.Branch.name,
  email: props.Branch.email,
  phone: props.Branch.phone,
  address: props.Branch.address,
  topMessage: props.Branch.topMessage,
  bottomMessage: props.Branch.bottomMessage,
  deviceSerial: props.Branch.deviceSerial,
});

function createNewCompanyBranch() {
  form.put(route('branch.update', props.Branch.id));
}
</script>

<template>
  <AppLayout title="Edit Branch">
    <template #header>
      <h2 class="font-semibold text-2xl text-gray-900 leading-tight">
        Edit Branch
      </h2>
    </template>

    <div class="py-6 px-4 sm:px-6 lg:px-8">
      <Card class=" border max-w-3xl mx-auto">
        <CardHeader class="bg-gray-50 border-b">
          <CardTitle class="text-xl font-semibold text-gray-800">
            Edit <span class="font-bold">{{ Branch.name }}</span>
          </CardTitle>
        </CardHeader>
        <CardContent class="p-6">
          <form @submit.prevent="createNewCompanyBranch" class="space-y-6">
            <div class="space-y-2">
              <Label for="name">Name</Label>
              <Input id="name" v-model="form.name" type="text" />
              <p v-if="form.errors.name" class="text-red-500 text-sm mt-1">{{ form.errors.name }}</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <Label for="email">Email</Label>
                <Input id="email" v-model="form.email" type="email" />
                <p v-if="form.errors.email" class="text-red-500 text-sm mt-1">{{ form.errors.email }}</p>
              </div>
              <div class="space-y-2">
                <Label for="phone">Phone</Label>
                <Input id="phone" v-model="form.phone" type="text" />
                <p v-if="form.errors.phone" class="text-red-500 text-sm mt-1">{{ form.errors.phone }}</p>
              </div>
            </div>

            <div class="space-y-2">
              <Label for="company_address">Address</Label>
              <Input id="company_address" v-model="form.address" type="text" />
              <p v-if="form.errors.address" class="text-red-500 text-sm mt-1">{{ form.errors.address }}</p>
            </div>

            <div class="space-y-2">
              <Label for="topMessage">Invoice Top Message</Label>
              <textarea
                id="topMessage"
                v-model="form.topMessage"
                maxlength="150"
                rows="4"
                class="w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-blue-200 focus:border-blue-500 p-2 text-sm"
              ></textarea>
              <div class="mt-1 text-sm text-gray-600">
                {{ form.topMessage ? form.topMessage.length : 0 }}/150 characters
              </div>
              <p v-if="form.errors.topMessage" class="text-red-500 text-sm mt-1">{{ form.errors.topMessage }}</p>
            </div>

            <div class="space-y-2">
              <Label for="bottomMessage">Invoice Bottom Message</Label>
              <textarea
                id="bottomMessage"
                v-model="form.bottomMessage"
                maxlength="150"
                rows="4"
                class="w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-blue-200 focus:border-blue-500 p-2 text-sm"
              ></textarea>
              <div class="mt-1 text-sm text-gray-600">
                {{ form.bottomMessage ? form.bottomMessage.length : 0 }}/150 characters
              </div>
              <p v-if="form.errors.bottomMessage" class="text-red-500 text-sm mt-1">{{ form.errors.bottomMessage }}</p>
            </div>


            <div class="flex justify-end">
              <Button 
                type="submit" 
                :disabled="form.processing"
                :class="{ 'opacity-25': form.processing }"
              >
                Save
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>

    <template #footer />
  </AppLayout>
</template>