<script setup>
import AppLayout from "@/Layouts/AppLayout.vue";
import { computed } from "vue";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";

const props = defineProps({
    Accounts: Array,
});

const tableHeaders = [
    "Name",
    "Code",
    "Debit Count",
    "Credit Count",
    "Debit Amount",
    "Credit Amount",
    "Total Difference",
];

const tableRows = computed(() => {
    return props.Accounts.map((account) => ({
        name: account.name,
        code: account.code,
        debitCount: account.debitCount,
        creditCount: account.creditCount,
        debitAmount: account.debitAmount,
        creditAmount: account.creditAmount,
        totalDifference: account.totalDifference,
    }));
});
</script>

<template>
    <AppLayout title="Finance">
        <template #header>
            <h2 class="font-semibold text-2xl text-gray-900 leading-tight">
                Finance Overview
            </h2>
        </template>

        <div class="py-6 px-4 sm:px-6 lg:px-8">
            <Card class="shadow-lg border border-gray-200">
                <CardHeader class="bg-gray-50 border-b">
                    <CardTitle class="text-xl font-semibold text-gray-800">
                        Account Summary
                    </CardTitle>
                </CardHeader>
                <CardContent class="p-0">
                    <div v-if="tableRows.length > 0" class="overflow-x-auto">
                        <Table class="w-full">
                            <TableHeader>
                                <TableRow class="bg-gray-100 hover:bg-gray-100">
                                    <TableHead
                                        v-for="header in tableHeaders"
                                        :key="header"
                                        class="font-semibold text-gray-700 py-4 px-6 text-left whitespace-nowrap"
                                    >
                                        {{ header }}
                                    </TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow
                                    v-for="(row, index) in tableRows"
                                    :key="row.code"
                                    class="border-b transition-colors hover:bg-gray-50"
                                >
                                    <TableCell
                                        class="py-4 px-6 text-gray-900"
                                        >{{ row.name }}</TableCell
                                    >
                                    <TableCell
                                        class="py-4 px-6 font-mono text-gray-700"
                                        >{{ row.code }}</TableCell
                                    >
                                    <TableCell
                                        class="py-4 px-6 text-gray-600"
                                        >{{ row.debitCount }}</TableCell
                                    >
                                    <TableCell
                                        class="py-4 px-6 text-gray-600"
                                        >{{ row.creditCount }}</TableCell
                                    >
                                    <TableCell
                                        class="py-4 px-6 text-green-600 font-medium"
                                    >
                                        {{
                                            row.debitAmount?.toLocaleString() ||
                                            0
                                        }}
                                    </TableCell>
                                    <TableCell
                                        class="py-4 px-6 text-red-600 font-medium"
                                    >
                                        {{
                                            row.creditAmount?.toLocaleString() ||
                                            0
                                        }}
                                    </TableCell>
                                    <TableCell
                                        class="py-4 px-6 font-medium"
                                        :class="{
                                            'text-blue-600':
                                                row.totalDifference > 0,
                                            'text-red-600':
                                                row.totalDifference < 0,
                                            'text-gray-600':
                                                row.totalDifference === 0,
                                        }"
                                    >
                                        {{
                                            row.totalDifference?.toLocaleString() ||
                                            0
                                        }}
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>
                    <div v-else class="p-6 text-center">
                        <p class="text-gray-500 text-lg">No accounts found</p>
                    </div>
                </CardContent>
            </Card>
        </div>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>
