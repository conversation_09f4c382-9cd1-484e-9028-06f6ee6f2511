<?php

namespace App\Services;

use App\Data\PaymentCallBackData;
use App\Data\PaySubscriptionData;
use App\Enums\SubscriptionTypesEnums;
use App\Exceptions\Forbidden;
use App\Exceptions\NotFound;
use App\Exceptions\ServerError;
use App\Models\AccountSubscription;
use App\Models\Company;
use App\Models\PaymentHistory;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Paypack\Paypack;
use App\Enums\SubscriptionActionEnums;

class AccountSubscriptionService
{


    public function create() {}

    public function storeSubscription(int $userId): void
    {
        AccountSubscription::create([
            'user_id' => $userId,
            'isActive' => true,
            'startDate' => strtotime(Carbon::parse(now())->format('Y-m-d h:i:s')),
            'subscriptionType' => SubscriptionTypesEnums::BASIC->value,
            'billingPeriod' => 1,
        ]);
    }


    public function getSubscription(int $userId)
    {

        $subscription = AccountSubscription::where('user_id', $userId)
            ->select('user_id', 'isActive', 'startDate', 'endDate', 'subscriptionType', 'billingPeriod')
            ->first();

        $companies = Company::where('user_id', $userId)
            ->select('id', 'name')
            ->with([
                'branches' => function ($query) {
                    $query->select('id', 'name', 'company_id')
                        ->withCount('orders')
                        ->withCount('products')
                        ->withSum('orders', 'totAmt');
                }
            ])
            ->get();

        $totalBranches = $companies->sum(function ($company) {
            return $company->branches->count();
        });

        return [
            'subscription' => $subscription ? [
                'user_id' => $subscription->user_id,
                'isActive' => $subscription->isActive,
                'startDate' => $subscription->startDate,
                'endDate' => $subscription->endDate,
                'subscriptionType' => $subscription->subscriptionType,
                'billingPeriod' => $subscription->billingPeriod,
            ] : null,
            'analytics' => [
                'total_companies' => $companies->count(),
                'total_branches' => $totalBranches,
                'companies' => $companies->map(function ($company) {
                    return [
                        'id' => $company->id,
                        'name' => $company->name,
                        'branches' => $company->branches->map(function ($branch) {
                            return [
                                'id' => $branch->id,
                                'name' => $branch->name,
                                'total_invoices' => $branch->orders_count,
                                'total_products' => $branch->products_count,
                                'total_amount_sold' => $branch->orders_sum_tot_amt ?? 0,
                            ];
                        })->toArray(),
                    ];
                })->toArray(),
            ],
        ];
    }

    public function paySubscription(PaySubscriptionData $data, int $userId): int
    {

        try {
            $paypackInstance = new  Paypack();
            $paypackInstance->config(['client_id' => env('PAYPACK_CLIENT_ID'), 'client_secret' => env('PAYPACK_CLIENT_SECRET'),]);
            $cashin = $paypackInstance->Cashin(['phone' => $data->phoneNumber, 'amount' => 100]);

            $payment =  PaymentHistory::create([
                'user_id' => $userId,
                'key' => $cashin['ref'],
                'amount' => $cashin['amount'],
                'currency' => 'RWF',
                'gateway' => 'Paypack',
                'plan' => 'Premium',
                'date' => strtotime(Carbon::parse(now())->format('Y-m-d h:i:s')),
                'status' => $cashin['status'],
                'provider' => $cashin['provider'],
                'kind' => $cashin['kind'],
            ]);

            return $payment->id;
        } catch (\Exception $e) {

            throw_if(true, ServerError::class, 'Something went wrong');
        }
    }

    /**
     * Handle Paypack webhook callback with security enhancements.
     *
     * @param PaymentCallBackData $data
     * @param int 
     * @param string
     * @param string|null
     * @return void
     * @throws Forbidden
     */
    public function zataCallback(PaymentCallBackData $data, int $userId, string $rawBody, ?string $signature): void
    {

        $this->verifySignature($rawBody, $signature);

        $this->enforceRateLimit();

        if ($this->isEventProcessed($data->event_id)) {
            Log::info('Webhook event already processed', ['event_id' => $data->event_id]);
            return;
        }

        $key = $data->data['ref'] ?? null;
        throw_if(is_null($key), Forbidden::class, 'Invalid transaction reference');

        $payment = PaymentHistory::where('key', $key)->first();
        throw_if(is_null($payment), Forbidden::class, 'Payment not found');

        if ($payment->status === 'pending' && ($data->data['status'] ?? '') === 'successful') {
            DB::transaction(function () use ($payment, $userId, $data) {

                $payment->update([
                    'status' => 'success',
                    'processed_at' => Carbon::now(),
                ]);

                $subscription = AccountSubscription::where('user_id', $userId)->firstOrFail();
                $subscription->update([
                    'isActive' => true,
                    'startDate' => Carbon::now()->startOfDay()->timestamp,
                    'endDate' => Carbon::now()->addMonth()->startOfDay()->timestamp,
                    'billingPeriod' => 1,
                    'subscriptionType' => SubscriptionTypesEnums::PREMIUM->value,
                ]);

                $this->recordEvent($data->event_id);
            });

            Log::info('Webhook processed successfully', [
                'event_id' => $data->event_id,
                'user_id' => $userId,
                'payment_key' => $key,
            ]);
        } else {
            Log::warning('Webhook skipped', [
                'event_id' => $data->event_id,
                'payment_status' => $payment->status,
                'transaction_status' => $data->data['status'] ?? 'unknown',
            ]);
        }
    }

    /**
     *
     * @param string $rawBody
     * @param string|null $signature
     * @return void
     * @throws Forbidden
     */
    private function verifySignature(string $rawBody, ?string $signature): void
    {
        $secret = config('services.paypack.webhook_secret');
        throw_if(empty($secret), Forbidden::class, 'Webhook secret not configured');

        if (empty($signature)) {
            Log::warning('Missing webhook signature');
            throw new Forbidden('Missing signature');
        }

        $expectedSignature = base64_encode(hash_hmac('sha256', $rawBody, $secret, true));
        if (!hash_equals($expectedSignature, $signature)) {
            Log::warning('Invalid webhook signature', ['provided' => $signature]);
            throw new Forbidden('Invalid signature');
        }
    }

    /**
     *
     * @return void
     * @throws Forbidden
     */
    private function enforceRateLimit(): void
    {
        $key = 'webhook:' . request()->ip();
        if (RateLimiter::tooManyAttempts($key, 100)) {
            Log::warning('Webhook rate limit exceeded', ['ip' => request()->ip()]);
            throw new Forbidden('Too many requests');
        }
        RateLimiter::hit($key, 60);
    }

    /**
     * @param string $eventId
     * @return bool
     */
    private function isEventProcessed(string $eventId): bool
    {
        return cache()->has("webhook_event:$eventId");
    }

    /**
     * @param string $eventId
     * @return void
     */
    private function recordEvent(string $eventId): void
    {
        cache()->put("webhook_event:$eventId", true, now()->addDays(7));
    }

    public function getPaymentById(int $paymentId, int $userId)
    {

        $payment = PaymentHistory::where('id', $paymentId)->where('user_id', $userId)
            ->select('id', 'user_id', 'amount', 'currency', 'gateway', 'plan', 'date', 'status')
            ->first();

        throw_if(is_null($payment), NotFound::class, 'Payment not found');

        return $payment;
    }


    /**
     * Validates and enforces subscription restrictions for specific actions.
     *
     * @param int $companyId
     * @param SubscriptionAction $action
     * @return bool
     * @throws Forbidden
     */
    public function restrict(int $companyId, SubscriptionActionEnums $action, ?int $branchId = null): bool
    {
        $company = Company::where('id', $companyId)
            ->with([
                'user.subscription',
                'branches' => function ($query) {
                    $query->withCount('products');
                },
                'roles'
            ])
            ->firstOrFail();

        $subscription = $company->user->subscription;
        if (!$subscription || !$subscription->isActive) {
            throw new Forbidden('No active subscription found. Please subscribe to continue.');
        }

        $plans = config('subscriptions');
        $plan = collect($plans)->firstWhere('name', $subscription->subscriptionType);

        if (!$plan) {
            throw new Forbidden('Invalid subscription plan configuration.');
        }

        if ($plan['name'] === SubscriptionTypesEnums::ENTERPRISE->value) {
            return true;
        }

        if ($plan['name'] === SubscriptionTypesEnums::PREMIUM->value) {

            $endData = $company->user->subscription->endDate;

            if ($endData < Carbon::now()) {
                throw new Forbidden('Subscription has expired. Please renew to continue.');
            }
        }
        switch ($action) {
            case SubscriptionActionEnums::CREATE_BRANCH:
                $currentBranches = $company->branches->count();
                $allowedBranches = $plan['allowed_branches'] === 'unlimited' ? PHP_INT_MAX : $plan['allowed_branches'];
                if ($currentBranches >= $allowedBranches) {
                    throw new Forbidden("Branch limit reached. Your {$plan['name']} plan allows {$allowedBranches} branches. Current: {$currentBranches}.");
                }
                break;

            case SubscriptionActionEnums::CREATE_PRODUCT:
                $branches = $company->branches;
                $allowedProducts = $plan['allowed_products_per_branch'] === 'unlimited' ? PHP_INT_MAX : $plan['allowed_products_per_branch'];
                foreach ($branches as $branch) {

                    $currentProducts = $branch->products_count;
                    if ($currentProducts >= $allowedProducts) {
                        if (!is_null($branchId)) {
                            throw_if($branchId == $branch->id, new Forbidden("Product limit reached for branch '{$branch->name}'. Your {$plan['name']} plan allows {$allowedProducts} products per branch. Current: {$currentProducts}."));
                        }
                    }
                }
                break;

            case SubscriptionActionEnums::CREATE_USER:
                $currentUsers = $company->roles->unique('user_id')->count();
                $allowedUsers = $plan['allowed_users'] === 'unlimited' ? PHP_INT_MAX : $plan['allowed_users'];
                if ($currentUsers >= $allowedUsers) {
                    throw new Forbidden("User limit reached. Your {$plan['name']} plan allows {$allowedUsers} users. Current: {$currentUsers}.");
                }
                break;

            case SubscriptionActionEnums::ACCESS_API:
                if (!$plan['api_access']) {
                    throw new Forbidden("API access is not available in your {$plan['name']} plan.");
                }
                break;

            case SubscriptionActionEnums::USE_EBM:
                if (!$plan['ebm']) {
                    throw new Forbidden("EBM feature is not available in your {$plan['name']} plan.");
                }
                break;

            case SubscriptionActionEnums::USE_CUSTOM_PLUGINS:
                if (!$plan['custom_plugins']) {
                    throw new Forbidden("Custom plugins are not available in your {$plan['name']} plan.");
                }
                break;

            case SubscriptionActionEnums::USE_AI_ASSISTANT:
                if (!$plan['ai_assistant']) {
                    throw new Forbidden("AI assistant is not available in your {$plan['name']} plan.");
                }
                break;

            case SubscriptionActionEnums::SUBMIT_FEATURE_REQUESTS:
                if (!$plan['feature_requests']) {
                    throw new Forbidden("Feature requests are not available in your {$plan['name']} plan.");
                }
                break;

            default:
                throw new Forbidden('Invalid action specified for subscription validation.');
        }

        return true;
    }
}
