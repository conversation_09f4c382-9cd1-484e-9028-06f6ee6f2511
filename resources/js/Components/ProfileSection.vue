<script setup>

defineProps({
    image: {
        type: String,
        required: true
    },

    title: {
        type: String,
        required: true
    },

    description: {
        type: String,
        required: false
    },
})
</script>

<template>


    <div class="flex items-center gap-4  pb-2">
        <div class="w-20 h-20 flex-shrink-0">
            <img :src="image" alt="Product Image"
                class="w-full h-full object-cover rounded-full border border-gray-200 shadow" />
        </div>

        <div class="flex-grow">
            <h1 class="text-2xl font-bold text-gray-800">{{ title }}</h1>
            <p class="text-sm text-gray-600">{{ description }}</p>
        </div>
    </div>

</template>