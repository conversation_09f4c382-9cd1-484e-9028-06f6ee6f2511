<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { reactive, watch } from 'vue';
import { useForm } from '@inertiajs/vue3';
import { Link } from '@inertiajs/vue3';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';

const props = defineProps({
    Purchases: Object,
    currentPage: Number,
    lastPage: Number,
    itemsPerPage: Number,
    total: Number,
    pageItems: Number,
});

const paginationState = reactive({
    currentPage: props.currentPage,
    itemsPerPage: props.itemsPerPage,
    totalPages: props.lastPage,
});

const paginationForm = useForm({
    page: paginationState.currentPage,
    perPage: paginationState.itemsPerPage,
});

const handleItemsPerPageChange = (value) => {
    paginationState.itemsPerPage = parseInt(value);
    paginationState.currentPage = 1;
    updatePagination();
};

const prevPage = () => {
    if (paginationState.currentPage > 1) {
        paginationState.currentPage -= 1;
        updatePagination();
    }
};

const nextPage = () => {
    if (paginationState.currentPage < paginationState.totalPages) {
        paginationState.currentPage += 1;
        updatePagination();
    }
};

const updatePagination = () => {
    paginationForm.page = paginationState.currentPage;
    paginationForm.perPage = paginationState.itemsPerPage;
    paginationForm.get(route('purchase.index'), {
        preserveState: true,
        preserveScroll: true,
    });
};

watch(() => [props.currentPage, props.lastPage, props.itemsPerPage], ([newCurrentPage, newLastPage, newItemsPerPage]) => {
    paginationState.currentPage = newCurrentPage;
    paginationState.totalPages = newLastPage;
    paginationState.itemsPerPage = newItemsPerPage;
});
</script>

<template>
    <AppLayout title="Purchases">
        <template #header>
            <h2 class="text-xl font-semibold text-gray-900">Purchases</h2>
        </template>

        <div class="max-w-6xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between mb-4">
                <div class="text-sm text-gray-600">
                    Showing {{ props.pageItems }} of {{ props.total }} items
                </div>
                <Button as-child >
                    <Link :href="route('purchase.sync')">Sync Purchases</Link>
                </Button>
            </div>

            <div v-if="props.Purchases.length > 0" class="border border-gray-100 rounded-lg">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead class="w-16">ID</TableHead>
                            <TableHead class="w-1/3">Supplier</TableHead>
                            <TableHead>Invoice No</TableHead>
                            <TableHead>Total</TableHead>
                            <TableHead>Items</TableHead>

                            <TableHead> Status</TableHead>
                            <TableHead class="w-20">Action</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="purchase in props.Purchases" :key="purchase.id">
                            <TableCell>{{ purchase.id }}</TableCell>
                            <TableCell class="whitespace-normal">{{ purchase.spplrNm || '-' }}</TableCell>
                            <TableCell>{{ purchase.spplrInvcNo || '-' }}</TableCell>
                            <TableCell>{{ purchase.totAmt }}</TableCell>
                            <TableCell>{{ purchase.totItemCnt }}</TableCell>
                            <TableCell>
                                <span v-if="purchase.status === 'Pending'">Pending</span>
                                <span v-else-if="purchase.status === 'Approved'" class="text-green-600">Approved</span>
                                <span v-else class="text-red-600">Rejected</span>
                            </TableCell>
                            <TableCell>
                                <Link v-if="purchase.status === 'Pending'" :href="route('purchase.show', purchase.id)"
                                    class="text-zata-primary-light text-sm underline">
                                Update
                                </Link>
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>

            <Card v-else class="mt-4 border-none">
                <CardContent class="text-center text-gray-500 py-12 text-sm">
                    No purchases found.
                </CardContent>
            </Card>

            <div class="flex items-center justify-between mt-4">
                <div class="flex items-center space-x-2">
                    <Select v-model="paginationState.itemsPerPage" @update:modelValue="handleItemsPerPageChange">
                        <SelectTrigger class="h-8 w-16 text-sm">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="5">5</SelectItem>
                            <SelectItem value="10">10</SelectItem>
                            <SelectItem value="20">20</SelectItem>
                        </SelectContent>
                    </Select>
                    <span class="text-sm text-gray-600">per page</span>
                </div>
                <div class="flex items-center space-x-2">
                    <Button variant="outline" @click="prevPage" :disabled="paginationState.currentPage === 1"
                        class="h-8 px-3 text-sm">
                        Prev
                    </Button>
                    <span class="text-sm text-gray-600">
                        {{ paginationState.currentPage }} of {{ paginationState.totalPages }}
                    </span>
                    <Button variant="outline" @click="nextPage"
                        :disabled="paginationState.currentPage === paginationState.totalPages" class="h-8 px-3 text-sm">
                        Next
                    </Button>
                </div>
            </div>
        </div>
        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>

<style scoped>
.table :deep(.table-row) {
    @apply border-b border-gray-100;
}

.table :deep(.table-cell) {
    @apply py-2 text-sm;
}

.table :deep(.table-head) {
    @apply bg-gray-50 text-gray-700 text-xs font-semibold;
}
</style>