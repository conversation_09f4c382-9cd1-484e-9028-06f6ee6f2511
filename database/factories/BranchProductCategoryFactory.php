<?php

namespace Database\Factories;

use App\Models\BranchCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BranchProductCategory>
 */
class BranchProductCategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            //
            'branch_category_id' => BranchCategory::factory(),
            'name' => $this->faker->word,
            'slug' =>  $this->faker->word,
            'isCommon' => true,
        ];
    }
}
