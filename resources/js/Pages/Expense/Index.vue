<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link, useForm } from "@inertiajs/vue3";
import { ref, watch, reactive, computed } from 'vue';
import PrimaryLink from '@/Components/PrimaryLink.vue';
import TableSection from '@/Components/TableSection.vue';

const props = defineProps({
    Expenses: Object,
    currentPage: Number,
    lastPage: Number,
    itemsPerPage: Number,
    pageItems: Number,
    total: Number,
    searchQuery: String,
});

const tableHeaders = ["id", "Description", "Payment option", "Amount", "Date"];
const tableRows = computed(() => {
    return props.Expenses.map((expense) => ({
        id: expense.id,
        description: expense.description,
        payment_option: expense.payment_mode.name,
        amount: expense.amount,
        date: expense.date,
    }));
});


</script>

<template>
    <AppLayout title="Expense">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Expense
            </h2>
        </template>
        <div>
            <TableSection :headers="tableHeaders" :rows="tableRows" :current-page="props.currentPage"
                :last-page="props.lastPage" :items-per-page="props.itemsPerPage" :search-query="props.searchQuery"
                title="Expense" notFoundMessage="No expense found." :route-name="'expense.index'" :is-dynamic="true" :dynamic-url="'expense.show'">
                >
                <template #actions>
                    <PrimaryLink :href="route('expense.new')">
                        Add new
                    </PrimaryLink>
                </template>
            </TableSection>
        </div>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>