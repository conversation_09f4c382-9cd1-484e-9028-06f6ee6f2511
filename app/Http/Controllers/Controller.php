<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Pagination\LengthAwarePaginator;
use OpenApi\Attributes as OA;

#[OA\Info(
    version: "1.0",
    title: "Zata EBM Service API",
    description: "Zata Point Global EBM Service API",
    contact: new OA\Contact(
        name: "NIYIBIZI HIRWA",
        email: "<EMAIL>",
    )
)]

abstract class Controller
{
    //
    use AuthorizesRequests, ValidatesRequests;

    public function jsonResponseWithPagination(LengthAwarePaginator $lengthAwarePaginator)
    {
        $template = [
            "data" => $lengthAwarePaginator->getCollection(),
            "currentPage" => $lengthAwarePaginator->currentPage(),
            "lastPage" => $lengthAwarePaginator->lastPage(),
            "itemsPerPage" => $lengthAwarePaginator->perPage(),
            "pageItems" => $lengthAwarePaginator->count(),
            "total" => $lengthAwarePaginator->total(),
            "timestamp" => now()->format("Y-m-d, H:i:s")
        ];

        return \response()->json($template);
    }

    public function pagination(LengthAwarePaginator $lengthAwarePaginator)
    {
       return [
            "data" => $lengthAwarePaginator->getCollection(),
            "currentPage" => $lengthAwarePaginator->currentPage(),
            "lastPage" => $lengthAwarePaginator->lastPage(),
            "itemsPerPage" => $lengthAwarePaginator->perPage(),
            "pageItems" => $lengthAwarePaginator->count(),
            "total" => $lengthAwarePaginator->total()
        ];

    }
}
