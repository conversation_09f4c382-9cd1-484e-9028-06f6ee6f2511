<script setup>

defineProps({

    title: {
        type: String,
        required: false,
        default: 'Additional Details'
    },

    details: {
        type: Array,
        required: true
    }
})
</script>
<template>

    <div class="mt-8">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b border-b-gray-200 pb-2">
            {{ title }}
        </h3>

        <ul class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-sm text-gray-600">
            <li class="" v-for="( data, index) in details" :key="index">
                <p> {{ data.title }}</p>
                <p class="font-bold">{{ data.value }}</p>
            </li>
        </ul>
    </div>

</template>