<?php

namespace App\Http\Controllers\Api;

use App\Data\Api\NewCompanyData;
use App\Data\UpdateCompanyData;
use App\Exceptions\Forbidden;
use App\Services\CompanyService;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\NewCompanyRequest;
use App\Http\Requests\UpdateCompanyRequest;
use OpenApi\Attributes as OA;

class CompanyController extends Controller
{

    protected $user;

    public function __construct(protected CompanyService $companyService,)
    {

        $this->user = Auth::user();
    }

    #[OA\Put(
        path: "/api/v1/company",
        description: "Update company details",
        security: [["authentication" => []]],
        summary: "company update",
        tags: ["company"]
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\RequestBody(
        description: "Order Details",
        required: true,
        content: new OA\JsonContent(
            example: [
                "name" => "Zata Point Global Service",
                "address" => "No 1, Zata Point Street, Zata Point",
                "phone" => "08012345678",
                "email" => "<EMAIL>",
                "tin" => "*********",
            ]
        )
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                "message" => "Company updated successfully",
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function update(UpdateCompanyRequest $request)
    {

        $company = companyData();
        $data = UpdateCompanyData::from($request->validated());

        $this->companyService->updateCompany($data, $this->user->id, $company->id);

        return response()->json(['message' => 'Company updated successfully']);
    }

    #[OA\Post(
        path: "/api/v1/company",
        description: "Create company",
        security: [["authentication" => []]],
        summary: "company store",
        tags: ["company"]
    )]
    #[OA\RequestBody(
        description: "Order Details",
        required: true,
        content: new OA\JsonContent(
            example: [
                "name" => "Zata Point Global Service",
                "address" => "No 1, Zata Point Street, Zata Point",
                "phone" => "08012345678",
                "email" => "<EMAIL>",
                "tin" => "*********",
            ]
        )
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                "message" => "Company created successfully",
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function create(NewCompanyRequest $request)
    {

        $data = NewCompanyData::from($request->validated());

        $this->companyService->storeCompany($data, $this->user->id);

        return response()->json(['message' => 'Company created successfully']);
    }

    #[OA\Get(
        path: "/api/v1/company/{companyId}",
        description: "Get compaby by id",
        security: [["authentication" => []]],
        summary: "company by id",
        tags: ["company"]
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\PathParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                'id' => 1,
                'name' => 'Zata Point Global Service',
                'address' => 'No 1, Zata Point Street, Zata Point',
                'phone' => '08012345678',
                'email' => '<EMAIL>',
                'tin' => '*********',
                'updated_at' => '2021-09-09, 12:00:00',
                'insurance' =>
                [
                    'name' => 'RSSB',
                    'code' => '*********',
                    'rate' => 90,
                ],
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function show(int $companyId)
    {

        $company = companyData();
        throw_if($company->id !== $companyId, Forbidden::class, 'Forbidden Action');
        $response =  $this->companyService->getCompanyById($this->user->id, $company->id);
        return response()->json($response);
    }
    #[OA\Get(
        path: "/api/v1/company",
        description: "Get All company",
        security: [["authentication" => []]],
        summary: "all company",
        tags: ["company"]
    )]
    #[OA\QueryParameter(
        name: "perPage",
        description: "Number of items per page",
        required: false,
        example: 100
    )]
    #[OA\QueryParameter(
        name: "page",
        description: "Page number",
        required: false,
        example: 1
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                'data' => [
                    [
                        'id' => 1,
                        'name' => 'Zata Point Global Service',
                        'address' => 'No 1, Zata Point Street, Zata Point',
                        'phone' => '08012345678',
                        'email' => '<EMAIL>',
                        'tin' => '*********',
                        'branchesCount' =>  1,
                        'isDefault' =>  true
                    ],
                    'currentPage' => 1,
                    'lastPage' => 1,
                    'itemsPerPage' => 100,
                    'pageItems' => 1,
                    'total' => 1,
                    'timestamp' => '2021-09-09, 12:00:00'
                ]

            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function index()
    {

        $userId = $this->user->id;
        $perPage = \request()->query('perPage', 50);
        $page = \request()->query('page', 1);
        $response =  $this->companyService->getCompany($userId, perPage: $perPage, page: $page, isEBM: true);
        return $this->jsonResponseWithPagination($response);
    }
}
