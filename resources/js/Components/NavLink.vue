<script setup>
import { computed } from 'vue';
import { Link } from '@inertiajs/vue3';

const props = defineProps({
    href: String,
    active: Boolean,
});

const classes = computed(() => {
    return props.active
        ? 'py-2 px-4  hover:text-zata-primary-dark font-medium   text-zata-primary-dark'
        : 'py-1 px-4  hover:text-zata-primary-dark font-medium text-gray-600 ';
});
</script>

<template>
    <Link :href="href" :class="classes">
        <slot />
    </Link>
</template>
