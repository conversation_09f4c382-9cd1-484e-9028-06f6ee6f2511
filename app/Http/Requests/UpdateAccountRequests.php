<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAccountRequests extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

     
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:40',
            'email' => ['required', 'email', 'max:255'],
            'phone' =>  ['nullable', 'string', 'min:10', 'max:12'],
            'subscriptionType' => 'required|string|in:Basic,Premium,Enterprise',
            'price' => 'required|numeric|min:0',
            'isActive' => 'required|boolean',
            'startDate' => 'required|date_format:Y-m-d',
        ];
    }
}
