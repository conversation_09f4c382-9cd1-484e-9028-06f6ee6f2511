<?php

namespace App\Http\Controllers;

use App\Data\PaymentCallBackData;
use App\Data\PaySubscriptionData;
use App\Http\Requests\PaySubscriptionRequest;
use App\Services\AccountSubscriptionService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;

class AccountSubscriptionController extends Controller
{

    // TODO, subscrption show by user owned company on sub dashbaord
    protected $user;

    public function __construct(protected AccountSubscriptionService $service)
    {
        $this->user = Auth::user();
    }

    public function index()
    {

        $subscrption = $this->service->getSubscription($this->user->id);
        return Inertia::render('Company/Subscription/Index', [
            'Subscription' => $subscrption,
            'SubscriptionList' => config('subscriptions'),
        ]);
    }

    public function update()
    {

        $subscrption = $this->service->getSubscription($this->user->id);
        return Inertia::render('Company/Subscription/Update', [
            'Subscription' => $subscrption,
            'SubscriptionList' => config('subscriptions'),
        ]);
    }

    public function pay(PaySubscriptionRequest $request)
    {

    
        $data = PaySubscriptionData::from($request->validated());
        $paymentId =  $this->service->paySubscription($data, $this->user->id);
        return redirect()->route('subscription.payment', ['paymentId' => $paymentId])->banner('check your mobile for payment');
    }

    public function zataCallback(Request $request, AccountSubscriptionService $service)
    {
        $data = PaymentCallBackData::from($request->validate([
            'event_id' => 'required|string',
            'event_kind' => 'required|string|in:transaction:processed',
            'created_at' => 'required|date',
            'data.ref' => 'required|string',
            'data.kind' => 'required|string|in:CASHIN,CASHOUT',
            'data.fee' => 'nullable|numeric',
            'data.merchant' => 'required|string',
            'data.client' => 'required|string',
            'data.amount' => 'required|numeric',
            'data.provider' => 'required|string',
            'data.status' => 'required|string|in:successful,failed',
            'data.created_at' => 'required|date',
            'data.processed_at' => 'required|date',
        ]));

        $rawBody = $request->getContent();
        $signature = $request->header('x-paypack-signature');

        $service->zataCallback($data, $this->user->id, $rawBody, $signature);

        return response()->json(['status' => 'success'], 200);
    }

    public function validatePayment(int $int)
    {

        $payment =  $this->service->getPaymentById($int, $this->user->id);
        return Inertia::render('Company/Subscription/ValidatePayment', [
            'Payment' => $payment
        ]);
    }
}
