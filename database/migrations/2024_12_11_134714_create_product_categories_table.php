<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ProductCategory', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();

            $table->unique(['name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ProductCategory');
    }
};
