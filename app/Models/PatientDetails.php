<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use DateTimeInterface;

class PatientDetails extends Model
{
    /** @use HasFactory<\Database\Factories\PatientDetailsFactory> */
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'PatientDetails';

    protected $fillable = [
        'party_id',
        'insurance_id',
        'code',
        'hasAffiliation',
        'affiliationNumber',
        'affiliateLastName',
        'affiliateFirstName',
        'relationship',
        'beneficiaryLastName',
        'beneficiaryFirstName',
        'dateOfBirth',
        'gender',
        'affiliationLocation',
        'beneficiaryNumber',
        'percentage',
        'expirationDate',
        'department',
        'phoneNumber',
        'status',
        'pin',
        'globalInezaId',
    ];

    protected $casts = [
        'dateOfBirth' => 'datetime',
        'expirationDate' => 'datetime',
        'hasAffiliation' => 'boolean',
    ];

    protected function serializeDate(DateTimeInterface $date): string
    {
        return $date->format('Y-m-d');
    }

    public function getDateOfBirthAttribute($value)
    {
        return \Carbon\Carbon::createFromTimestamp($value)->format('Y-m-d');
    }

    public function party(): BelongsTo
    {
        return $this->belongsTo(CompanyParty::class, 'party_id', 'id');
    }

    public function insurance(): BelongsTo
    {
        return $this->belongsTo(Insurance::class, 'insurance_id', 'id');
    }
}