<?php

namespace Database\Factories;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PaymentHistory>
 */
class PaymentHistoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'key' => $this->faker->uuid(),
            'amount' => $this->faker->randomFloat(2, 1, 1000),
            'currency' => $this->faker->randomElement(['usd', 'pkr', 'aud']),
            'gateway' => $this->faker->randomElement(['PayPack',]),
            'plan' => $this->faker->randomElement(['Basic',]),
            'date' => strtotime(Carbon::now()),
            'status' => $this->faker->randomElement(['pending']),
        ];
    }
}
