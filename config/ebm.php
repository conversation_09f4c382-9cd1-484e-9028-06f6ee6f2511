<?php

return [

    "initialization" => env("EBM_INITIALIZATION_ENDPOINT", "/initializer/selectInitInfo"),
    "item_codes" => env("EBM_ITEM_CODES_ENDPOINT", "/code/selectCodes"),
    "item_class" => env("EBM_ITEM_CLASS_ENDPOINT", "/itemClass/selectItemsClass"),
    "save_item" => env("EBM_SAVE_ITEM_ENDPOINT", "/items/saveItems"),
    "get_items" => env("EBM_GET_ITEM_ENDPOINT", "/items/selectItems"),
    "sale_transaction" => env("EBM_SALE_TRANSACTION_ENDPOINT", "/trnsSales/saveSales"),
    "get_imported_items" => env("EBM_GET_IMPORTED_ITEMS_ENDPOINT", "/imports/selectImportItems"),
    "get_purchases" => env("EBM_GET_PURCHASES_ENDPOINT", "/trnsPurchase/selectTrnsPurchaseSales"),
    "save_purchases" => env("EBM_SAVE_PURCHASES_ENDPOINT", "/trnsPurchase/savePurchases"),
    "save_imported_items" => env("EBM_SAVE_IMPORTED_ITEMS_ENDPOINT", "/imports/updateImportItems"),
    "save_stock_items" => env("EBM_SAVE_STOCK_ITEMS_ENDPOINT", "/stock/saveStockItems"),
    "save_stock_master" => env("EBM_SAVE_STOCK_MASTER_ENDPOINT", "/stockMaster/saveStockMaster"),
];
