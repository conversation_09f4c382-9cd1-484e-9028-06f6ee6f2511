<?php

namespace App\Services;

use App\Exceptions\BadRequest;
use App\Exceptions\Forbidden;
use App\Exceptions\NotFound;
use App\Exceptions\ServerError;
use App\Models\Company;
use App\Models\CompanyBranch;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class EbmService
{

  public function saveSaleTransaction($data, Company $company, CompanyBranch $branch)
  {
    if ($branch->cluster != 'demo') {
      if (!$branch->isInitialised) {
        throw new BadRequest('Branch is not initialized');
      }

      $url = $this->buildUrl($branch->cluster, $branch->clusterName, '/trnsSales/saveSales');

      try {
        $response = Http::timeout(30)->post($url, $data['payload']);
        Log::info('Received response from EBM API', [
          'url' => $url,
          'status' => $response->status(),
          'response' => $response->json() ?? 'Invalid JSON response',
          'body' => $response->body()
        ]);

        if (!$response->successful() || ($response->json()['resultCd'] ?? '999') !== '000') {
          Log::channel('slack')->error('Failed to save sale transaction', [
            'payload' => $data['payload'],
            'status' => $response->status(),
            'body' => $response->body(),
            'url' => $url,
            'tin' => $branch->company->tin,
            'deviceSerial' => $branch->deviceSerial,
            'branchName' => $branch->name,
            'companyName' => $branch->company->name,
            'cluster' => config('app.instance')
          ]);
          $errorMessage = $response->json()['resultMsg'] ?? 'Unknown error';
          throw new BadRequest($errorMessage);
        }


        return $response->json();
      } catch (\Illuminate\Http\Client\ConnectionException $e) {

        Log::channel('slack')->error('Connection error while calling EBM API', [
          'url' => $url,
          'error' => $e->getMessage(),
          'payload' => $data['payload'],
          'tin' => $branch->company->tin,
          'deviceSerial' => $branch->deviceSerial,
          'branchName' => $branch->name,
          'companyName' => $branch->company->name,
        ]);
        throw new ServerError('Failed to connect to EBM API: ' . $e->getMessage());
      } catch (\Exception $e) {

        Log::channel('slack')->error('Unexpected error while calling EBM API', [
          'url' => $url,
          'error' => $e->getMessage(),
          'payload' => $data['payload'],
          'tin' => $branch->company->tin,
          'deviceSerial' => $branch->deviceSerial,
          'branchName' => $branch->name,
          'companyName' => $branch->company->name,
        ]);
        throw new ServerError('Unexpected error during EBM API call: ' . $e->getMessage());
      }
    } else {
      // Demo mode logic
      $timestamp = now()->format('YmdHis');
      $receiptNumber = rand(1, 999);
      $internalData = strtoupper(Str::random(25));
      $receiptSignature = strtoupper(Str::random(16));
      $totalReceiptNumber = rand(1, 999);

      return [
        'resultCd' => '000',
        'resultMsg' => 'It is succeeded',
        'resultDt' => $timestamp,
        'data' => [
          'rcptNo' => $receiptNumber,
          'intrlData' => $internalData,
          'rcptSign' => $receiptSignature,
          'totRcptNo' => $totalReceiptNumber,
          'vsdcRcptPbctDate' => $timestamp,
          'sdcId' => 'SDC012000001',
          'mrcNo' => 'HIQ-0001-demo',
        ]
      ];
    }
  }

  public function autoInitializeBranch(int $branchId)
  {

    $branch = CompanyBranch::where('id', $branchId)
      ->with('company')
      ->first();

    throw_if(is_null($branch), NotFound::class, 'Branch not found');

    $branch->isInitialised = true;
    $branch->save();
  }


  public function initializeBranch(int $branchId)
  {

    $branch = CompanyBranch::where('id', $branchId)
      ->with('company')
      ->first();

    throw_if(is_null($branch), NotFound::class, 'Branch not found');

    throw_if($branch->cluster === 'demo', Forbidden::class, 'This action is not allowed in demo mode');

    $url = $this->buildUrl($branch->cluster, $branch->clusterName, '/initializer/selectInitInfo');

    $response = Http::timeout(1000)->post($url, [
      'tin' => $branch->company->tin,
      'bhfId' => $branch->branchCode,
      'dvcSrlNo' => $branch->deviceSerial,
    ]);

    if (!$response->successful()) {
      if (app()->environment('production')) {
        Log::channel('slack')->error('Failed to initialize branch due to API error', [
          'status' => $response->status(),
          'body' => $response->body(),
          'url' => $url,
          'tin' => $branch->company->tin,
          'deviceSerial' => $branch->deviceSerial,
          'branchName' => $branch->name,
          'companyName' => $branch->company->name,
          'cluster' => config('app.instance')
        ]);
      }
      Log::error('Failed to initialize branch due to API error', [
        'response' => $response->json(),
        'status' => $response->status(),
        'body' => $response->body(),
        'url' => $url,
        'tin' => $branch->company->tin,
        'deviceSerial' => $branch->deviceSerial,
        'branchName' => $branch->name,
        'companyName' => $branch->company->name,
        'cluster' => config('app.instance')
      ]);
      throw new BadRequest('API request failed');
    }

    $responseData = $response->json();

    if (is_null($responseData) || !is_array($responseData)) {
      Log::error('Invalid JSON response from API', [
        'body' => $response->body(),
        'url' => $url,
        'tin' => $branch->company->tin,
        'deviceSerial' => $branch->deviceSerial,
      ]);
      throw new BadRequest('Invalid API response format');
    }

    $resultCode = $responseData['resultCd'] ?? null;
    $resultMessage = $responseData['resultMsg'] ?? 'No result message provided';

    if (($resultCode === '000' && $resultMessage === 'It is succeeded') ||
      ($resultCode === '902' && $resultMessage === 'This device is installed')
    ) {

      if (app()->environment('production')) {
        Log::channel('slack')->info('Branch initialized successfully', [
          'response' => $responseData,
          'url' => $url,
          'tin' => $branch->company->tin,
          'deviceSerial' => $branch->deviceSerial,
          'name' => $branch->name . " " . $branch->company->name,
        ]);
      }

      if ($resultCode === '000') {

        $branch->update([
          'isInitialised' => true,
          'isActive' => true,
          'dvcId' => $responseData['data']['info']['dvcId'],
          'lastPchsInvcNo' => $responseData['data']['info']['lastPchsInvcNo'],
          'lastSaleInvcNo' => $responseData['data']['info']['lastSaleInvcNo'],
          'vatTyCd' => $responseData['data']['info']['vatTyCd'],
        ]);

        return;
      }

      $branch->update([
        'isInitialised' => true,
        'isActive' => true,
      ]);

      return;
    }

    if ($resultCode === '901' && $resultMessage === 'It is not valid device') {

      if (app()->environment('production')) {
        Log::channel('slack')->error('Branch initialization failed: Invalid device', [
          'response' => $responseData,
          'url' => $url,
          'tin' => $branch->company->tin,
          'deviceSerial' => $branch->deviceSerial,
          'name' => $branch->name . " " . $branch->company->name,
        ]);
      }
      throw new BadRequest('The device is not valid');
    }


    if (app()->environment('production')) {
      Log::channel('slack')->error('Unexpected API response during branch initialization', [
        'response' => $responseData,
        'url' => $url,
        'tin' => $branch->company->tin,
        'deviceSerial' => $branch->deviceSerial,
        'name' => $branch->name . " " . $branch->company->name,
      ]);
    }
    throw new BadRequest('Unexpected response from API: ' . $resultMessage);
  }

  public function syncProducts(CompanyBranch $branch)
  {
    if (!$branch->isInitialised) {
      throw new BadRequest('Branch is not initialized');
    }

    if ($branch->cluster === 'demo') {
      throw new Forbidden('This action is not allowed in demo mode');
    }

    $url = $this->buildUrl($branch->cluster, $branch->clusterName, '/items/selectItems');
    $response = Http::timeout(600)->post($url, [
      'tin' => $branch->company->tin,
      'bhfId' => $branch->branchCode,
      'lastReqDt' => '20180101000000',
    ]);

    Log::info('Syncing products to EBM', ['url' => $url, 'response' => $response->json()]);

    $resultCode = $response->json()['resultCd'] ?? null;
    $resultMessage = $response->json()['resultMsg'] ?? 'No result message provided';

    if ($resultCode === '000' && $resultMessage === 'It is succeeded') {
      Log::info("returning data");

      $data = collect($response->json()['data']['itemList'] ?? [])
        ->map(fn($item) => [
          'name' => $item['itemNm'],
          'itemCd' => $item['itemCd'],
          'itemClsCd' => $item['itemClsCd'],
          'itemTyCd' => $item['itemTyCd'],
          'itemNm' => $item['itemNm'],
          'orgnNatCd' => $item['orgnNatCd'],
          'pkgUnitCd' => $item['pkgUnitCd'],
          'qtyUnitCd' => $item['qtyUnitCd'],
          'taxTyCd' => $item['taxTyCd'],
          'regBhfId' => $item['regBhfId'],
          'dftPrc' => $item['dftPrc'],
          'sftyQty' => $item['sftyQty'],
          'useYn' => $item['useYn'],
        ])
        ->toArray();

      return $data;
    }
  }

  public function  syncImport($date, CompanyBranch $branch)
  {

    $formDate = null;

    if (!is_null($date)) {
      $formDate = Carbon::parse($date)->format('YmdHis');
    }

    if (!$branch->isInitialised) {
      throw new BadRequest('Branch is not initialized');
    }

    if ($branch->cluster === 'demo') {
      throw new Forbidden('This action is not allowed in demo mode');
    }

    $url = $this->buildUrl($branch->cluster, $branch->clusterName, '/imports/selectImportItems');
    $data = [
      'tin' => $branch->company->tin,
      'bhfId' => $branch->branchCode,
      'lastReqDt' => !is_null($formDate) ? $formDate : $branch->importation_last_request_date,
    ];

    $response = Http::timeout(600)->post($url, $data);
    Log::info($response->json());

    if ($response->successful() || $response->json('resultCd') == '000' || $response->json('resultMsg') == 'It is succeeded') {

      $branch->update(['importation_last_request_date' => $response->json('resultDt')]);

      $items = $response->json('data.itemList', []);
      $importProductData = [];
      foreach ($items as $item) {
        $importProductData[] = [
          'branch_id' => $branch->id,
          'taskCd' => $item['taskCd'],
          'dclNo' => $item['dclNo'],
          'dclDe' => $item['dclDe'],
          'itemSeq' => $item['itemSeq'],
          'hsCd' => $item['hsCd'],
          'itemNm' => $item['itemNm'],
          'orgnNatCd' => $item['orgnNatCd'],
          'exptNatCd' => $item['exptNatCd'],
          'pkg' => $item['pkg'],
          'pkgUnitCd' => $item['pkgUnitCd'],
          'qty' => $item['qty'],
          'qtyUnitCd' => $item['qtyUnitCd'],
          'totWt' => $item['totWt'],
          'netWt' => $item['netWt'],
          'spplrNm' => $item['spplrNm'],
          'agntNm' => $item['agntNm'],
          'invcFcurAmt' => $item['invcFcurAmt'],
          'invcFcurCd' => $item['invcFcurCd'],
          'invcFcurExcrt' => $item['invcFcurExcrt'],
          'status' => 'pending',
          'created_at' => strtotime(Carbon::parse(now())->format('Y-m-d h:i:s')),
          'updated_at' => strtotime(Carbon::parse(now())->format('Y-m-d h:i:s')),
        ];
      }

      return $importProductData;
    }
  }

  public function syncPurchase($transactionDate, CompanyBranch $branch)
  {

    if (!$branch->isInitialised) {
      throw new BadRequest('Branch is not initialized');
    }

    if ($branch->cluster === 'demo') {
      throw new Forbidden('This action is not allowed in demo mode');
    }

    $url = $this->buildUrl($branch->cluster, $branch->clusterName, '/trnsPurchase/selectTrnsPurchaseSales');

    $data = [
      'tin' => $branch->company->tin,
      'bhfId' => $branch->branchCode,
      'lastReqDt' => $transactionDate,
    ];

    $response = Http::timeout(600)->post($url, $data);

    if ($response->successful() || $response->json('resultCd') == '000' || $response->json('resultMsg') == 'It is succeeded') {
      Log::info($response->json());
      if ($response->json('resultDt') != null) {

        //   $requestDate->action_date = $response->json('resultDt');
        //   $requestDate->save();
      }

      return  $response->json('data.saleList', []);
    }
  }

  public function approveRejectImport(CompanyBranch $branch, $data)
  {

    if (!$branch->isInitialised) {
      throw new BadRequest('Branch is not initialized');
    }

    if ($branch->cluster === 'demo') {
      throw new Forbidden('This action is not allowed in demo mode');
    }

    $url = $this->buildUrl($branch->cluster, $branch->clusterName, '/imports/updateImportItems');

    $response = Http::timeout(600)->post($url, $data);

    if ($response->successful() || $response->json('resultCd') == '000' || $response->json('resultMsg') == 'It is succeeded') {

      Log::info($response->json());
      return true;
    }

    $errorMessage = $response['resultMsg'] ?? 'Unknown error';
    throw new BadRequest($errorMessage);
  }

  public function savePurchase(CompanyBranch $branch, $data)
  {

    if (!$branch->isInitialised) {
      throw new BadRequest('Branch is not initialized');
    }

    if ($branch->cluster === 'demo') {
      throw new Forbidden('This action is not allowed in demo mode');
    }

    $url = $this->buildUrl($branch->cluster, $branch->clusterName, '/trnsPurchase/savePurchases');

    $response = Http::timeout(600)->post($url, $data);

    if ($response->successful() || $response->json('resultCd') == '000' || $response->json('resultMsg') == 'It is succeeded') {

      Log::info($response->json());
      return $response->json();
    }

    throw new BadRequest($response['resultMsg'] ?? 'Unknown error');
  }

  public function buildUrl(string $serverUrl, string $appName, string $endpoint): string
  {
    $serverUrl = trim($serverUrl);
    $isValidUrl = filter_var($serverUrl, FILTER_VALIDATE_URL);
    $isValidIp = filter_var($serverUrl, FILTER_VALIDATE_IP) ||
      filter_var('http://' . $serverUrl, FILTER_VALIDATE_URL);

    if (!$isValidUrl && !$isValidIp) {
      throw new BadRequest('Invalid server URL format. Must be a valid IP or domain (with optional port)');
    }

    $baseUrl = rtrim($serverUrl, '/');
    if (!preg_match('#^https?://#', $baseUrl) && $isValidIp) {
      $baseUrl = 'http://' . $baseUrl;
      if (filter_var($baseUrl, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
        $parsed = parse_url($baseUrl);
        if (!str_contains($parsed['host'], '[')) {
          $baseUrl = str_replace($parsed['host'], '[' . $parsed['host'] . ']', $baseUrl);
        }
      }
    }

    throw_if(
      !preg_match('/^[a-zA-Z0-9_-]+$/', $appName),
      BadRequest::class,
      'Invalid application name. Only letters, numbers, underscores, and hyphens are allowed'
    );

    throw_if(
      !preg_match('#^(/[-a-zA-Z0-9_.]+)+(/)?$#', $endpoint) && $endpoint !== '',
      BadRequest::class,
      'Invalid endpoint format. Must be a valid path (e.g., "/users/list" or empty)'
    );

    $appPart = trim($appName, '/');
    $endpointPart = trim($endpoint, '/');

    return $baseUrl . '/' . $appPart . ($endpointPart ? '/' . $endpointPart : '');
  }
}
