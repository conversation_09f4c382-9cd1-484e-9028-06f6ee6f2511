<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use DateTimeInterface;

class AccountSubscription extends Model
{

    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'AccountSubscription';

    protected $fillable = [
        "isActive",
        "startDate",
        "endDate",
        "user_id",
        "subscriptionType",
        "billingPeriod",
        "price"
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'startDate' => 'datetime',
        'endDate' => 'datetime',
    ];

    protected function serializeDate(DateTimeInterface $date): string
    {
        return $date->format('Y-m-d');
    }

    public function  getStartDateAttribute($value)
    {
        return \Carbon\Carbon::createFromTimestamp($value)->format('Y-m-d');
    }

    public function  getEndDateAttribute($value)
    {

       return $value ? \Carbon\Carbon::createFromTimestamp($value)->format('Y-m-d') : null;
    }
}
