<?php

namespace App\Http\Controllers\Api;

use App\Data\NewProductData;
use App\Data\UpdateProductData;
use App\Data\UpdateProductQuantityData;
use App\Data\UpdateProductQuantityOutData;
use App\Http\Requests\NewProductRequest;
use App\Http\Requests\UpdateProductQuantityRequest;
use App\Services\ProductService;
use OpenApi\Attributes as OA;
use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateProductQuantityOutRequest;
use App\Http\Requests\UpdateProductRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class ProductController extends Controller
{

    protected $user;
    public function __construct(protected ProductService $service)
    {
        $this->user = Auth::user();
    }

    #[OA\Post(
        path: "/api/v1/product",
        description: "Create new product",
        security: [["authentication" => []]],
        summary: "create product",
        tags: ["Product"]
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        required: true,
        example: 1
    )]
    #[OA\RequestBody(
        description: "Product Payload",
        required: true,
        content: new OA\JsonContent(
            example: [
                "name" => "Product Name",
                "packagingUnitID" => 1,
                "quantityUnitID"  =>  1,
                "countryID" =>  1,
                "taxID" => 1,
                "branchProductCategoryID" =>  1,
                "hasStock" => "yes or no",
            ]
        )
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                "message" => "Product created successfully",
                "productId" => 1
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function create(NewProductRequest $request)
    {

        $company = companyData();
        $defaultBranch = branchData();
        $data = NewProductData::from($request->validated());
        $id =  $this->service->store($data, $defaultBranch, $company);
        return response()->json(['message' => 'Product created successfully', 'productId' => $id]);
    }
    #[OA\Get(
        path: "/api/v1/product/branch",
        description: "Get product by branch id",
        security: [["authentication" => []]],
        summary: "get product",
        tags: ["Product"]
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        required: true,
        example: 1
    )]
    #[OA\QueryParameter(
        name: "perPage",
        description: "Number of items per page",
        required: false,
        example: 100
    )]
    #[OA\QueryParameter(
        name: "page",
        description: "Page number",
        required: false,
        example: 1
    )]
    #[OA\QueryParameter(
        name: "searchQuery",
        description: "Search Query",
        example: "test"
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                "data" => [
                    [
                        "id" => 1,
                        "name" => "Product Name",
                        "itemCode" => "Product Code",
                        "category" => "Product Category",
                        "quantityUnit" => "Quantity Unit",
                        "packagingUnit" => "Packaging Unit",
                        "tax" => "Product Tax",
                        "class" => "Product Class",
                        "type" => "Product Type",
                        "totalStock" => 10,
                        "image" => 'url',
                    ]
                ],
                "currentPage" =>  1,
                "lastPage" =>  1,
                "itemsPerPage" =>  10,
                "pageItems" =>  2,
                "total" =>  2,
                "timestamp" => "2024-12-12, 09:16:26"
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function getProduct()
    {

        $defaultBranch = branchData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $searchQuery = \request()->query('searchQuery', null);
        $product = $this->service->getProducts($defaultBranch->id, $perPage, $page, $searchQuery);
        return $this->jsonResponseWithPagination($product);
    }

    #[OA\Get(
        path: "/api/v1/product/{productId}",
        description: "Get product by id and branch id",
        security: [["authentication" => []]],
        summary: "get product by id",
        tags: ["Product"]
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        description: "Branch Id",
        required: true,
        schema: new OA\Schema(
            type: "integer",
            example: 1
        )
    )]
    #[OA\PathParameter(
        name: "productId",
        description: "Product Id",
        required: true,
        schema: new OA\Schema(
            type: "integer",
            example: 1
        )
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                "id" => 1,
                "name" => "Product Name",
                "itemCode" => "Product Code",
                "category" => "Product Category",
                "categoryID" => 1,
                "quantityUnit" => "Quantity Unit",
                "quantityUnitID" => 1,
                "packagingUnit" => "Packaging Unit",
                "packagingUnitID" => 1,
                "country" => "Product Country",
                "countryID" => 1,
                "tax" => "Product Tax",
                "taxID" => 1,
                "class" => "Product Class",
                "classID" => 1,
                "type" => "Product Type",
                "typeID" => 1,
                "branchProductCategory" => "Product Category",
                "branchProductCategoryID" => 1,
                "totalStock" => 10,
                "soldInSubUnit" => false,
                "conversionFactor" => 0,
                "image" => 'url',
                "productDetails" => [
                    [
                        "id" => 1,
                        "currentStock" => 10,
                        "purchasePrice" => 10,
                        "salesPrice" => 10,
                        "discountRate" => 20,
                        "status" => "in_stock",
                        "expireDate" => "2024-12-12",
                        "batchNumber" => "Batch Number",
                    ]
                ]
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function getProductById(int $productId)
    {

        $defaultBranch = branchData();
        $product = $this->service->getProductById($defaultBranch->id, $productId);
        return response()->json($product);
    }

    #[OA\Put(
        path: "/api/v1/product/{productId}",
        description: "Update product",
        security: [["authentication" => []]],
        summary: "update product",
        tags: ["Product"]
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        required: true,
        example: 1
    )]
    #[OA\PathParameter(
        name: "productId",
        required: true,
        example: 1
    )]
    #[OA\RequestBody(
        description: "Product Payload",
        required: true,
        content: new OA\JsonContent(
            example: [
                "name" => "Product Name",
                "branchProductCategoryID" => 1,
            ]
        )
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                "message" => "Product updated successfully",
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function update(UpdateProductRequest $request, int $productId)
    {

        $defaultBranch = branchData();
        $data = UpdateProductData::from($request->validated());

        $this->service->update($data, $defaultBranch, $productId);

        return response()->json(['message' => 'Product updated successfully']);
    }
    #[OA\Put(
        path: "/api/v1/product/increase-quantity/{productId}",
        description: "increase product quantity",
        security: [["authentication" => []]],
        summary: "increase product quantity",
        tags: ["Product"]
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        required: true,
        example: 1
    )]
    #[OA\PathParameter(
        name: "productId",
        required: true,
        example: 1
    )]
    #[OA\RequestBody(
        description: "Product Payload",
        required: true,
        content: new OA\JsonContent(
            example: [
                "description" => "reason for increasing quantity",
                "quantity" => 10,
                "salePrice" => 10,
                "batchNumber" => "Batch Number",
                "discountRate" => 0,
            ]
        )
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                "message" => "Product quantity updated successfully",
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function updateProductQuantity(UpdateProductQuantityRequest $request, int $productId)
    {

        $defaultBranch = branchData();
        $data = UpdateProductQuantityData::from($request->validated());
        $this->service->increaseProductQuantity($data, $defaultBranch, $productId, $this->user->id);

        return  response()->json(['message' => 'Product quantity updated successfully']);
    }
    #[OA\Put(
        path: "/api/v1/product/reduce-quantity/{productId}",
        description: "reduce product quantity",
        security: [["authentication" => []]],
        summary: "reduce product",
        tags: ["Product"]
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        required: true,
        example: 1
    )]
    #[OA\PathParameter(
        name: "productId",
        required: true,
        example: 1
    )]
    #[OA\RequestBody(
        description: "Product Payload",
        required: true,
        content: new OA\JsonContent(
            example: [
                "quantity" => 10,
                "description" => "reason for reducing quantity",
                "batchNumber" => "Batch Number",
            ]
        )
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                "message" => "Product quantity updated successfully",
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function reduceProductQuantity(UpdateProductQuantityOutRequest $request, int $productId)
    {

        $defaultBranch = branchData();
        $data = UpdateProductQuantityOutData::from($request->validated());
        $this->service->reduceProductQuantity($data, $defaultBranch, $productId, $this->user->id);

        return  response()->json(['message' => 'Product quantity reduced successfully']);
    }
}
