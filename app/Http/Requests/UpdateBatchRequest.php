<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateBatchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'salePrice' => 'required|numeric|min:1',
            'purchasePrice' => 'nullable|numeric|min:1|lte:salePrice',
            'expireDate' => 'nullable|date|after:today',
            'discountRate' => 'nullable|min:0|max:100',
        ];
    }
}
