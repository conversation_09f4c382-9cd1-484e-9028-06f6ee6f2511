<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref, watch, reactive } from 'vue';
import { useForm } from "@inertiajs/vue3";
import PrimaryButton from '@/Components/PrimaryButton.vue';
import RadioOptionGroup from '@/Components/RadioOptionGroup.vue';
import "@fortawesome/fontawesome-free/css/all.css";
import "@fortawesome/fontawesome-free/js/all.js";
import DangerButton from '@/Components/DangerButton.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import TextAreaInput from '@/Components/TextAreaInput.vue';


const props = defineProps({
    Products: Array,
    PaymentMethods: Array,
    Customers: Array,
    currentPage: Number,
    lastPage: Number,
    itemsPerPage: Number,
    pageItems: Number,
    total: Number,
    isEBM: Boolean,
    BranchProductCategories: Array,
});

const cart = ref([]);
const searchQuery = ref('');
const selectedCategory = ref('');

const form = useForm({
    customerID: null,
    transactionDate: new Date().toISOString().slice(0, 10),
    note: 'NB: ',
    items: [],
});
function addToCart(product) {
    const existingProduct = cart.value.find(item => item.id === product.id);
    if (existingProduct) {
        existingProduct.units += 1;
    } else {
        cart.value.push({
            ...product,
            units: 1,
            unitPrice: product.salePrice,
            discountRate: 0,
            productID: product.id,
            expireDate: product.expireDate,
            movingUnit: 'sub'
        });
    }
    updateFormItems();
}

function removeFromCart(index) {
    cart.value.splice(index, 1);
    updateFormItems();
}

function updateFormItems() {
    form.items = cart.value.map(item => ({
        productID: item.productID,
        units: item.units,
        unitPrice: item.unitPrice,
        batchNumber: item.batchNumber,
        discountRate: item.discountRate,
        expireDate: item.expireDate,
        movingUnit: item.movingUnit || 'sub'
    }));
}


function submitOrder() {
    form.post(route('transaction.purchase.store'));
}

function clearCart() {
    cart.value = [];
    updateFormItems();
}

const state = reactive({
    selectedItemsPerPage: props.itemsPerPage,
    jumpToPage: props.currentPage,
});

const paginationForm = useForm({
    page: props.currentPage,
    perPage: props.itemsPerPage,
    searchQuery: '',
    productBranchCategoryID: '',
});

const updateItemsPerPage = () => {
    paginationForm.perPage = state.selectedItemsPerPage;
    paginationForm.page = 1;
    paginationForm.get(route('pos'), { preserveState: true, preserveScroll: true });
};

const prevPage = () => {
    if (props.currentPage > 1) {
        paginationForm.page = props.currentPage - 1;
        paginationForm.get(route('pos'), { preserveState: true, preserveScroll: true });
    }
};

const nextPage = () => {
    if (props.currentPage < props.lastPage) {
        paginationForm.page = props.currentPage + 1;
        paginationForm.get(route('pos'), { preserveState: true, preserveScroll: true });
    }
};

watch(searchQuery, (newQuery) => {
    paginationForm.searchQuery = newQuery;
    paginationForm.page = 1;
    paginationForm.get(route('pos'), { preserveState: true, preserveScroll: true });
});

watch(selectedCategory, (newCategory) => {
    paginationForm.productBranchCategoryID = newCategory;
    paginationForm.page = 1;
    paginationForm.get(route('pos'), { preserveState: true, preserveScroll: true });
});

watch(() => form.customerID, (newCustomerID) => {
    const selectedCustomer = props.Customers.find(customer => customer.id === newCustomerID);
    if (selectedCustomer) {
        form.clientTin = selectedCustomer.tin;
    } else {
        form.clientTin = null;
    }

});

function updateQuantity(index, quantity) {
    if (quantity < 1) {
        removeFromCart(index);
    } else {
        cart.value[index].units = quantity;
        updateFormItems();
    }
}
</script>


<template>
    <AppLayout title="Purchase order" :fullwidth="true">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Purchase order
            </h2>
        </template>

        <p class="text-center text-gray-500 text-lg py-5 font-bold">
            Purchase order
        </p>
        <section style="max-width:1800px" class="mx-auto">
            <div class="hidden xl:flex mt-5 space-x-4" style="height: 50vh">

                <div class="w-1/2 p-4  rounded-lg border border-gray-300 flex flex-col h-full bg-zata-background">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Products</h3>
                    <div class="w-full flex justify-between space-x-4">
                        <input type="text" v-model="searchQuery" placeholder="Search products..."
                            class="mb-4 p-2 border border-gray-300 rounded w-1/2" />
                        <select v-model="selectedCategory" class="mb-4 p-2 border border-gray-300 rounded w-1/2">
                            <option value="">Select Category</option>
                            <option v-for="category in BranchProductCategories" :key="category.id" :value="category.id">
                                {{ category.name }}
                            </option>
                        </select>
                    </div>

                    <div class="flex flex-wrap -m-2 overflow-y-auto " style="max-height: 550px;"
                        v-if="Products.length > 0">
                        <div class="p-2 lg:w-1/2 md:w-full w-full hover:cursor-pointer" v-for="Product in Products"
                            :key="Product.id" @click="addToCart(Product)">
                            <div
                                class="h-full flex items-center border-gray-300 border p-4 rounded-lg hover:bg-gray-100 bg-white">
                                <img alt="team"
                                    class="w-16 h-16 bg-gray-100 border border-gray-400 object-cover object-center flex-shrink-0 rounded-full mr-4"
                                    :src="Product.image" />
                                <div class="flex-grow">
                                    <h2 class="">
                                        <span class="font-bold text-lg">{{ Product.name }} </span>
                                    </h2>
                                    <p class="">
                                        <span class="ml-1 text-gray-500">Current stock : {{ Product.currentStock }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class="text-gray-500 text-center p-32">No products found</div>
                    <div class="mt-auto flex justify-between items-center pt-4 ">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing
                                <span class="font-medium">{{ (currentPage - 1) * itemsPerPage + 1 }}</span>
                                to
                                <span class="font-medium">{{ Math.min(currentPage * itemsPerPage, total) }}</span>
                                of
                                <span class="font-medium">{{ total }}</span>
                                results
                            </p>
                        </div>
                        <div class="flex items-center">
                            <label for="itemsPerPage" class="text-sm font-medium text-gray-700">Items per
                                page:</label>
                            <select id="itemsPerPage" v-model="state.selectedItemsPerPage" @change="updateItemsPerPage"
                                class="ml-2 border-gray-300 rounded">
                                <option :value="5">5</option>
                                <option :value="10">10</option>
                                <option :value="20">20</option>
                                <option :value="50">50</option>
                                <option :value="100">100</option>
                            </select>
                        </div>
                        <div class="flex space-x-2">
                            <button @click="prevPage" :disabled="currentPage === 1"
                                class="px-4 py-2 bg-white rounded hover:bg-gray-300 disabled:opacity-70 border border-gray-300">
                                Previous
                            </button>

                            <button @click="nextPage" :disabled="currentPage === lastPage"
                                class="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50">
                                Next
                            </button>
                        </div>
                    </div>
                </div>

                <div class="w-1/2 p-4 rounded-lg border border-gray-300 h-full flex flex-col">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Cart</h3>
                    <div v-if="cart.length === 0" class="text-gray-500 text-center p-6">No products in cart</div>
                    <div v-else class="overflow-y-auto" style="max-height: 550px;">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Product Name
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Quantity
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr v-for="(product, index) in cart" :key="index" class="hover:bg-gray-100">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ product.name }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <input type="number" v-model.number="product.units"
                                            @change="updateQuantity(index, product.units)"
                                            class="w-16 p-2 border border-gray-300 rounded" />

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <button @click="removeFromCart(index)"
                                            class="bg-red-500 text-white py-1 px-2 rounded">
                                            <i class="fa-solid fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="flex justify-center border-t p-3 mt-auto">
                        <p>Total Items: {{ cart.length }}</p>
                    </div>
                </div>
            </div>

            <div class="  mt-5   flex justify-center">
                <div class="w-full h-full mx-auto">
                    <form @submit.prevent="submitOrder" class="w-full flex h-full gap-3 justify-center">

                        <div class="w-1/3 p-5 border rounded-lg">

                            <div class="pb-4">
                                <InputLabel for="transactionDate" value="Date" />
                                <TextInput id="transactionDate" v-model="form.transactionDate" type="date" />
                                <InputError class="mt-2" :message="form.errors.transactionDate" />
                            </div>
                            <div class="pb-4">
                                <InputLabel for="note" value="Note" />
                                <TextAreaInput id="note" v-model="form.note" />
                                <InputError class="mt-2" :message="form.errors.note" />
                            </div>

                        </div>
                        <div class="w-1/3 p-5 border rounded-lg border-gray-300">
                            <label for="customer" class="font-bold text-xl px-4 flex justify-center">Supplier</label>
                            <div class="px-4 mt-5">
                                <div>
                                    <select id="customer" v-model="form.customerID"
                                        class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm">
                                        <option :value="null"> Select supplier</option>
                                        <option v-for="customer in Customers" :key="customer.id" :value="customer.id">{{
                                            customer.name }}
                                        </option>

                                    </select>
                                    <InputError class="mt-2" :message="form.errors.customerID" />
                                </div>
                                <div class="flex justify-between gap-2 pt-6">
                                    <DangerButton class="w-72" @click="clearCart"
                                        :class="{ 'cursor-not-allowed': cart.length === 0 }">
                                        Clear
                                    </DangerButton>
                                    <PrimaryButton :disabled="cart.length === 0 || form.processing" class="w-72"
                                        :class="{ 'cursor-not-allowed ': cart.length === 0, '': cart.length > 0 }">
                                        Create
                                    </PrimaryButton>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </section>

        <div class="block xl:hidden">
            <p class="text-center text-gray-500 text-lg py-32">
                Please use a larger screen to access this feature
            </p>
        </div>
    </AppLayout>
</template>