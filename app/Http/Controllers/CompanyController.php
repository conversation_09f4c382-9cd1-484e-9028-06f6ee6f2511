<?php

namespace App\Http\Controllers;

use App\Data\NewCompanyData;
use App\Data\UpdateCompanyData;
use App\Http\Requests\NewCompanyRequest;
use App\Http\Requests\UpdateCompanyRequest;
use App\Services\CompanyService;
use App\Services\InsuranceService;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Enums\AccountTypeEnums;

class CompanyController extends Controller
{

    protected $user;

    public function __construct(
        protected CompanyService $companyService,
        protected InsuranceService $insuranceService
    ) {

        $this->user = Auth::user();
    }
    public function index()
    {

        $company =  companyData();
        $companyProfile = $this->companyService->getCompanyProfile($company->id);
        return Inertia::render('Company/Index', [
            'Company' => $companyProfile,
            'Branches' => $companyProfile['branches'],
            'defaultBranchId' => branchData()->id
        ]);
    }

    public function list()
    {

        $userId = $this->user->id;
        $perPage = \request()->query('perPage', 100);
        $page = \request()->query('page', 1);

        $response = $this->pagination($this->companyService->getCompany($userId, perPage: $perPage, page: $page));

        return Inertia::render('Company/List', [
            'Companies' => $response['data'],
            'currentPage' => $response['currentPage'],
            'lastPage' => $response['lastPage'],
            'itemsPerPage' => $response['itemsPerPage'],
            'pageItems' => $response['pageItems'],
            'total' => $response['total']
        ]);
    }

    public function switch(int $companyId)
    {

        $this->companyService->switchCompany($this->user->id, $companyId);

        return redirect()->route('dashboard');
    }

    public function new()
    {

        return Inertia::render('Company/New');
    }

    public function store(NewCompanyRequest $request)
    {

        $data = NewCompanyData::from($request->validated());

        $this->companyService->storeCompany($data, $this->user->id);

        return redirect()->route('company.index')->banner('Company Created Successfully');
    }

    public function edit(int $companyId)
    {

        zataAccess([AccountTypeEnums::ADMIN->value]); 
        $company = $this->companyService->getCompanyById($this->user->id, $companyId);
        $insurance = $this->insuranceService->getInsurance($companyId);
        return Inertia::render('Company/Edit', [
            'Company' => $company,
            'Insurance' => $insurance
        ]);
    }

    public function update(UpdateCompanyRequest $request, int $companyId)
    {

        zataAccess([AccountTypeEnums::ADMIN->value]);
        $data = UpdateCompanyData::from($request->validated());

        $this->companyService->updateCompany($data, $this->user->id, $companyId);

        return redirect()->route('company.index')->banner('Company Updated Successfully');
    }
}
