<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Laravel\Scout\Searchable;
use Illuminate\Support\Facades\Log;

class ProductStockHistory extends Model
{
    /** @use HasFactory<\Database\Factories\ProductStockHistoryFactory> */
    use HasFactory, Searchable;

    protected $dateFormat = 'U';

    protected $table = 'ProductStockHistory';

    protected $fillable = [
        'product_id',
        'productCode',
        'batchNumber',
        'branch_id',
        'to_branch_id',
        'user_id',
        'quantity',
        'oldQuantity',
        'orderType',
        'stockType',
        'description',
        'date',
        'confirmationDate',
        'expiryDate',
        'party_id',
    ];

    protected $casts = [
        'expiryDate' => 'datetime',
        'date' => 'datetime',
        'confirmationDate' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function getDateAttribute($value)
    {
        return \Carbon\Carbon::createFromTimestamp($value)->format('Y-m-d H:i:s');
    }

    public function getConfirmationDateAttribute($value)
    {
      return $value ? \Carbon\Carbon::createFromTimestamp($value)->format('Y-m-d H:i:s') : null;
    }

    public function getExpiryDateAttribute($value)
    {
      return $value ? \Carbon\Carbon::createFromTimestamp($value)->format('Y-m-d H:i:s') : null;
    }

  public function  getCreatedAtAttribute($value)
    {
        return \Carbon\Carbon::createFromTimestamp($value)->format('Y-m-d H:i:s');
    }

    public function toSearchableArray()
    {
        return [
            'description' => $this->description,
            'quantity' => $this->quantity,
            'stockType' => $this->stockType,
            'orderType' => $this->orderType,
        ];
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(CompanyBranch::class, 'branch_id', 'id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function scopeWhereProductId($query, ?int $productId)
    {

        if (!is_null($productId)) {
            return $query->where('product_id', $productId);
        }

        return $query;
    }

    public function scopeFilterByDate($query, $startDate = null, $endDate = null)
    {
        if (is_null($startDate) && is_null($endDate)) {
            return $query;
        }
    
        try {
            $startTimestamp = null;
            if (!is_null($startDate) && !empty($startDate)) {
                $startTimestamp = \Carbon\Carbon::createFromFormat('Y-m-d', $startDate)
                    ->startOfDay()
                    ->getTimestamp(); // Convert to Unix timestamp
            }
    
            $endTimestamp = null;
            if (!is_null($endDate) && !empty($endDate)) {
                $endTimestamp = \Carbon\Carbon::createFromFormat('Y-m-d', $endDate)
                    ->endOfDay()
                    ->getTimestamp(); // Convert to Unix timestamp
            }
    
            if ($startTimestamp && $endTimestamp) {
                return $query->whereBetween('date', [$startTimestamp, $endTimestamp]);
            } elseif ($startTimestamp) {
                return $query->where('date', '>=', $startTimestamp);
            } elseif ($endTimestamp) {
                return $query->where('date', '<=', $endTimestamp);
            }
    
            return $query;
    
        } catch (\Exception $e) {
             Log::error('Date filter error: ' . $e->getMessage());
            return $query;
        }
    }

    public function party(): HasOne
    {
        return $this->hasOne(CompanyParty::class, 'id', 'party_id');
    }
}
