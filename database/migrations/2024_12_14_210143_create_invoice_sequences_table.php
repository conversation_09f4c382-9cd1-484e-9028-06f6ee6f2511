<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('InvoiceSequences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->references("id")->on("CompanyBranch")->onUpdate("cascade")->onDelete("restrict");
            $table->bigInteger('last_number');
             $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();

            $table->unique('branch_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('InvoiceSequences');
    }
};
