<?php

namespace App\Http\Controllers;

use App\Services\CommonDataService;
use OpenApi\Attributes as OA;

class CommonDataController extends Controller
{


    public function __construct(protected CommonDataService $service) {}

    #[OA\Get(
        path: "/api/v1/data/product-type",
        description: "Get all product types",
        security: [["authentication" => []]],
        summary: "product types",
        tags: ["App Data"]
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [

                [
                    "id" => 1,
                    "name" => "Product Type 1",
                    "description" => "Product Type 1 description"
                ],
                [
                    "id" => 2,
                    "name" => "Product Type 2",
                    "description" => "Product Type 2 description"
                ],
                [
                    "id" => 3,
                    "name" => "Product Type 3",
                    "description" => "Product Type 3 description"
                ]
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function producType()
    {

        $data = $this->service->getProductType();
        return response()->json($data);
    }

    #[OA\Get(
        path: "/api/v1/data/payment-mode",
        description: "Get all payment modes",
        security: [["authentication" => []]],
        summary: "payment modes",
        tags: ["App Data"]
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [

                [
                    "id" => 1,
                    "name" => "Payment Mode 1",
                    "description" => "Payment Mode 1 description",
                ],
                [
                    "id" => 2,
                    "name" => "Payment Mode 2",
                    "description" => "Payment Mode 2 description",
                ],
                [
                    "id" => 3,
                    "name" => "Payment Mode 3",
                    "description" => "Payment Mode 3 description",
                ]
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function paymentMode()
    {

        $company = companyData();
        $data = $this->service->getPaymentMode($company->id, true);
        return response()->json($data);
    }

    #[OA\Get(
        path: "/api/v1/data/product-category",
        description: "Get all product categories",
        security: [["authentication" => []]],
        summary: "product categories",
        tags: ["App Data"]
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [

                [
                    "id" => 1,
                    "name" => "Product category 1",
                ]

            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function productCategory(){

        $data = $this->service->getProductCategory();
        return response()->json($data);
    }


    #[OA\Get(
        path: "/api/v1/data/product-class",
        description: "Get all product classes",
        security: [["authentication" => []]],
        summary: "product classes",
        tags: ["App Data"]
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [

                [
                    "id" => 1,
                    "name" => "Product Class 1",
                    "code" => "Product Class 1 code",
                ]

            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function productClass(){

        $data = $this->service->getProductClass();
        return response()->json($data);
    }

    #[OA\Get(
        path: "/api/v1/data/product-quantity-unit",
        description: "Get all product quantity units",
        security: [["authentication" => []]],
        summary: "product quantity units",
        tags: ["App Data"]
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [

                [
                    "id" => 1,
                    "name" => "Product Quantity Unit 1",
                    "code" => "Product Quantity Unit 1 code",
                ]

            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function productQuantityUnit(){

        $data = $this->service->getProductQuantityUnit();
        return response()->json($data);
    }

    #[OA\Get(
        path: "/api/v1/data/product-packaging-unit",
        description: "Get all product packaging units",
        security: [["authentication" => []]],
        summary: "product packaging units",
        tags: ["App Data"]
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [

                [
                    "id" => 1,
                    "name" => "Product Packaging Unit 1",
                    "code" => "Product Packaging Unit 1 code",
                ]

            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function productPackagingUnit(){

        $data = $this->service->getProductPackagingUnit();
        return response()->json($data);
    }

    #[OA\Get(
        path: "/api/v1/data/product-tax",
        description: "Get all product taxes",
        security: [["authentication" => []]],
        summary: "product taxes",
        tags: ["App Data"]
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [

                [
                    "id" => 1,
                    "name" => "Product Tax 1",
                    "rate" => 18,
                ]

            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function productTax(){

        $data = $this->service->getProductTax(true);
        return response()->json($data);
    }

    #[OA\Get(
        path: "/api/v1/data/product-country-origin",
        description: "Get all product countries of origin",
        security: [["authentication" => []]],
        summary: "product countries",
        tags: ["App Data"]
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [

                [
                    "id" => 1,
                    "name" => "Country 1",
                    "code" => "Country Code 1",
                ]

            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function productCountry(){

        $data = $this->service->getProductCountry();
        return response()->json($data);
    }
}
