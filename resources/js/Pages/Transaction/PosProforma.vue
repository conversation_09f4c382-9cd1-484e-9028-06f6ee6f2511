<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref, watch, reactive } from 'vue';
import { useForm, Link } from "@inertiajs/vue3";
import PrimaryButton from '@/Components/PrimaryButton.vue';
import RadioOptionGroup from '@/Components/RadioOptionGroup.vue';

import "@fortawesome/fontawesome-free/css/all.css";
import "@fortawesome/fontawesome-free/js/all.js";
import SecondaryLink from '@/Components/SecondaryLink.vue';
import DangerButton from '@/Components/DangerButton.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';

const props = defineProps({
    Products: Array,
    PaymentMethods: Array,
    Customers: Array,
    currentPage: Number,
    lastPage: Number,
    itemsPerPage: Number,
    pageItems: Number,
    total: Number,
    isEBM: Boolean,
    BranchProductCategories: Array,
});

const cart = ref([]);
const searchQuery = ref('');
const selectedCategory = ref('');

const form = useForm({
    'clientTin': null,
    'clientPhoneNumber': null,
    'clientName': null,
    'clientAddress': null,
    'customerID': null,
    'paymentMethodID': 1,
    'purchaseCode': null,
    'customerID': null,
    'transactionDate': new Date().toISOString().split('T')[0],
    'purchaseCode': null,
    'note': 'Quotation',
    'items': [],
});

const subtotal = ref(0);
const taxA = ref(null);
const taxB = ref(null);
const taxC = ref(null);
const taxD = ref(null);
const afterInsuranceDiscount = ref(null);
const insuranceDiscount = ref(null);
const discountPercentage = ref(null);
const insuranceName = ref(null);

function addToCart(product) {
    const existingProduct = cart.value.find(item => item.id === product.id && item.batchNumber === product.batchNumber);
    if (existingProduct) {
        existingProduct.units += 1;
    } else {
        cart.value.push({
            ...product,
            units: 1,
            unitPrice: product.salePrice,
            batchNumber: product.batchNumber,
            discountRate: 0,
            productID: product.id,
            expireDate: product.expireDate,
            movingUnit: 'sub'
        });
    }
    updateFormItems();
    sendPayloadToBackend();
}

function removeFromCart(index) {
    cart.value.splice(index, 1);
    updateFormItems();
    sendPayloadToBackend();
}

function updateFormItems() {
    form.items = cart.value.map(item => ({
        productID: item.productID,
        units: item.units,
        unitPrice: item.unitPrice,
        batchNumber: item.batchNumber,
        discountRate: item.discountRate,
        expireDate: item.expireDate,
        movingUnit: item.movingUnit || 'sub'
    }));
}

async function sendPayloadToBackend() {
    const payload = {
        items: form.items, // Use the updated cart items
        clientTin: form.clientTin,
        clientPhoneNumber: form.clientPhoneNumber,
        clientName: form.clientName,
        paymentMethodID: form.paymentMethodID,
        transactionDate: form.transactionDate,
        note: form.note,
        customerID: form.customerID,
    };

    try {
        const response = await axios.post(route('pos.calculate'), payload);

        console.log('Response:', response.data);

        subtotal.value = response.data.total;
        taxA.value = response.data.taxblAmtA;
        taxB.value = response.data.taxblAmtB;
        taxC.value = response.data.taxblAmtC;
        taxD.value = response.data.taxblAmtD;
        afterInsuranceDiscount.value = response.data.afterInsuranceDiscount;
        insuranceDiscount.value = response.data.insuranceDiscount;
        discountPercentage.value = response.data.discountPercentage;
        insuranceName.value = response.data.insuranceName;

    } catch (error) {
        console.error('Error sending data to backend:', error.message || error);
    }
}

function submitOrder() {
    form.post(route('transaction.proforma.store'), {

    });
}

function clearCart() {
    cart.value = [];
    updateFormItems();
    sendPayloadToBackend();
}

const state = reactive({
    selectedItemsPerPage: props.itemsPerPage,
    jumpToPage: props.currentPage,
});

const paginationForm = useForm({
    page: props.currentPage,
    perPage: props.itemsPerPage,
    searchQuery: '',
    productBranchCategoryID: '',
});

const updateItemsPerPage = () => {
    paginationForm.perPage = state.selectedItemsPerPage;
    paginationForm.page = 1;
    paginationForm.get(route('pos'), { preserveState: true, preserveScroll: true });
};

const prevPage = () => {
    if (props.currentPage > 1) {
        paginationForm.page = props.currentPage - 1;
        paginationForm.get(route('pos'), { preserveState: true, preserveScroll: true });
    }
};

const nextPage = () => {
    if (props.currentPage < props.lastPage) {
        paginationForm.page = props.currentPage + 1;
        paginationForm.get(route('pos'), { preserveState: true, preserveScroll: true });
    }
};

watch(searchQuery, (newQuery) => {
    paginationForm.searchQuery = newQuery;
    paginationForm.page = 1;
    paginationForm.get(route('pos'), { preserveState: true, preserveScroll: true });
});

watch(selectedCategory, (newCategory) => {
    paginationForm.productBranchCategoryID = newCategory;
    paginationForm.page = 1;
    paginationForm.get(route('pos'), { preserveState: true, preserveScroll: true });
});

watch(() => form.customerID, (newCustomerID) => {
    const selectedCustomer = props.Customers.find(customer => customer.id === newCustomerID);
    if (selectedCustomer) {
        form.clientTin = selectedCustomer.tin;
        form.clientPhoneNumber = selectedCustomer.phone;
        form.clientName = selectedCustomer.name;
    } else {
        form.clientTin = null;
        form.clientPhoneNumber = null;
        form.clientName = null;
    }
});

watch(cart, () => {
    updateFormItems();
}, { deep: true });

function updateQuantity(index, quantity) {
    if (quantity < 1) {
        removeFromCart(index);
    } else {
        cart.value[index].units = quantity;
        updateFormItems();
        sendPayloadToBackend();
    }
}

const options = props.PaymentMethods.map(method => ({
    value: method.id,
    label: method.name,
}));
</script>

<template>
    <AppLayout title="Quotation" :fullwidth="true">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Quotation
            </h2>
        </template>

        <p class="text-center text-gray-500 text-lg py-5 font-bold">
            Quotation 
        </p>
        <section style="max-width:1800px" class="mx-auto">
            <div class="hidden xl:flex mt-5 space-x-4" style="height: 50vh">

                <div class="w-1/2 p-4  rounded-lg border border-gray-300 flex flex-col h-full bg-zata-background">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Products</h3>
                    <div class="w-full flex justify-between space-x-4">
                        <input type="text" v-model="searchQuery" placeholder="Search products..."
                            class="mb-4 p-2 border border-gray-300 rounded w-1/2" />
                        <select v-model="selectedCategory" class="mb-4 p-2 border border-gray-300 rounded w-1/2">
                            <option value="">Select Category</option>
                            <option v-for="category in BranchProductCategories" :key="category.id" :value="category.id">
                                {{ category.name }}
                            </option>
                        </select>
                    </div>

                    <div class="flex flex-wrap -m-2 overflow-y-auto " style="max-height: 550px;"
                        v-if="Products.length > 0">
                        <div class="p-2 lg:w-1/2 md:w-full w-full hover:cursor-pointer" v-for="Product in Products"
                            :key="Product.id" @click="addToCart(Product)">
                            <div
                                class="h-full flex items-center border-gray-300 border p-4 rounded-lg hover:bg-gray-100 bg-white">
                                <img alt="team"
                                    class="w-16 h-16 bg-gray-100 border border-gray-400 object-cover object-center flex-shrink-0 rounded-full mr-4"
                                    :src="Product.image" />
                                <div class="flex-grow">
                                    <h2 class="">
                                        <span class="font-bold text-lg">{{ Product.name }} </span>
                                        <span class="italic ml-2">{{ Product.batchNumber }}</span>
                                    </h2>
                                    <p class="">
                                        <span class="font-bold text-gray-500">Price </span>
                                        <span class=" ml-1 text-gray-500"> {{ Product.salePrice }} Frw </span> -
                                        <span class="font-bold text-gray-500">Quantity</span>
                                        <span class="ml-1 text-gray-500"> {{ Product.currentStock }} </span>
                                    </p>
                                    <p class="">
                                        <span class="font-bold text-gray-500">Expire date</span>
                                        <span class="ml-3  text-green-600"> {{ Product.expireDate }} </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class="text-gray-500 text-center p-32">No products found</div>
                    <div class="mt-auto flex justify-between items-center pt-4 ">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing
                                <span class="font-medium">{{ (currentPage - 1) * itemsPerPage + 1 }}</span>
                                to
                                <span class="font-medium">{{ Math.min(currentPage * itemsPerPage, total) }}</span>
                                of
                                <span class="font-medium">{{ total }}</span>
                                results
                            </p>
                        </div>
                        <div class="flex items-center">
                            <label for="itemsPerPage" class="text-sm font-medium text-gray-700">Items per
                                page:</label>
                            <select id="itemsPerPage" v-model="state.selectedItemsPerPage" @change="updateItemsPerPage"
                                class="ml-2 border-gray-300 rounded">
                                <option :value="5">5</option>
                                <option :value="10">10</option>
                                <option :value="20">20</option>
                                <option :value="50">50</option>
                                <option :value="100">100</option>
                            </select>
                        </div>
                        <div class="flex space-x-2">
                            <button @click="prevPage" :disabled="currentPage === 1"
                                class="px-4 py-2 bg-white rounded hover:bg-gray-300 disabled:opacity-70 border border-gray-300">
                                Previous
                            </button>

                            <button @click="nextPage" :disabled="currentPage === lastPage"
                                class="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50">
                                Next
                            </button>
                        </div>
                    </div>
                </div>

                <div class="w-1/2 p-4 rounded-lg border border-gray-300 h-full flex flex-col">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Cart</h3>
                    <div v-if="cart.length === 0" class="text-gray-500 text-center p-6">No products in cart</div>
                    <div v-else class="overflow-y-auto" style="max-height: 550px;">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Product Name
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Price
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Quantity
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr v-for="(product, index) in cart" :key="index" class="hover:bg-gray-100">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ product.name }} | {{ product.batchNumber }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ product.salePrice }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <input type="number" v-model.number="product.units"
                                            @change="updateQuantity(index, product.units)"
                                            class="w-16 p-2 border border-gray-300 rounded" />

                                        <span v-if="product.soldInSubUnit == 1" class="ml-2">
                                            <select v-model="product.movingUnit" class="border border-gray-300 rounded">
                                                <option value="sub">Sub Units / {{ product.quantityUnit }}</option>
                                                <option value="main">Main Units / {{ product.packagingUnit }}</option>
                                            </select>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <button @click="removeFromCart(index)"
                                            class="bg-red-500 text-white py-1 px-2 rounded">
                                            <i class="fa-solid fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="flex justify-center border-t p-3 mt-auto">
                        <p>Total Items: {{ cart.length }}</p>
                    </div>
                </div>
            </div>

            <div class="hidden xl:flex  mt-5 space-x-4 max-h-content h-auto">

                <div class="w-1/3 p-4 rounded-lg border border-gray-300 bg-zata-background-dark">
                    <p class="font-bold text-xl flex justify-center">Total </p>
                    <div class="mt-5 p-5">
                        <div id="total-card" class="space-y-4">
                            <div class="flex justify-between items-center">
                                <p class="text-lg font-semibold text-gray-700">Total:</p>
                                <p class="text-lg font-semibold text-gray-900"><span>{{ subtotal }}</span> Frw</p>
                            </div>
                            <div class="flex justify-between items-center" v-if="taxA">
                                <p class="text-lg font-semibold text-gray-700">Tax A:</p>
                                <p class="text-lg font-semibold text-gray-900"><span>{{ taxA }}</span> Frw</p>
                            </div>
                            <div class="flex justify-between items-center" v-if="taxB">
                                <p class="text-lg font-semibold text-gray-700">Tax B 18%:</p>
                                <p class="text-lg font-semibold text-gray-900"><span>{{ taxB }}</span> Frw</p>
                            </div>
                            <div class="flex justify-between items-center" v-if="taxC">
                                <p class="text-lg font-semibold text-gray-700">Tax C:</p>
                                <p class="text-lg font-semibold text-gray-900"><span>{{ taxC }}</span> Frw</p>
                            </div>
                            <div class="flex justify-between items-center" v-if="taxD">
                                <p class="text-lg font-semibold text-gray-700">Tax D:</p>
                                <p class="text-lg font-semibold text-gray-900"><span>{{ taxD }}</span> Frw</p>
                            </div>
                            <div class="flex justify-between items-center" v-if="afterInsuranceDiscount">
                                <p class="text-lg font-semibold text-gray-700">After Insurance Discount:</p>
                                <p class="text-lg font-semibold text-gray-900"><span>{{ afterInsuranceDiscount }}</span>
                                    Frw</p>
                            </div>
                            <div class="flex justify-between items-center" v-if="insuranceDiscount">
                                <p class="text-lg font-semibold text-gray-700">Insurance Discount:</p>
                                <p class="text-lg font-semibold text-gray-900"><span>{{ insuranceDiscount }}</span> Frw
                                </p>
                            </div>
                            <div class="flex justify-between items-center" v-if="discountPercentage">
                                <p class="text-lg font-semibold text-gray-700">Discount Percentage:</p>
                                <p class="text-lg font-semibold text-gray-900"><span>{{ discountPercentage }}</span> %
                                </p>
                            </div>
                            <div class="flex justify-between items-center" v-if="insuranceName">
                                <p class="text-lg font-semibold text-gray-700">Insurance Name:</p>
                                <p class="text-lg font-semibold text-gray-900"><span>{{ insuranceName }}</span></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-2/3 h-full">
                    <form @submit.prevent="submitOrder" class="w-full flex  h-full ">
                        <div class="w-1/2 p-5 border border-gray-300 rounded-lg ">

                            <label for="paymentMethodID" class="font-bold text-xl flex justify-center">Payment
                                Method</label>
                            <RadioOptionGroup class="mt-5" v-model="form.paymentMethodID" :options="options"
                                name="paymentMethodID" label="" />
                        </div>
                        <div class="w-1/2 p-5 border ml-4 rounded-lg border-gray-300">
                            <label for="customer" class="font-bold text-xl px-4 flex justify-center">Customer</label>
                            <div class="px-4 mt-5">
                                <select id="customer" v-model="form.customerID"
                                    class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm">
                                    <option :value="null"> Select Customer</option>
                                    <option v-for="customer in Customers" :key="customer.id" :value="customer.id">{{
                                        customer.name }}
                                    </option>
                                </select>

                                <div class="mb-4 mt-4" v-if="isEBM && form.clientTin !== null">
                                    <!-- <label for="purchaseCode" class="block text-sm font-medium text-gray-700">Purchase
                                        Code</label>
                                    <input type="text" id="purchaseCode" v-model="form.purchaseCode"
                                        class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"> -->
                                        <InputLabel for="purchaseCode" value="Purchase Code" />
                                        <TextInput id="purchaseCode" v-model="form.purchaseCode" type="text" />
                                        <InputError class="mt-2" :message="form.errors.purchaseCode" />
                                </div>
                                <div class="flex justify-between gap-2 pt-6">
                                    <DangerButton class="w-72" @click="clearCart"
                                        :class="{ 'cursor-not-allowed': cart.length === 0 }">
                                        Clear
                                    </DangerButton>
                                    <PrimaryButton :disabled="cart.length === 0 || form.processing" class="w-72"
                                        :class="{ 'cursor-not-allowed ': cart.length === 0, '': cart.length > 0 }">
                                       Generate Quotation 
                                    </PrimaryButton>
                                </div>

                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </section>

        <div class="block xl:hidden">

            <p class="text-center text-gray-500 text-lg py-32">
                Please use a larger screen to access this feature
            </p>
        </div>
    </AppLayout>
</template>