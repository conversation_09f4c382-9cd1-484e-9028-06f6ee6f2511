<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use DateTimeInterface;

class OrderReceiptSignature extends Model
{
    /** @use HasFactory<\Database\Factories\OrderReceiptSignatureFactory> */
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'OrderReceiptSignature';

    protected $fillable = [
        'receiptNumber',
        'internalData',
        'receiptSignature',
        'totalReceiptNumber',
        'vsdcReceiptPublishDate',
        'sdcId',
        'mrcNumber',
        'order_id',
        'invoiceNumber'
    ];

    protected $casts = [
        'vsdcReceiptPublishDate' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected function serializeDate(DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function getVsdcReceiptPublishDateAttribute($value)
    {
        return \Carbon\Carbon::createFromTimestamp($value)->format('Y-m-d H:i:s');
    }


}
