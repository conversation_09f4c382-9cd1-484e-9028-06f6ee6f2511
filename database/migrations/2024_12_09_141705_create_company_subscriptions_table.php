<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('AccountSubscription', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->references('id')->on('users')->onDelete('restrict');
            $table->boolean('isActive')->default(true);
            $table->bigInteger('startDate');
            $table->bigInteger('endDate')->nullable();
            $table->string('subscriptionType');
            $table->bigInteger('billingPeriod')->nullable();
            $table->string('price')->nullable()->default(0);
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();  
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('AccountSubscription');
    }
};
