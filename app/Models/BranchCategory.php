<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BranchCategory extends Model
{
    /** @use HasFactory<\Database\Factories\BranchCategoryFactory> */
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'BranchCategory';

    protected $fillable = [
        'name',
        'description',
        'isActive',
    ];

    protected $casts = [
        'isActive' => 'boolean',
    ];
}
