<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/vue3';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ref, onMounted, computed } from 'vue';
import { useForm } from '@inertiajs/vue3';
import { ChevronDown, ChevronUp } from 'lucide-vue-next';

const props = defineProps({
  Subscription: Object,
});

const subscription = props.Subscription?.subscription;
const analytics = props.Subscription?.analytics;

const isLoading = ref(true);
const updatingSubscription = ref(false);
const expandedCompanies = ref({});

const updateSubscriptionForm = useForm({
  subscriptionType: subscription?.subscriptionType || '',
  billingPeriod: subscription?.billingPeriod || 'Monthly',
});

const subscriptionPlans = [
  { name: 'Basic', allowed_branches: 2, allowed_products_per_branch: 200 },
  { name: 'Premium', allowed_branches: 'unlimited', allowed_products_per_branch: 'unlimited' },
  { name: 'Enterprise', allowed_branches: 'unlimited', allowed_products_per_branch: 'unlimited', custom_plugins: true, ai_assistant: true, api_access: true },
];

const subscriptionAnalytics = computed(() => {
  const plan = subscriptionPlans.find(p => p.name === subscription?.subscriptionType);
  if (!plan || !analytics) return { isQuotaReached: false, allowedBranches: 0, allowedProducts: 0, totalProducts: 0 };

  const totalBranches = analytics.total_branches;
  const isQuotaReached = plan.allowed_branches !== 'unlimited' && totalBranches >= plan.allowed_branches;
  const totalProducts = analytics.companies.reduce((sum, company) =>
    sum + company.branches.reduce((bSum, branch) => bSum + (branch.total_products || 0), 0), 0);
  return {
    isQuotaReached,
    allowedBranches: plan.allowed_branches,
    allowedProducts: plan.allowed_products_per_branch,
    totalProducts,
  };
});

onMounted(() => {
  setTimeout(() => {
    isLoading.value = false;
  }, 1000);
});

const skeletonRows = (count) => Array.from({ length: count }, (_, i) => i);

const toggleCompany = (index) => {
  expandedCompanies.value[index] = !expandedCompanies.value[index];
};

const openUpdateSubscriptionModal = () => {
  updateSubscriptionForm.subscriptionType = subscription?.subscriptionType || '';
  updateSubscriptionForm.billingPeriod = subscription?.billingPeriod || 'Monthly';
  updatingSubscription.value = true;
};

const updateSubscription = () => {
  updateSubscriptionForm.put(route('subscription.update'), {
    preserveScroll: true,
    preserveState: true,
    onSuccess: () => {
      updatingSubscription.value = false;
      updateSubscriptionForm.reset();
    },
  });
};
</script>

<template>
  <AppLayout title="Subscription Dashboard">
    <template #header>
      <div class="sticky top-0 bg-white shadow-sm z-10 py-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center">
          <h2 class="text-2xl font-semibold text-gray-900">Subscription Dashboard</h2>

        </div>
      </div>
    </template>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-end">
    <Button variant="default" as-child class="bg-green-800 hover:bg-green-900 text-white">
            <Link :href="route('subscription.update')">Update Subscription</Link>
          </Button>
          </div>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-6">
      <!-- Plan Card for Premium/Enterprise -->
      <Card v-if="subscription && ['Premium', 'Enterprise'].includes(subscription.subscriptionType)" class="border bg-gray-100">
        <CardHeader class="bg-white">
          <CardTitle class="text-lg font-medium text-gray-900">Your {{ subscription.subscriptionType }} Plan</CardTitle>
        </CardHeader>
        <CardContent class="p-6">
          <div class="space-y-4">
            <p class="text-sm text-gray-600">Enjoy the full power of your {{ subscription.subscriptionType }} plan with exclusive features.</p>
            <ul class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <li class="flex items-center text-sm text-gray-900">
                <svg class="h-5 w-5 text-green-800 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>
                Unlimited Branches
              </li>
              <li class="flex items-center text-sm text-gray-900">
                <svg class="h-5 w-5 text-green-800 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>
                Unlimited Users
              </li>
              <li class="flex items-center text-sm text-gray-900">
                <svg class="h-5 w-5 text-green-800 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>
                Unlimited Products per Branch
              </li>
              <li v-if="subscription.subscriptionType === 'Enterprise'" class="flex items-center text-sm text-gray-900">
                <svg class="h-5 w-5 text-green-800 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>
                Custom Plugins & API Access
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>

      <!-- Subscription Overview -->
      <Card class="border">
        <CardHeader class="bg-gray-50">
          <CardTitle class="text-lg font-medium text-gray-900">Subscription Overview</CardTitle>
        </CardHeader>
        <CardContent class="p-6">
          <div v-if="isLoading" class="space-y-4">
            <div v-for="i in skeletonRows(3)" :key="i" class="h-4 w-3/4 bg-gray-200 rounded animate-pulse"></div>
          </div>
          <div v-else-if="subscription" class="space-y-6">
            <div v-if="subscriptionAnalytics.isQuotaReached" class="bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
              <p class="text-sm text-red-700">
                You have reached your branch quota of {{ subscriptionAnalytics.allowedBranches }} for the {{ subscription.subscriptionType }} plan.
                <Link :href="route('subscription.update')" class="underline hover:text-red-900">Upgrade your plan</Link> to add more branches.
              </p>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">Status</p>
                <p class="mt-1 text-base font-semibold text-gray-900">{{ subscription.isActive ? 'Active' : 'Inactive' }}</p>
              </div>
              <div>
                <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</p>
                <p class="mt-1 text-base font-semibold text-gray-900">{{ subscription.subscriptionType }}</p>
              </div>
              <div>
                <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</p>
                <p class="mt-1 text-base font-semibold text-gray-900">{{ subscription.startDate }}</p>
              </div>
              <div>
                <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</p>
                <p class="mt-1 text-base font-semibold text-gray-900">{{ subscription.endDate ?? 'Unlimited' }} </p>
              </div>
              <div>
                <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">Billing Period</p>
                <p class="mt-1 text-base font-semibold text-gray-900">{{ subscription.billingPeriod ?? 'Monthly' }}</p>
              </div>
              <div>
                <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">Total Branches</p>
                <p class="mt-1 text-base font-semibold text-gray-900">{{ analytics.total_branches }}</p>
              </div>
              <div>
                <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">Total Products</p>
                <p class="mt-1 text-base font-semibold text-gray-900">
                  {{ subscriptionAnalytics.totalProducts }} / {{ subscriptionAnalytics.allowedProducts === 'unlimited' ? 'Unlimited' : subscriptionAnalytics.allowedProducts }}
                </p>
              </div>
            </div>
          </div>
          <div v-else class="py-8 text-center">
            <p class="text-sm text-gray-500">No subscription details available</p>
          </div>
        </CardContent>
      </Card>

      <!-- Company Analytics -->
      <div class="space-y-4">
        <h3 class="text-lg font-medium text-gray-900">Company Analytics</h3>
        <Card v-if="isLoading" v-for="i in skeletonRows(2)" :key="i" class="border-0">
          <CardHeader class="bg-gray-50 py-3">
            <div class="h-5 w-1/2 bg-gray-200 rounded animate-pulse"></div>
          </CardHeader>
          <CardContent class="p-4">
            <div v-for="j in skeletonRows(2)" :key="j" class="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
          </CardContent>
        </Card>
        <Card v-else v-for="(company, cIndex) in analytics.companies" :key="`company-${cIndex}`" class="border">
          <CardHeader class="bg-gray-50 py-3 cursor-pointer hover:bg-gray-100 transition-colors" @click="toggleCompany(cIndex)">
            <div class="flex justify-between items-center">
              <p class="text-base font-medium text-gray-900">{{ cIndex + 1 }}. {{ company.name }}</p>
              <component :is="expandedCompanies[cIndex] ? ChevronUp : ChevronDown" class="h-5 w-5 text-gray-500" />
            </div>
          </CardHeader>
          <CardContent v-if="expandedCompanies[cIndex]" class="p-4">
            <Table v-if="company.branches.length > 0" class="min-w-full">
              <TableHeader>
                <TableRow>
                  <TableHead class="text-sm font-medium text-gray-700">Branch Name</TableHead>
                  <TableHead class="text-sm font-medium text-gray-700">Total Invoices</TableHead>
                  <TableHead class="text-sm font-medium text-gray-700">Total Products</TableHead>
                  <TableHead class="text-sm font-medium text-gray-700 text-right">Total Amount Sold</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow
                  v-for="(branch, bIndex) in company.branches"
                  :key="`branch-${cIndex}-${bIndex}`"
                  class="hover:bg-gray-50 transition-colors"
                >
                  <TableCell class="text-sm text-gray-900">{{ branch.name }}</TableCell>
                  <TableCell class="text-sm text-gray-900">{{ branch.total_invoices }}</TableCell>
                  <TableCell class="text-sm text-gray-900">{{ branch.total_products || 0 }}</TableCell>
                  <TableCell class="text-sm text-gray-900 text-right">{{ $zataCurrency(branch.total_amount_sold) }}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
            <div v-else class="py-6 text-center">
              <p class="text-sm text-gray-500">No branches available</p>
            </div>
          </CardContent>
        </Card>
        <div v-if="!isLoading && analytics.companies.length === 0" class="text-center py-8">
          <p class="text-sm text-gray-500">No companies or branches available</p>
        </div>
      </div>
    </div>

    <template #sidenav />
    <template #footer />
  </AppLayout>
</template>

<style scoped>

.animate-pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.transition-all {
  transition: all 0.3s ease-in-out;
}
</style>