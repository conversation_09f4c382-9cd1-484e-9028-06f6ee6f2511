<script setup>
defineProps({
    modelValue: {
        type: [String, Number, null],
        required: true,
    },
    options: {
        type: Array,
        required: true,
    },
    label: {
        type: String,
        default: '',
    },
    id: {
        type: String,
        required: true,
    },
    placeholder: {
        type: String,
        default: 'Please select',
    },
    valueKey: {
        type: String,
        default: 'value',
    },
    labelKey: {
        type: String,
        default: 'label',
    },
});

const emit = defineEmits(['update:modelValue']);
</script>

<template>
    <div>
        <label :for="id" v-if="label" class="block font-medium text-gray-900" style="size:14px;">
            {{ label }}
        </label>

        <select :id="id" :value="modelValue" @change="$emit('update:modelValue', $event.target.value)"
            class="border-gray-300 focus:border-gray-400 focus:ring-gray-400 rounded-md mt-1 block w-full" style="
            height:48px;">
            <option value="" disabled>{{ placeholder }}</option>
            <option v-for="option in options" :key="option[valueKey]" :value="option[valueKey]">
                {{ option[labelKey] }}
            </option>
        </select>
    </div>
</template>
