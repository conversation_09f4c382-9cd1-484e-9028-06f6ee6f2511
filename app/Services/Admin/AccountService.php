<?php

namespace App\Services\Admin;

use App\Data\UpdateAccountData;
use App\Exceptions\BadRequest;
use App\Exceptions\NotFound;
use App\Models\AccountSubscription;
use App\Models\User;

class AccountService
{

    // TODO, add Pagination tot users and company 
    public function getAccounts()
    {

        return User::select('id', 'name', 'email')
            ->with('subscription:user_id,isActive,startDate,endDate,subscriptionType,billingPeriod,price')
            ->get();
    }

    public function getAccountById(int $userId)
    {
        $user =  User::select('id', 'name', 'email', 'phone')
            ->with('subscription:user_id,isActive,startDate,endDate,subscriptionType,billingPeriod,price')
            ->where('id', $userId)
            ->first();

        throw_if(is_null($user), NotFound::class, 'User not found');

        return $user;
    }

    public function updateAccount(UpdateAccountData $data, int $userId)
    {
        $user = User::where('id', $userId)->first();

        throw_if(is_null($user), NotFound::class, 'User not found');

        if ($user->email !== $data->email) {
            $user = User::where('email', $data->email)->first();

            throw_if($user, NotFound::class, 'Email already exists');
        }

        $user->name = $data->name;
        $user->email = $data->email;
        $user->phone = $data->phone;
        $user->save();

        $subscription = AccountSubscription::where('user_id', $userId)->first();

        $startDate = \Carbon\Carbon::parse($data->startDate);

        $expectedEndDate = $startDate->copy()->addMonth();

        if (!$subscription) {
            $subscription = AccountSubscription::create([
                'user_id' => $userId,
                'isActive' => $data->isActive,
                'startDate' => strtotime($data->startDate),
                'endDate' => strtotime($expectedEndDate),
                'subscriptionType' => $data->subscriptionType,
                // 'billingPeriod' => $data->billingPeriod, TODO
                'price' => $data->price,
            ]);
        } else {
            $subscription->update([
                'isActive' => $data->isActive,
                'startDate' => strtotime($data->startDate),
                'endDate' => strtotime($expectedEndDate),
                'subscriptionType' => $data->subscriptionType,
                // 'billingPeriod' => $data->billingPeriod, TODO
                'price' => $data->price,
            ]);
        }
    }
}
