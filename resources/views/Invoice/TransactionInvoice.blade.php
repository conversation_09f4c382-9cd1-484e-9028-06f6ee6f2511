<head>
    <title>Zata Point Invoice</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <style>
    </style>
</head>

<body>
    <section id="print-section" class="flex justify-center p-4 print-only mx-auto bg-white " style="font-size: 10px">
        <div class="w-full bg-white rounded mx-auto">
            <div class=" m-5 p-5">
                <section class="flex justify-between">
                    <!-- Invoice Header -->
                    <div class="py-2 border-gray-600 flex justify-start">
                        <div class="">
                            <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('ebm_logo_2.png'))) }}"
                                alt="logo" class="w-44">
                        </div>
                        <div class="py-8">
                            <p class="font-bold text-left " style="font-size: 10px">
                                {{ $company->name }}
                            </p>
                            <p class="text-left">
                                <span class="font-bold " style="font-size: 10px"> Address : </span> {{ $company->address }}
                            </p>
                            <p aria-colspan="2" class="text-left ">
                                <span class="font-bold " style="font-size: 10px"> Tel:</span> {{ $company->phone }}
                            </p>
                            <p aria-colspan="2" class="text-left hidden ">
                                <span class="font-bold " style="font-size: 10px"> Email :</span> {{ $company->email }}
                            </p>
                            <p class="text-left ">
                                <span class="font-bold " style="font-size: 10px"> Tin :</span> {{ $company->tin }}
                            </p>
                            @if ($copy)
                                <p class="text-left py-2">
                                    <span class="font-bold"> COPY </span>
                                </p>
                            @endif
                            <p class="text-left py-1">
                                @if ($transaction->type == 'NS')
                                    <span class="hidden">SALES</span>
                                @elseif ($transaction->type == 'TS')
                                    TRAINING SALES
                                @elseif ($transaction->type == 'TR')
                                    TRAINING REFUND
                                @elseif ($transaction->type == 'PS')
                                    PROFORMA
                                @endif
                            </p>
                            @if ($transaction->type == 'NR' || $transaction->type == 'TR')
                                <div class="text-left font-bold uppercase">
                                    @if ($transaction->type == 'NR')
                                        <span>REFUND</span>
                                    @endif
                                    <br>
                                    REF. NORMAL RECEIPT : {{ $transaction->originalInvoiceNumber }} <br>
                                    <hr class="border-dotted border-black" />
                                    <p>REFUND IS APPROVED ONLY FOR ORIGINAL SALES RECEIPT</p>
                                </div>
                            @endif
                        </div>

                    </div>
                    <div>
                        <div class="flex justify-between">
                            <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('ebm_logo_1.png'))) }}"
                                alt="logo" class="w-40">
                        </div>
                    </div>
                </section>



                @if ($transaction->party->patient_details != null)
                    <div class="" style="font-size: 10px">
                        @if ($transaction->party?->patient_details?->hasAffiliation)
                            <p> Affiliate No: {{ $transaction->party?->patient_details?->affiliationNumber }} </p>
                            <p> Affiliate Name: {{ $transaction->party?->patient_details?->affiliateFirstName }}
                                {{ $transaction->party?->patient_details?->affiliateLastName }} </p>
                            <p> Relationship: {{ $transaction->party?->patient_details?->relationship }} </p>
                        @endif
                        <p> Beneficial No: {{ $transaction->party?->patient_details?->beneficiaryNumber }}</p>
                        <p> Beneficial Name: {{ $transaction->party?->patient_details?->beneficiaryFirstName }}
                            {{ $transaction->party?->patient_details?->beneficiaryLastName }} </p>
                        <p> Date of birth: {{ $transaction->party?->patient_details?->dateOfBirth }} </p>
                        <p> Department: {{ $transaction->party?->patient_details?->department }} </p>
                        @if ($transaction->prescriptionNumber != null)
                            <p> Prescription No: {{ $transaction->prescriptionNumber }} </p>
                        @endif
                        <p> Insurance: {{ $transaction->party?->patient_details?->insurance?->name }} </p>
                        @if ($transaction->insuranceTin != null)
                            <p> Insurance TIN: {{ $transaction->insuranceTin }} </p>
                        @endif
                    </div>
                @endif

                <div class="">
                    <p class="font-semibold text-center">

                        {{ $branch->topMessage }}
                    </p>
                </div>

                <div>
                    <p>
                        <span class="font-bold">
                            Reference :
                        </span>
                        <span>
                            Served by {{ $transaction->user->name }}
                        </span>
                    </p>
                </div>

                <!-- Client Info -->
                <div class="py-2 flex justify-between">
                    <div>
                        <p class="text-left py-3"> INVOICE TO </p>
                        <div class="border border-1 border-gray-500 text-left px-3 py-4 w-80">
                            <p> TIN : {{ $transaction->clientTin }}</p>
                            <p> Name: {{ $transaction->clientName }} </p>
                            <p class="hidden"> Phone : {{ $transaction->clientPhoneNumber }} </p>
                        </div>

                    </div>
                    <div>
                        <p class="py-3 text-white">.</p>
                        <div class="border border-1 border-gray-500 text-left px-3 py-4 w-80">
                            <p> Invoice No : {{ $transaction->invoiceNumber }} </p>
                            <p> Date : {{ $transaction->confirmationDate }}</p>
                        </div>
                    </div>

                </div>

                <div class="w-full">
                    <table class="min-w-full bg-white border border-gray-300">
                        <thead class="bg-gray-200">
                            <tr>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/8 " style="font-size: 10px">Item code</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/8 " style="font-size: 10px">Item Desc</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/8 " style="font-size: 10px">Batch</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/8 " style="font-size: 10px">Exp</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/8 " style="font-size: 10px">Qty</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/8 " style="font-size: 10px">Tax</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/8 " style="font-size: 10px">Unit Price</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/8 " style="font-size: 10px">Total Price</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($transaction->items as $item)
                                <tr>
                                    <td class="text-left px-2 py-2 border border-gray-300  w-1/8" style="font-size: 10px">
                                        {{ $item->productCode }}
                                    </td>
                                    <td
                                        class="text-left px-2 py-2 border border-gray-300  w-2/8 truncate max-w-xs"  style="font-size: 10px">
                                        {{ $item->productName }}
                                    </td>
                                    <td class="text-left px-2 py-2 border border-gray-300  w-1/8"  style="font-size: 10px">
                                        {{ $item->batchNumber }}
                                    </td>
                                    <td class="text-left px-2 py-2 border border-gray-300  w-1/8"  style="font-size: 10px">
                                        {{ date('d-m-Y', strtotime($item->expireDate)) }}
                                    </td>
                                    <td class="text-left px-2 py-2 border border-gray-300 w-1/8"  style="font-size: 10px">
                                        {{ $item->quantity }}
                                    </td>
                                    <td class="text-left px-2 py-2 border border-gray-300  w-1/8"  style="font-size: 10px">
                                        {{ $item->taxName }}
                                    </td>
                                    <td class="text-left px-2 py-2 border border-gray-300  w-1/8"  style="font-size: 10px">
                                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')
                                            -
                                        @endif
                                        {{ number_format($item->price, 2) }}
                                    </td>
                                    <td class="text-left px-2 py-2 border border-gray-300  w-1/8"  style="font-size: 10px">
                                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')
                                            -
                                        @endif
                                        {{ number_format($item->totalAmount, 2) }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                @if ($transaction->type == 'PS' || $transaction->type == 'TS' || $transaction->type == 'TR' || $copy == true)
                    <div class="border-b border-dotted py-2 border-gray-600">
                        <p class="text-center font-black">
                            THIS IS NOT AN OFFICIAL RECEIPT
                        </p>
                    </div>
                @endif
                <div class="flex justify-between mt-12">
                    <!-- VSDC Info -->
                    <div>
                        <!-- SDC Information -->
                        @if ($transaction->signature != null && $transaction->signature !== '')
                            <div class="border-b border-dotted py-2 border-gray-600">
                                <div class="font-bold text-center " style="font-size: 10px">SDC INFORMATION</div>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="text-gray-600 " style="font-size: 10px">
                                        <span class="font-semibold text-gray-600 " style="font-size: 10px"> Date: </span>
                                        <span>
                                            {{ date('d-m-Y', strtotime($transaction->signature?->vsdcReceiptPublishDate)) }}</span>
                                    </div>
                                    <div class="text-gray-600">
                                        <span class="font-semibold text-gray-600 " style="font-size: 10px"> Time: </span>
                                        <span>
                                            {{ date('h:i A', strtotime($transaction->signature?->vsdcReceiptPublishDate)) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-2">
                                    <span class="font-semibold text-gray-600 " style="font-size: 10px">SDC ID:</span>
                                    <span class="text-gray-600 " style="font-size: 10px">
                                        {{ $transaction->signature?->sdcId }}
                                    </span>
                                </div>
                                <div class="flex justify-between items-center mt-2">
                                    <span class="font-semibold text-gray-600 " style="font-size: 10px">RECEIPT NUMBER:</span>
                                    <div class="text-gray-600 " style="font-size: 10px">
                                        {{ $transaction->signature?->receiptNumber }} /
                                        {{ $transaction->signature?->totalReceiptNumber }} &nbsp;
                                        &nbsp;
                                        <!-- <span v-if="Copy">
                                    <span v-if="Order.type == 'NS'"> CS</span>
                                    <span v-if="Order.type == 'NR'"> CR</span>
                                </span>
                                <span v-else>
                                    {{-- {{ Order . type }} --}}
                                </span> -->
                                        <span>

                                            {{ $transaction->type }}
                                        </span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center mt-2">
                                    <div class="font-semibold text-gray-600">Internal Data:</div>
                                    <div class="text-gray-600" style="font-size: 10px">

                                        @if ($transaction->signature?->internalData != null)
                                            {{ $transaction->signature?->internalData }}
                                        @endif
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="font-semibold text-gray-600 " style="font-size: 10px">Receipt Signature:</div>
                                    <div class="text-gray-600 " style="font-size: 10px">
                                        @if ($transaction->signature?->receiptSignature != null)
                                            {{ $transaction->signature?->receiptSignature }}
                                        @endif
                                    </div>
                                </div>

                            </div>
                        @endif
                        @if ($transaction->signature != null)
                            <div class="border-b border-dotted py-2 border-gray-600">
                                <div class="flex justify-between items-center mt-2">
                                    <span class="text-gray-600 font-semibold " style="font-size: 10px">RECEIPT NUMBER</span>
                                    <span class="text-gray-600">
                                        {{ $transaction->signature?->invoiceNumber }}</span>
                                </div>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="text-gray-600">
                                        <span class="font-semibold " style="font-size: 10px"> Date: </span>

                                        {{ date('d-m-Y', strtotime($transaction->signature?->vsdcReceiptPublishDate)) }}
                                    </div>
                                    <div class="text-gray-600">
                                        <span class="font-semibold " style="font-size: 10px"> Time: </span>

                                        {{ date('h:i A', strtotime($transaction->signature?->vsdcReceiptPublishDate)) }}
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-2">
                                    <div class="text-gray-600 text-xs font-semibold">MRC:</div>
                                    <div class="text-gray-600 xs">
                                        {{ $branch->mrc }}
                                    </div>
                                </div>

                            </div>
                        @endif

                        <!-- Thank You Message -->
                        <div class="py-1">
                            <div class="font-bold text-center hidden">THANK YOU</div>
                            <p class="text-center"> Version V1.0.0 Powered by RRA_VSDC </p>
                        </div>

                    </div>
                    <div>

                        @if ($transaction->salesTypeCode !== 'P' && $transaction->signature != null)
                            <div class="flex justify-center p-1 mt-2">
                                {{ QrCode::generate('Demo QR code data for EBM Invoice from HIQ Zata') }}
                            </div>
                        @endif

                        <div class="font-semibold text-center max-w-lg mt-3">{{ $branch->bottomMessage }}</div>

                    </div>

                    <!-- Totals -->
                    <div>
                        <div class="border-b border-dotted py-2 border-gray-600 w-64">
                            <div class="flex justify-between items-center">
                                <div class="font-bold">TOTAL</div>
                                <div class="font-semibold">
                                    @if ($transaction->type == 'NR' || $transaction->type == 'TR')
                                        -
                                    @endif
                                    {{ number_format($transaction->totAmt, 2, '.', ',') }} Frw
                                </div>
                            </div>
                            @if ($transaction->taxblAmtA > 0)
                                <div class="flex justify-between items-center mt-2">
                                    <div class="">TOTAL A-EX</div>
                                    <div class="text-gray-600">
                                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')
                                            -
                                        @endif
                                        {{ number_format($transaction->taxblAmtA, 2, '.', ',') }}
                                    </div>
                                </div>
                            @endif

                            @if ($transaction->taxblAmtB > 0)
                                <div class="flex justify-between items-center mt-2">
                                    <div class="">TOTAL B-18%</div>
                                    <div class="text-gray-600">
                                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')
                                            -
                                        @endif
                                        {{ number_format($transaction->taxblAmtB, 2, '.', ',') }}
                                    </div>
                                </div>
                            @endif
                            @if ($transaction->taxAmtB > 0 || $transaction->taxAmtB < 0)
                                <div class="flex justify-between items-center mt-2">
                                    <div class="">TOTAL TAX B</div>
                                    <div class="text-gray-600">
                                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')
                                            -
                                        @endif
                                        {{ number_format($transaction->taxAmtB, 2, '.', ',') }}
                                    </div>
                                </div>
                            @endif
                            @if ($transaction->taxblAmtC > 0)
                                <div class="flex justify-between items-center mt-2">
                                    <div class="">TOTAL C</div>
                                    <div class="text-gray-600">
                                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')
                                            -
                                        @endif
                                        {{ number_format($transaction->taxblAmtC, 2, '.', ',') }}
                                    </div>
                                </div>
                            @endif
                            @if ($transaction->taxblAmtD > 0)
                                <div class="flex justify-between items-center mt-2">
                                    <div class="">TOTAL D</div>
                                    <div class="text-gray-600">
                                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')
                                            -
                                        @endif
                                        {{ number_format($transaction->taxblAmtD, 2, '.', ',') }}
                                    </div>
                                </div>
                            @endif
                            <div class="flex justify-between items-center mt-2">
                                <div class="" style="font-size: 10px">TOTAL TAX</div>
                                <div class="text-gray-600 " style="font-size: 10px">
                                    @if ($transaction->type == 'NR' || $transaction->type == 'TR')
                                        -
                                    @endif
                                    {{ number_format($transaction->totTaxAmt, 2, '.', ',') }}
                                </div>
                            </div>
                        </div>
                        @if ($transaction->party?->patient_details != null)
                            <div class="mt-4 border p-4">
                                <p> <span class="font-bold">Insurance</span> :
                                    <span>{{ number_format($transaction->party->patient_details->percentage, 2, '.', ',') }}
                                        %</span> </p>
                                <p> <span class="font-bold">Insurance Discount</span> :
                                    {{ number_format(($transaction->totAmt * $transaction->party->patient_details->percentage) / 100, 2, '.', ',') }} Frw 
                                </p>
                                <p> <span class="font-bold">Amount to be paid</span> :
                                    {{ number_format($transaction->totAmt - ($transaction->totAmt * $transaction->party->patient_details->percentage) / 100, 2, '.', ',') }} Frw
                                </p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>
</body>

</html>
