<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";
import TableSection from '@/Components/TableSection.vue';
import { ref, watch, reactive, computed } from 'vue';
import PrimaryLink from '@/Components/PrimaryLink.vue';

const props = defineProps({
    Users: Array,
});

const headers = ["Name", "Email", "Role", "Branch", "Actions"];
const rows = computed(() => {
    return props.Users.map((user) => ({
        name: user.name,
        email: user.email,
        role: user.role,
        defaultBranch: user.defaultBranch,
        id: user.id
    }));
});
</script>

<template>
    <AppLayout title="Users ">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Users
            </h2>
        </template>
        <PrimaryLink :href="route('user.createInvitation')">
            Invite User
        </PrimaryLink>
        <div v-if="rows.length > 0"
            class="overflow-x-auto flex flex-col divide-y divide-gray-200 rounded-lg border border-gray-300 mt-7"
            style="min-height: 500px;">
            <div class="flex-grow">
                <table class="min-w-full divide-y divide-gray-200 table-auto">
                    <thead class="bg-gray-100">
                        <tr>
                            <th v-for="(header, index) in headers" :key="index"
                                class="px-6 py-3 text-left text-sm font-semibold text-gray-600 tracking-wider">
                                {{ header }}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="(row, index) in rows" :key="index" class="hover:bg-gray-100">
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                <span>{{ row.name !== null && row.name !== undefined ? row.name : '-' }}</span>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                <span>{{ row.email !== null && row.email !== undefined ? row.email : '-' }}</span>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                <span>{{ row.role !== null && row.role !== undefined ? row.role : '-' }}</span>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                <span>{{ row.defaultBranch !== null && row.defaultBranch !== undefined ? row.defaultBranch : '-' }}</span>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                <Link :href="route('user.update', row.id)" class="text-zata-primary-light hover:underline">
                                    Edit
                                </Link>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div v-else class="mt-5 text-center text-gray-500 py-56 rounded-lg border border-gray-300">
            nothing
        </div>
    </AppLayout>
</template>