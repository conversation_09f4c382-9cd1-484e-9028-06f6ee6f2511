<?php

namespace Database\Factories;

use App\Models\BranchCategory;
use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CompanyBranch>
 */
class CompanyBranchFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [

            "company_id" => Company::factory(),
            "name" => $this->faker->name(),
            "address" => $this->faker->address(),
            "branchCode" => $this->faker->word(),
            "mode" => 'T',
            "mrc" => 'hiqDemo',
            "topMessage" => "Welcome",
            "bottomMessage" => "Goodbye",
            "currency" => 'RWF',
            "isActive" => true,
            "isEBM" => false,
            "isPrimary" => true,
            "branch_category_id" => BranchCategory::factory(),
            "deviceSerial" => $this->faker->word(),
            "cluster" => 'demo',
            "clusterName" => 'demo',
            "isInitialised" => false
        ];
    }
}
