<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('UserInvitation', function (Blueprint $table) {
            $table->id();
            $table->string('email');
            $table->foreignId('company_id')->references('id')->on('Company')->onDelete('restrict');
            $table->foreignId('permission_id')->references('id')->on('RolePermission')->onDelete('restrict');
            $table->foreignId('branch_id')->references('id')->on('CompanyBranch')->onDelete('restrict');
            $table->boolean('isAccepted')->default(false);
            $table->bigInteger('expireAt')->nullable();
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('UserInvitation');
    }
};
