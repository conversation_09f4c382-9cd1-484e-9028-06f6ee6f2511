<?php

namespace App\Services;

use App\Data\NewUserData;
use App\Data\NewUserInvitationData;
use App\Data\UpdateCompanyUserData;
use App\Exceptions\Forbidden;
use App\Exceptions\NotFound;
use App\Exceptions\ServerError;
use App\Mail\InvitationMail;
use App\Models\Affiliate;
use App\Models\Company;
use App\Models\CompanyBranch;
use App\Models\RolePermission;
use App\Models\User;
use App\Models\UserInvitation;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserService
{

    public function __construct(
        protected RoleService $roleService,
        protected CompanyService $companyService,
        protected AffiliateService $affiliateService,
        protected AccountSubscriptionService $accountSubscription
    ) {}

    public function storeUser(NewUserData $data)
    {

        if (!is_null($data->affiliateCode)) {
            $affiliate = Affiliate::where('affiliateCode', $data->affiliateCode)->first();
            throw_if(!$affiliate, NotFound::class, 'Affiliate not found');
        }

        try {

            DB::beginTransaction();
            $user =  User::create([
                'name' => $data->name,
                'email' => $data->email,
                'password' => bcrypt($data->password),
                'affiliateCode' => $data->affiliateCode,
            ]);

            $this->affiliateService->createAffiliateCode($user->id);
            $this->accountSubscription->storeSubscription($user->id);
            $user->sendEmailVerificationNotification();

            DB::commit();
        } catch (\Exception $e) {

            DB::rollBack();
            Log::error($e->getMessage());
            throw_if(true, ServerError::class, 'Something went wrong');
        }
    }

    public function getCompanyUsers(int $companyId): array
    {

        $userRoles = $this->roleService->getCompanyUsers($companyId);

        return $userRoles->map(function ($role) {
            return [
                'id' => $role->user->id,
                'name' => $role->user->name,
                'email' => $role->user->email,
                'role' => $role->permission->name,
                'defaultBranch' => $role->branch?->name,
                'image' => $role->user->profile_photo_url,
            ];
        })->toArray();
    }

    public function createInvitation(NewUserInvitationData $data, Company $company, string  $userName): void
    {

        $validateUser = $this->companyService->checkIfUserIsInCompanyByEmail($data->email, $company->id);

        throw_if($validateUser, Forbidden::class, 'User already exists in this company');

        $validateInvitation = UserInvitation::where('email', $data->email)
            ->where('company_id', $company->id)
            ->where('isAccepted', false)
            ->where('expireAt', '>', strtotime(now()))
            ->exists();

        throw_if($validateInvitation, Forbidden::class, "Invitation already sent to this email.");

        $role = RolePermission::where('id', $data->permissionID)
            ->whereCompanyId($company->id)
            ->first();

        throw_if(is_null($role), NotFound::class, "Permission not found");

        $validateBranch = CompanyBranch::where('id', $data->branchID)
            ->where('company_id', $company->id)
            ->exists();

        throw_if(!$validateBranch, NotFound::class, "Branch not found");

        $invitation = UserInvitation::create([
            'email' => $data->email,
            'company_id' => $company->id,
            'permission_id' => $data->permissionID,
            'branch_id' => $data->branchID,
            'expireAt' => strtotime(now()->addDays(7)),
        ]);

        $invitationLink = config('app.url') . "/user/accept-invitation/{$invitation->id}";
        $invitationMail =  new InvitationMail($userName, $data->name, $company->name, $role->name, $invitationLink);
        Mail::to($data->email)->send($invitationMail->build());
    }

    public function getInvitation(int $invitationId, User $user): array
    {

        $invitation = UserInvitation::where('id', $invitationId)
            ->where('email', $user->email)
            ->with([
                'company',
                'permission',
                'branch'
            ])
            ->first();

        throw_if(is_null($invitation), NotFound::class, "Invitation  not found.");

        throw_if($invitation->expireAt < now(), Forbidden::class, "Invitation has expired.");

        throw_if($invitation->isAccepted, Forbidden::class, "Invitation already accepted");

        return [
            'id' => $invitation->id,
            'company' => $invitation->company->name,
            'permission' => $invitation->permission->name,
            'branch' => $invitation->branch->name,
            'expireAt' => Carbon::parse($invitation->expireAt)->format('Y-m-d H:i:s'),
        ];
    }

    public function getCompanyUser(int $userId, $companyId)
    {

        $userRole = $this->roleService->getCompanyUser($companyId, $userId);

        throw_if(is_null($userRole), Forbidden::class, "User not found");

        return [
            'id' => $userRole->user->id,
            'name' => $userRole->user->name,
            'email' => $userRole->user->email,
            'role' => $userRole->permission->name,
            'defaultBranch' => $userRole->branch?->name,
            'image' => $userRole->user->profile_photo_url,
            'isActive' => $userRole->isActive,
            'permissionID' => $userRole->role_permission_id,
            'branchID' => $userRole->default_branch_id
        ];
    }

    public function updateCompanyUser(int $userId, UpdateCompanyUserData $data, Company $company)
    {

        $userRole = $this->roleService->getCompanyUser($company->id, $userId);

        throw_if(is_null($userRole), Forbidden::class, "User not found");

        throw_if($userRole->role_permission_id == 1, Forbidden::class, "Admin cannot be updated");

        $role = RolePermission::where('id', $data->permissionID)
            ->whereCompanyId($company->id)
            ->first();

        throw_if(is_null($role), NotFound::class, "Permission not found");

        throw_if($role->id == 1, Forbidden::class, "Admin cannot be updated");

        $branch = CompanyBranch::where('id', $data->branchID)
            ->where('company_id', $company->id)
            ->first();

        throw_if(is_null($branch), NotFound::class, "Branch not found");

        $userRole->update([
            'role_permission_id' => $data->permissionID,
            'default_branch_id' => $data->branchID
        ]);
    }

    public function confirmInvitation(int $invitationId, User $user): void
    {

        $invitation = UserInvitation::where('id', $invitationId)
            ->where('email', $user->email)
            ->first();

        throw_if(is_null($invitation), NotFound::class, "Invitation not found.");

        throw_if($invitation->expireAt < now(), Forbidden::class, "Invitation has expired.");

        throw_if($invitation->isAccepted, Forbidden::class, "Invitation already accepted");

        $this->roleService->createRole($user->id, $invitation->company_id, $invitation->permission_id, $invitation->branch_id);

        $invitation->update(['isAccepted' => true]);
    }
}
