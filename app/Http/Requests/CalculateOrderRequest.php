<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CalculateOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "items" => "required|array",
            "items.*.productID" => "required|integer|min:1",
            "items.*.units" => "required|numeric|min:1",
            "items.*.unitPrice" => "required|numeric|min:1",
            "items.*.discountRate" => "required|numeric|min:0|max:100",
            "items.*.batchNumber" => 'required|string|max:100',
            "items.*.movingUnit" => 'nullable|string|max:100|in:main,sub',
        ];
    }
}
