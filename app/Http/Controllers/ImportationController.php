<?php

namespace App\Http\Controllers;

use App\Data\UpdateImportData;
use App\Http\Requests\UpdateImportsRequest;
use App\Services\ImportService;
use App\Services\ProductService;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
use App\Enums\AccountTypeEnums;

class ImportationController extends Controller
{
    protected $user;
    public function __construct(protected ImportService $service, protected ProductService $productService)
    {
        $this->user = Auth::user();
    }
    public function index()
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $response = $this->pagination($this->service->getImport($branch, perPage: $perPage, page: $page));
        return Inertia::render('Import/Index', [
            'Imports' => $response['data'],
            'currentPage' => $response['currentPage'],
            'lastPage' => $response['lastPage'],
            'itemsPerPage' => $response['itemsPerPage'],
            'pageItems' => $response['pageItems'],
            'total' => $response['total'],
        ]);
    }

    public function checkImportation()
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $this->service->checkImportation('**************', $branch);

        return redirect()->route('import.index')->banner('Import Synced');
    }

    public function showImport(int $id)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $import = $this->service->getImportationById($branch, $id);
        $productData  = $this->pagination($this->productService->getProducts($branch->id, perPage: 1000, page: 1, searchQuery: null));
        return Inertia::render('Import/Show', [
            'Import' => $import,
            'Products' => $productData['data']
        ]);
    }

    public function update(UpdateImportsRequest $request, int $id)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $data = UpdateImportData::from($request->validated());
        $this->service->updateImportation($branch, $data, $this->user->id, $id);

        return redirect()->route('import.index')->banner('Import update completed');
    }
}
