<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { computed, reactive, watch } from "vue";
import { useForm, Link } from "@inertiajs/vue3";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';

const props = defineProps({
    Parties: Array,
    currentPage: Number,
    lastPage: Number,
    itemsPerPage: Number,
});

const tableHeaders = ["No", "Image", "Name", "Email", "Phone", "Address"];

const tableRows = computed(() => {
    return props.Parties.map((party) => ({
        id: party.id,
        image: party.image,
        name: party.name,
        email: party.email,
        phone: party.phone,
        address: party.address,
    }));
});

// Pagination logic from TableSection
const paginationState = reactive({
    currentPage: props.currentPage,
    itemsPerPage: props.itemsPerPage,
    totalPages: props.lastPage,
});

const paginationForm = useForm({
    page: paginationState.currentPage,
    perPage: paginationState.itemsPerPage,
});

const handleItemsPerPageChange = (value) => {
    paginationState.itemsPerPage = value;
    paginationState.currentPage = 1;
    updatePagination();
};

const prevPage = () => {
    if (paginationState.currentPage > 1) {
        paginationState.currentPage -= 1;
        updatePagination();
    }
};

const nextPage = () => {
    if (paginationState.currentPage < paginationState.totalPages) {
        paginationState.currentPage += 1;
        updatePagination();
    }
};

const updatePagination = () => {
    paginationForm.page = paginationState.currentPage;
    paginationForm.perPage = paginationState.itemsPerPage;
    paginationForm.get(route('customer.index'), {  // Assuming 'customer.index' as the route
        preserveState: true,
        preserveScroll: true,
    });
};

watch(() => [props.currentPage, props.lastPage], ([newCurrentPage, newLastPage]) => {
    paginationState.currentPage = newCurrentPage;
    paginationState.totalPages = newLastPage;
});

watch(() => props.itemsPerPage, (newItemsPerPage) => {
    paginationState.itemsPerPage = newItemsPerPage;
});
</script>

<template>
    <AppLayout title="Customers">
        <template #header>
            <h2 class="font-semibold text-2xl text-gray-900 leading-tight">
                Company Customers
            </h2>
        </template>

        <div class="py-6 px-4 sm:px-6 lg:px-8">
            <Card class="shadow-lg border border-gray-200">
                <CardHeader class="bg-gray-50 border-b flex justify-between items-center">
                    <CardTitle class="text-xl font-semibold text-gray-800">
                        Customers
                    </CardTitle>
                    <Button as-child>
                        <Link :href="route('customer.new')">
                        <span class="sr-only">New</span>
                        <i class="fa-solid fa-user-plus"></i>
                        </Link>
                    </Button>
                </CardHeader>

                <CardContent class="p-6">
                    <div v-if="tableRows.length > 0" class="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow class="bg-gray-100 hover:bg-gray-100">
                                    <TableHead v-for="header in tableHeaders" :key="header"
                                        class="font-semibold text-gray-700 py-4 px-6 text-left uppercase text-xs">
                                        {{ header }}
                                    </TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow v-for="row in tableRows" :key="row.id" class="hover:bg-gray-50 cursor-pointer"
                                    @click="$inertia.get(route('customer.edit', row.id))">
                                    <TableCell class="py-4 px-6">{{ row.id }}</TableCell>
                                    <TableCell class="py-4 px-6">
                                        <img v-if="typeof row.image === 'string' && (row.image.startsWith('http') || /\.(jpg|jpeg|png|gif)$/i.test(row.image))"
                                            :src="row.image" alt="" class="w-10 h-10 object-cover rounded-full" />
                                        <span v-else>-</span>
                                    </TableCell>
                                    <TableCell class="py-4 px-6 font-medium text-gray-900">{{ row.name || '-' }}
                                    </TableCell>
                                    <TableCell class="py-4 px-6">{{ row.email || '-' }}</TableCell>
                                    <TableCell class="py-4 px-6">{{ row.phone || '-' }}</TableCell>
                                    <TableCell class="py-4 px-6">{{ row.address || '-' }}</TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>

                    <div v-else class="text-center py-12 text-gray-500">
                        No customers found.
                    </div>

                    <div class="flex items-center justify-between mt-6 pt-4 border-t">
                        <div class="flex items-center gap-2">
                            <span class="text-sm text-gray-600">Items per page:</span>
                            <Select v-model="paginationState.itemsPerPage"
                                @update:modelValue="handleItemsPerPageChange">
                                <SelectTrigger class="w-20">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="5">5</SelectItem>
                                    <SelectItem value="10">10</SelectItem>
                                    <SelectItem value="20">20</SelectItem>
                                    <SelectItem value="50">50</SelectItem>
                                    <SelectItem value="100">100</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div class="flex items-center gap-4">
                            <Button variant="outline" @click="prevPage" :disabled="paginationState.currentPage === 1">
                                Previous
                            </Button>
                            <span class="text-sm text-gray-600">
                                Page {{ paginationState.currentPage }} of {{ paginationState.totalPages }}
                            </span>
                            <Button variant="outline" @click="nextPage"
                                :disabled="paginationState.currentPage === paginationState.totalPages">
                                Next
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>