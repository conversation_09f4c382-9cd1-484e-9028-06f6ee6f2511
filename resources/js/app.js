import './bootstrap';
import '../css/app.css';

import { createSSRApp, h } from 'vue'
import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { ZiggyVue } from '../../vendor/tightenco/ziggy';

const appName = import.meta.env.VITE_APP_NAME || 'Zata Point';

const zataCurrency = (value) => {
    if (!value && value !== 0) return '';
    return Number(value).toLocaleString('en-US') + ' Frw';
};

const cleanApp = () => {
    document.getElementById('app').removeAttribute('data-page');
};

document.addEventListener('inertia:finish', cleanApp);

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./Pages/${name}.vue`, import.meta.glob('./Pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        const app = createSSRApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue);

        app.config.globalProperties.$zataCurrency = zataCurrency;

        return app.mount(el);
    },
    progress: {
        color: '#4B5563',
    },
}).then(cleanApp);