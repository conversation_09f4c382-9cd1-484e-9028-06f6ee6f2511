<?php

namespace App\Enums;

enum TransactionTypeEnums: string
{
        // Order Types
    case PURCHASE = 'purchase';
    case SALES = 'sales';
    case SALES_RETURN = 'sales-return';
    case PROFORMA = 'proforma';
    case IMPORT = 'import';
    case DELIVERY_NOTE = 'delivery-note';

        // Transaction Account Source Types
    case EXPENSE = 'expense';
    case OPENING_BALANCE = 'opening-balance';
    case TRANSFER = 'transfer';


        //Inventory Transaction
    case INVENTORY_ADJUSTMENT = 'inventory-adjustment';
    case INVENTORY_TRANSFER = 'inventory-transfer';

        // Payment Transaction
    case PAYMENT = 'payment';
}
