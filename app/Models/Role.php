<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Role extends Model
{
    /** @use HasFactory<\Database\Factories\RoleFactory> */
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'Role';

    protected $fillable = [
        'user_id',
        'company_id',
        'role_permission_id',
        'default_branch_id',
        'isDefault',
        'isActive'
    ];

    protected $casts = [

        'isDefault' => 'boolean',
        'isActive' => 'boolean',
        'created_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function permission(): BelongsTo
    {
        return $this->belongsTo(RolePermission::class, 'role_permission_id');
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(CompanyBranch::class, 'default_branch_id');
    }
}
