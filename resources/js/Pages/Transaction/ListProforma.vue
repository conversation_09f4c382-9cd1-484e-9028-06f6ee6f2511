<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { reactive, watch, computed, ref } from 'vue';
import { useForm } from "@inertiajs/vue3";
import { Link } from '@inertiajs/vue3';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const props = defineProps({
  Transactions: Array,
  currentPage: Number,
  lastPage: Number,
  itemsPerPage: Number,
  Customers: Array,
  Cashiers: Array,
  routeName: { type: String, default: 'transaction.proforma' },
  dynamicUrl: { type: String, default: 'transaction.show' },
});

const zataCurrency = (amount) => amount; 
const showFilters = ref(false);

const tableHeaders = ["NO", "Customer Name", "Total Amount", "Items", "Date", "Status"];

const tableRows = computed(() => {
  return props.Transactions.map((transaction) => ({
    id: transaction.id,
    clientName: transaction.clientName,
    totalAmount: zataCurrency(transaction.totalAmount),
    itemsCount: transaction.itemsCount,
    salesDate: transaction.salesDate,
    isRefunded: transaction.isRefunded ? 'Converted to Sale' : 'Approved',
  }));
});

const paginationState = reactive({
  currentPage: props.currentPage,
  itemsPerPage: props.itemsPerPage,
  totalPages: props.lastPage,
});

const paginationForm = useForm({
  page: paginationState.currentPage,
  perPage: paginationState.itemsPerPage,
  customerID: null,
  userID: null,
  fromDate: null,
  toDate: null,
});

const handleFilterChange = () => {
  paginationState.currentPage = 1;
  updatePagination();
};

const handleItemsPerPageChange = (value) => {
  paginationState.itemsPerPage = value;
  paginationState.currentPage = 1;
  updatePagination();
};

const prevPage = () => {
  if (paginationState.currentPage > 1) {
    paginationState.currentPage -= 1;
    updatePagination();
  }
};

const nextPage = () => {
  if (paginationState.currentPage < paginationState.totalPages) {
    paginationState.currentPage += 1;
    updatePagination();
  }
};

const updatePagination = () => {
  paginationForm.page = paginationState.currentPage;
  paginationForm.perPage = paginationState.itemsPerPage;
  paginationForm.get(route(props.routeName), {
    preserveState: true,
    preserveScroll: true,
  });
};

watch(() => [props.currentPage, props.lastPage], ([newCurrentPage, newLastPage]) => {
  paginationState.currentPage = newCurrentPage;
  paginationState.totalPages = newLastPage;
});

watch(() => props.itemsPerPage, (newItemsPerPage) => {
  paginationState.itemsPerPage = newItemsPerPage;
});
</script>

<template>
  <AppLayout title=" Quatation Transactions">
    <template #header>
      <h2 class="font-semibold text-2xl text-gray-900 leading-tight">
        Quatation Transactions
      </h2>
    </template>

    <div class="py-6 px-4 sm:px-6 lg:px-8">
        <div class="py-4 flex justify-end">
            <Button as-child>
                <Link :href="route('transaction.proforma.create')">Add Quatation</Link>
            </Button>
        </div>
      <Card class="shadow-lg border border-gray-200">
        <CardHeader class="bg-gray-50 border-b flex justify-between items-center">
          <CardTitle class="text-xl font-semibold text-gray-800">
            Transaction Overview
          </CardTitle>
          <Button
            variant="outline"
            @click="showFilters = !showFilters"
            class="flex items-center gap-2"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
            </svg>
            {{ showFilters ? 'Hide Filters' : 'Show Filters' }}
          </Button>
        </CardHeader>

        <CardContent class="p-6">
          <transition name="fade">
            <div v-if="showFilters" class="mb-6">
              <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="space-y-2">
                  <Label class="text-sm font-medium text-gray-700">Customer</Label>
                  <Select v-model="paginationForm.customerID" @update:modelValue="handleFilterChange">
                    <SelectTrigger>
                      <SelectValue placeholder="All Customers" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem :value="null">All Customers</SelectItem>
                      <SelectItem v-for="customer in props.Customers" :key="customer.id" :value="customer.id">
                        {{ customer.name }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div class="space-y-2">
                  <Label class="text-sm font-medium text-gray-700">Cashier</Label>
                  <Select v-model="paginationForm.userID" @update:modelValue="handleFilterChange">
                    <SelectTrigger>
                      <SelectValue placeholder="All Cashiers" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem :value="null">All Cashiers</SelectItem>
                      <SelectItem v-for="cashier in props.Cashiers" :key="cashier.id" :value="cashier.id">
                        {{ cashier.name }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div class="space-y-2">
                  <Label class="text-sm font-medium text-gray-700">From Date</Label>
                  <Input type="date" v-model="paginationForm.fromDate" @change="handleFilterChange" />
                </div>

                <div class="space-y-2">
                  <Label class="text-sm font-medium text-gray-700">To Date</Label>
                  <Input type="date" v-model="paginationForm.toDate" @change="handleFilterChange" />
                </div>
              </div>
            </div>
          </transition>

          <div v-if="tableRows.length > 0" class="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow class="bg-gray-100 hover:bg-gray-100">
                  <TableHead 
                    v-for="header in tableHeaders" 
                    :key="header" 
                    class="font-semibold text-gray-700 py-4 px-6 text-left uppercase text-xs"
                  >
                    {{ header }}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow 
                  v-for="row in tableRows" 
                  :key="row.id"
                  class="hover:bg-gray-50 cursor-pointer"
                  @click="$inertia.get(route(dynamicUrl, row.id))"
                >
                  <TableCell class="py-4 px-6">{{ row.id }}</TableCell>
                  <TableCell class="py-4 px-6">{{ row.clientName || '-' }}</TableCell>
                  <TableCell class="py-4 px-6 font-medium text-green-600">{{ row.totalAmount }}</TableCell>
                  <TableCell class="py-4 px-6">{{ row.itemsCount }}</TableCell>
                  <TableCell class="py-4 px-6">{{ row.salesDate }}</TableCell>
                  <TableCell class="py-4 px-6">
                    <span 
                      :class="{
                        'text-orange-600': row.isRefunded === 'Converted to Sale',
                        'text-green-600': row.isRefunded === 'Approved'
                      }"
                    >
                      {{ row.isRefunded }}
                    </span>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <div v-else class="text-center py-12 text-gray-500">
            No transactions found matching your criteria.
          </div>

          <div class="flex items-center justify-between mt-6 pt-4 border-t">
            <div class="flex items-center gap-2">
              <span class="text-sm text-gray-600">Items per page:</span>
              <Select v-model="paginationState.itemsPerPage" @update:modelValue="handleItemsPerPageChange">
                <SelectTrigger class="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-center gap-4">
              <Button
                variant="outline"
                @click="prevPage"
                :disabled="paginationState.currentPage === 1"
              >
                Previous
              </Button>
              <span class="text-sm text-gray-600">
                Page {{ paginationState.currentPage }} of {{ paginationState.totalPages }}
              </span>
              <Button
                variant="outline"
                @click="nextPage"
                :disabled="paginationState.currentPage === paginationState.totalPages"
              >
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <template #sidenav />
    <template #footer />
  </AppLayout>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>