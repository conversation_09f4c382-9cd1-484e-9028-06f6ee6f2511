<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class NewCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:60',
            'address' => 'nullable|string|max:50',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:50',
            'tin' => 'nullable|string|max:9|min:9',
            'hasInsurance' => 'required|boolean',

            'insuranceID' => 'required_if:hasInsurance,true|nullable|integer|min:1',
            'code' => 'required_if:hasInsurance,true|nullable|string|max:10',
            'percentage' => 'required_if:hasInsurance,true|nullable|integer|min:1|max:100',
            'expirationDate' => 'required_if:hasInsurance,true|nullable|date|after:now',
            'gender' => 'required_if:hasInsurance,true|nullable|string',
            'hasAffiliation' => 'required_if:hasInsurance,true|nullable|boolean',
            'beneficiaryFirstName' => 'required_if:hasInsurance,true|nullable|string|max:30',
            'beneficiaryLastName' => 'required_if:hasInsurance,true|nullable|string|max:30',
            'beneficiaryNumber' => 'required_if:hasInsurance,true|nullable|string|max:30',
            'dateOfBirth' => 'required_if:hasInsurance,true|nullable|date|before:today',
            'department' => 'required_if:hasInsurance,true|nullable|string|max:30',
            'status' => 'required_if:hasInsurance,true|nullable|string',

            'affiliationNumber' => 'required_if:hasAffiliation,true|nullable|string|max:100',
            'affiliateLastName' => 'required_if:hasAffiliation,true|nullable|string|max:30',
            'affiliateFirstName' => 'required_if:hasAffiliation,true|nullable|string|max:30',
            'relationship' => 'required_if:hasAffiliation,true|nullable|string|max:30',
            
        ];
    }

    public function messages(): array
    {
        return [
            'insuranceID.required_if' => 'Select an insurance company',
            'code.required_if' => 'Enter the code',
            'percentage.required_if' => 'Enter the percentage',
            'expirationDate.required_if' => 'Enter the expiration date',
            'gender.required_if' => 'Select gender',
            'hasAffiliation.required_if' => 'Select if the customer has an affiliation',
            'affiliationNumber.required_if' => 'Enter the affiliation number',
            'affiliateFirstName.required_if' => 'Enter the affiliation first name',
            'affiliateLastName.required_if' => 'Enter the affiliation last name',
            'relationship.required_if' => 'Enter the relationship',
            'beneficiaryFirstName.required_if' => 'Enter the beneficiary first name',
            'beneficiaryLastName.required_if' => 'Enter the beneficiary last name',
            'beneficiaryNumber.required_if' => 'Enter the beneficiary number',
            'dateOfBirth.required_if' => 'Enter the date of birth',
            'department.required_if' => 'Enter the department',
            'status.required_if' => 'Select the status',
        ];
    }
}
