<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PurchaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "customerID" => "required|numeric|min:1",
            "transactionDate" => "required|date_format:Y-m-d",
            "note" => "nullable|string|max:200",
            'items' => 'required|array',
            'items.*.productID' => 'required|numeric|min:1',
            'items.*.units' => 'required|numeric|min:1',
        ];
    }

    public function messages(): array
    {

        return [
            'customerID.required' => 'Customer is required',
        ];
    }
}
