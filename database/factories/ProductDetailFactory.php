<?php

namespace Database\Factories;

use App\Enums\ProductStockStatusEnums;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductDetails>
 */
class ProductDetailFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [

            'currentStock' => $this->faker->randomNumber(),
            'purchasePrice' => $this->faker->randomFloat(2, 0, 99999999999.99),
            'salesPrice' => $this->faker->randomFloat(2, 0, 999999999.99),
            'stockQuantityAlert' => $this->faker->randomNumber(),
            'openingStock' => $this->faker->randomNumber(),
            'status' => ProductStockStatusEnums::IN_STOCK->value,
            'expireDate' => 999999999,
            'batchNumber' => $this->faker->word,
            'discountRate' => $this->faker->randomNumber(1, 100),
        ];
    }
}
