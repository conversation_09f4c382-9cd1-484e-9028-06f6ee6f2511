<?php

namespace App\Services;

use App\Data\ConfirmProductSyncData;
use App\Data\NewProductData;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductClass;
use App\Models\ProductDetail;
use App\Models\ProductPackingUnit;
use App\Models\ProductQuantityUnit;
use App\Models\ProductType;
use App\Models\Tax;
use App\Data\ProductTaxData;
use App\Data\StockTransferData;
use App\Data\UpdateProductData;
use App\Data\UpdateProductQuantityData;
use App\Data\UpdateProductQuantityOutData;
use App\Enums\ProductMovingUnitEnums;
use App\Exceptions\BadRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\Country;
use App\Exceptions\NotFound;
use App\Models\BranchProductCategory;
use App\Models\Company;
use App\Models\CompanyBranch;
use App\Models\ProductStockHistory;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Enums\ProductStockStatusEnums;
use App\Enums\TransactionTypeEnums;
use App\Exceptions\Forbidden;
use App\Enums\StockTypeEnums;
use App\Data\EbmNewProductData;
use App\Data\UpdateBatchData;

class ProductService
{
    protected string $endpoint;

    public function __construct(protected EbmService $ebmService)
    {
        $this->endpoint = config('ebm.save_item');
    }

    public function store(NewProductData $data, CompanyBranch $branch, Company $companyData): int
    {

        if ($branch->isEBM) {
            $this->validateEbmProduct($data, $branch->id, $companyData, productId: null);

            $countryCode =  Country::where('id', $data->countryID)->first()->code;
            $packagingUnit = ProductPackingUnit::where('id', $data->packagingUnitID)->first()->code;
            $quantityUnits = ProductQuantityUnit::where('id', $data->quantityUnitID)->first()->code;
            $productType = ProductType::where('name', 'Finished Product')->first();
            $productCategory = ProductCategory::where('name', 'For Sale')->first()->id;

            $lastProductId = Product::where('branch_id', $branch->id)->count() ?? 0;
            $sequence = str_pad($lastProductId + 1, 7, '0', STR_PAD_LEFT);
            $itemCode = $countryCode . $productType->id . $packagingUnit . $quantityUnits . $sequence;
            $tax = Tax::where('id', $data->taxID)->first()->code;

            $itemEbmData = [
                "tin" => (string) $companyData->tin,
                "bhfId" => $branch->branchCode,
                "itemCd" => $itemCode,
                "itemClsCd" => ProductClass::first()->code,
                "itemTyCd" => $productType->code,
                "itemNm" => $data->name,
                "orgnNatCd" => $countryCode,
                "pkgUnitCd" => $packagingUnit,
                "qtyUnitCd" => $quantityUnits,
                "taxTyCd" => $tax,
                "dftPrc" => 100,
                "useYn" => 'Y',
                "isrcAplcbYn" => "N",
                "regrNm" => "Admin",
                "regrId" => "Admin",
                "modrNm" => "Admin",
                "modrId" => "Admin"
            ];

            $payload = EbmNewProductData::from($itemEbmData);
            $this->saveProductToEbm($payload, $branch);
            $product =  Product::create([
                'branch_id' => $branch->id,
                'name' => $data->name,
                'quantity_unit_id' => $data->quantityUnitID,
                'packaging_unit_id' => $data->packagingUnitID,
                'branch_product_category_id' => $data->branchProductCategoryID,
                'category_id' => $productCategory,
                'type_id' => $productType->id,
                'tax_id' => $data->taxID,
                'slug' => Str::slug($data->name),
                'itemCode' => $itemCode,
                'country_id' => $data->countryID,
                'class_id' => ProductClass::first()->id,
                'soldInSubUnit' => $data->soldInSubUnit ?? false,
                'conversionFactor' => $data->conversionFactor ?? 0,
            ]);

            return $product->id;
        } else {
            $this->validateProduct($data, $branch->id, $companyData, productId: null);

            $product =   Product::create([
                'branch_id' => $branch->id,
                'name' => $data->name,
                'tax_id' => $data->taxID,
                'slug' => Str::slug($data->name),
                'quantity_unit_id' => $data->quantityUnitID,
                'packaging_unit_id' => $data->packagingUnitID,
                'itemCode' => 'demo-code',
                'class_id' => ProductClass::first()->id,
                'packaging_unit_id' => ProductPackingUnit::first()->id,
                'branch_product_category_id' => $data->branchProductCategoryID,
                'soldInSubUnit' => $data->soldInSubUnit ?? false,
                'conversionFactor' => $data->conversionFactor ?? 0,
                'description_three' => $data->hasStock
            ]);

            if ($data->hasStock === 'no') {
                $this->createServiceItem($product->id, $branch);
            }

            return $product->id;
        }
    }

    private function createServiceItem(int $productId, CompanyBranch $branch): void {}

    public function confirmProductSync(ConfirmProductSyncData $data, CompanyBranch $branch): void
    {

        $tax = Tax::where('id', $data->taxID)->exists();
        throw_if(!$tax, NotFound::class, 'Tax not found');

        $product = Product::where('id', $data->productID)->first();
        throw_if(is_null($product), NotFound::class, 'Product not found');

        Product::updateOrCreate(
            [
                'itemCode' => $product->itemCode,
                'branch_id' => $branch->id
            ],
            [
                'name' => $product->name,
                'slug' => $product->slug,

                'tax_id' => $data->taxID,
                'quantity_unit_id' => $product->quantity_unit_id,
                'packaging_unit_id' => $product->packaging_unit_id,
                'branch_product_category_id' => $product->branch_product_category_id,
                'class_id' => $product->class_id,
                'soldInSubUnit' => $product->soldInSubUnit ?? false,
                'conversionFactor' => $product->conversionFactor ?? 0,
            ]
        );
    }

    public function increaseProductQuantity(UpdateProductQuantityData $data, CompanyBranch $defaultBranch, int  $productId, int $userId): void
    {

        $product = Product::where('id', $productId)
            ->where('branch_id', $defaultBranch->id)
            ->first();

        throw_if(!$product, NotFound::class, 'Product not found');

        if ($product->soldInSubUnit && $data->movingUnit != null) {

            throw_if(is_null($data->movingUnit), BadRequest::class, 'Moving unit is required');
            if ($data->movingUnit === ProductMovingUnitEnums::MAIN->value) {
                $data->quantity = $data->quantity * $product->conversionFactor;
            }
        }

        if ($product->description_three === 'yes') {
            throw_if(is_null($data->batchNumber), BadRequest::class, 'Batch number is required');
        }

        $productDetail = ProductDetail::where('product_id', $productId)
            ->where('batchNumber', $data->batchNumber)
            ->first();

        try {
            DB::beginTransaction();

            switch ($product->description_three) {
                case 'yes':
                    if (!is_null($productDetail)) {
                        $productDetail->update(array_filter([
                            'currentStock' => $productDetail->currentStock + $data->quantity,
                            'purchasePrice' => $data->purchasePrice ?? 0,
                            'expireDate' => strtotime($data->expireDate),
                            'status' => ProductStockStatusEnums::IN_STOCK->value,
                            'discountRate' => $data->discountRate ?? 0,
                        ], fn($value) => !is_null($value)));
                    } else {
                        ProductDetail::create([
                            'product_id' => $productId,
                            'currentStock' => $data->quantity,
                            'purchasePrice' => $data->purchasePrice ?? 0,
                            'salesPrice' => $data->salePrice,
                            'discountRate' => $data->discountRate ?? 0,
                            'expireDate' => strtotime($data->expireDate),
                            'status' => ProductStockStatusEnums::IN_STOCK->value,
                            'batchNumber' => $data->batchNumber,
                        ]);
                    }

                    break;
                case 'no':
                    if (!is_null($productDetail)) {
                        $productDetail->update(array_filter([
                            'currentStock' => 0,
                            'status' => ProductStockStatusEnums::NO_STOCK->value,
                            'discountRate' => $data->discountRate ?? 0,
                            'expireDate' => null,
                            'purchasePrice' => $data->purchasePrice ?? 0,
                        ]));
                    } else {
                        ProductDetail::create([
                            'product_id' => $productId,
                            'currentStock' => 0,
                            'status' => ProductStockStatusEnums::NO_STOCK->value,
                            'discountRate' => $data->discountRate ?? 0,
                            'expireDate' => null,
                            'purchasePrice' => $data->purchasePrice ?? 0,
                            'batchNumber' => 'no-stock',
                            'salesPrice' => $data->salePrice
                        ]);
                    }
                    break;
            }

            ProductStockHistory::create([
                'branch_id' => $defaultBranch->id,
                'product_id' => $productId,
                'user_id' => $userId,
                'quantity' => $data->quantity,
                'oldQuantity' => '0', // to be revised
                'stockType' => StockTypeEnums::IN->value,
                'orderType' => $data->orderType,
                'description' => $data->description,
                'date' => strtotime(now()),
                'confirmationDate' => strtotime(now()),
                'productCode' => $product->itemCode,
                'batchNumber' => $data->batchNumber,
                'expiryDate' => $data->expireDate,
                'party_id' => $data->partyId
            ]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage());
            throw new BadRequest('An error occurred while updating product quantity');
        }
    }

    public function reduceProductQuantity(UpdateProductQuantityOutData $data, CompanyBranch $defaultBranch, int  $productId, int $userId): void
    {

        $product = Product::where('id', $productId)
            ->where('branch_id', $defaultBranch->id)
            ->first();

        throw_if(!$product, NotFound::class, 'Product not found');

        if ($product->soldInSubUnit) {

            throw_if(is_null($data->movingUnit), BadRequest::class, 'Moving unit is required');
            if ($data->movingUnit === ProductMovingUnitEnums::MAIN->value) {
                $data->quantity = $data->quantity * $product->conversionFactor;
            }
        }

        throw_if(is_null($data->batchNumber), BadRequest::class, 'Batch number is required');

        $productDetail = ProductDetail::where('product_id', $productId)
            ->where('batchNumber', $data->batchNumber)
            ->first();

        throw_if(is_null($productDetail), NotFound::class, 'Invalid batch number');

        if ($product->description_three !== 'no') {
            throw_if($productDetail->currentStock < $data->quantity, BadRequest::class, 'Quantity to reduce is greater than the current stock');
        }

        $newStock = $productDetail->currentStock - $data->quantity;

        try {
            DB::beginTransaction();

            $productDetail->update([
                'currentStock' => $newStock,
                'status' => $newStock > 0 ? ProductStockStatusEnums::IN_STOCK->value :  ProductStockStatusEnums::OUT_OF_STOCK->value,
            ]);

            ProductStockHistory::create([
                'branch_id' => $defaultBranch->id,
                'product_id' => $productId,
                'user_id' => $userId,
                'quantity' => $data->quantity,
                'oldQuantity' => '0', // to be revised
                'stockType' => StockTypeEnums::OUT->value,
                'orderType' => $data->orderType,
                'description' => $data->description,
                'date' => strtotime(now()),
                'confirmationDate' => strtotime(now()),
                'productCode' => $product->itemCode,
                'batchNumber' => $data->batchNumber,
                'expiryDate' => $productDetail->expireDate,
                'party_id' => $data->partyId
            ]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage());
            throw new BadRequest('An error occurred while updating product quantity');
        }
    }

    public function stockTransfer(StockTransferData $data, Company $companyData, CompanyBranch $branch, int $productId, int $userId)
    {

        $product =  Product::where('id', $productId)
            ->where('branch_id', $branch->id)
            ->first();

        throw_if(is_null($product), NotFound::class, 'Product not found');

        $batchNumber = ProductDetail::where('batchNumber', $data->batchNumber)
            ->where('product_id', $product->id)
            ->first();

        throw_if(is_null($batchNumber), NotFound::class, 'Invalid batch number');

        throw_if($batchNumber->currentStock <= 0, Forbidden::class, 'Can not transfer empty stock');

        if (!$data->wholeStock) {

            throw_if($batchNumber->currentStock < $data->quantity, Forbidden::class, 'Insufficient stock quantity');

            if ($product->soldInSubUnit && $data->movingUnit === ProductMovingUnitEnums::MAIN->value) {

                $data->quantity = $data->quantity * $product->conversionFactor;
            }
        }

        $branchCheck = collect($companyData['branches'])->where('id', $data->branchID)->first();

        throw_if(!$branchCheck, NotFound::class, 'Receiving Branch not found');

        try {
            DB::beginTransaction();

            ProductStockHistory::create([
                'branch_id' => $branch->id,
                'product_id' => $product->id,
                'to_branch_id' => $data->branchID,
                'user_id' => $userId,
                'quantity' => $data->quantity ?? $batchNumber->currentStock,
                'oldQuantity' => '0', // TODO
                'stockType' => StockTypeEnums::OUT_TO->value,
                'orderType' => TransactionTypeEnums::INVENTORY_TRANSFER->value,
                'description' => $data->note,
                'date' => strtotime(now()),
                'batchNumber' => $data->batchNumber,
                'confirmationDate' => strtotime(now()),
                'productCode' => $product->itemCode,
            ]);

            ProductStockHistory::create([
                'branch_id' => $branchCheck->id,
                'product_id' => $product->id,
                'productCode' => $product->itemCode,
                'user_id' => $userId,
                'quantity' => $data->quantity ?? $batchNumber->currentStock,
                'oldQuantity' => '0', // TODO
                'stockType' => StockTypeEnums::IN_FROM->value,
                'orderType' => TransactionTypeEnums::INVENTORY_TRANSFER->value,
                'description' => $data->note,
                'date' => strtotime(now()),
                'batchNumber' => $data->batchNumber,
                'to_branch_id' => $branch->id,
                'expiryDate' => $batchNumber->expireDate,
                'confirmationDate' => strtotime(now()),
            ]);

            $newTransferProduct = Product::updateOrCreate(
                [
                    'itemCode' => $product->itemCode,
                    'branch_id' => $data->branchID,
                ],
                [
                    'name' => $product->name,
                    'branch_product_category_id' => $product->branch_product_category_id,
                    'category_id' => $product->category_id,
                    'quantity_unit_id' => $product->quantity_unit_id,
                    'packaging_unit_id' => $product->packaging_unit_id,
                    'type_id' => $product->type_id,
                    'tax_id' => $product->tax_id,
                    'class_id' => $product->class_id,
                    'country_id' => $product->country_id,
                    'soldInSubUnit' => $product->soldInSubUnit,
                    'conversionFactor' => $product->conversionFactor,
                    'description' => $product->description,
                    'description_two' => $product->description_two,
                    'description_three' => $product->description_three,
                ]
            );

            if (!$data->wholeStock) {
                $batchNumber->update([
                    'currentStock' => $batchNumber->currentStock - $data->quantity,
                ]);

                ProductDetail::updateOrCreate([
                    'product_id' => $newTransferProduct->id,
                    'batchNumber' => $batchNumber->batchNumber
                ], [
                    'expireDate' => $batchNumber->expireDate,
                    'currentStock' => $data->quantity,
                    'salesPrice' => $batchNumber->salesPrice,
                    'purchasePrice' => $batchNumber->purchasePrice,
                    'discountRate' => $batchNumber->discountRate,
                    'status' => ProductStockStatusEnums::IN_STOCK->value,
                ]);
            } else {
                ProductDetail::updateOrCreate([
                    'product_id' => $newTransferProduct->id,
                    'batchNumber' => $batchNumber->batchNumber
                ], [
                    'expireDate' => $batchNumber->expireDate,
                    'currentStock' => $batchNumber->currentStock,
                    'salesPrice' => $batchNumber->salesPrice,
                    'purchasePrice' => $batchNumber->purchasePrice,
                    'discountRate' => $batchNumber->discountRate,
                    'status' => ProductStockStatusEnums::IN_STOCK->value,
                ]);

                $batchNumber->update([
                    'currentStock' => 0,
                ]);
            }

            DB::commit();
        } catch (\Exception $e) {

            DB::rollBack();
            Log::error($e->getMessage());
            throw new BadRequest('An error occurred while transferring stock');
        }
    }

    public function getProducts(int $branchId, int $perPage, int $page, $searchQuery): LengthAwarePaginator
    {

        if (is_null($searchQuery)) {
            return  Product::where('branch_id', $branchId)
                ->with(['productDetails', 'productCategory:id,name', 'productQuantityUnit:id,code', 'productPackingUnit:id,code', 'productCountry:id,name', 'productTax:id,code,rate', 'productClass:id,code', 'productType:id,name'])
                ->paginate(perPage: $perPage, page: $page)
                ->through(fn($product) => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'itemCode' => $product->itemCode,
                    'category' => $product->productCategory?->name,
                    'quantityUnit' => $product->productQuantityUnit->code,
                    'packagingUnit' => $product->productPackingUnit?->code,
                    'tax' => $product->productTax->code,
                    'class' => $product->productClass?->code,
                    'type' => $product->productType?->name,
                    'totalStock' => $product->description_three == 'no' ? 'Unlimited' : $product->productDetails->sum('currentStock'),
                    'image' => 'https://ui-avatars.com/api/?name=' . urlencode($product->name) . '&color=7F9CF5&background=EBF4FF',
                ]);
        }

        return Product::search($searchQuery)
            ->where('branch_id', $branchId)
            ->query(fn($query) => $query->with(['productDetails', 'productCategory:id,name', 'productQuantityUnit:id,code', 'productPackingUnit:id,code', 'productCountry:id,name', 'productTax:id,code,rate', 'productClass:id,code', 'productType:id,name']))
            ->paginate(perPage: $perPage, page: $page)
            ->through(fn($product) => [
                'id' => $product->id,
                'name' => $product->name,
                'itemCode' => $product->itemCode,
                'category' => $product->productCategory?->name,
                'quantityUnit' => $product->productQuantityUnit->code,
                'packagingUnit' => $product->productPackingUnit?->code,
                'tax' => $product->productTax->code,
                'class' => $product->productClass?->code,
                'type' => $product->productType?->name,
                'totalStock' => $product->description_three == 'no' ? 'Unlimited' : $product->productDetails->sum('currentStock'),
                'image' => 'https://ui-avatars.com/api/?name=' . urlencode($product->name) . '&color=7F9CF5&background=EBF4FF',
            ]);
    }

    public function getProductsForPurchase(int $branchId, int $perPage, int $page, string|int|null  $searchQuery): LengthAwarePaginator
    {

        return  Product::where('branch_id', $branchId)
            ->with(['productDetails', 'productCategory:id,name', 'productQuantityUnit:id,code', 'productPackingUnit:id,code', 'productCountry:id,name', 'productTax:id,code,rate', 'productClass:id,code', 'productType:id,name'])
            ->paginate(perPage: $perPage, page: $page)
            ->through(fn($product) => [
                'id' => $product->id,
                'name' => $product->name,
                'itemCode' => $product->itemCode,
                'category' => $product->productCategory?->name,
                'quantityUnit' => $product->productQuantityUnit->code,
                'packagingUnit' => $product->productPackingUnit?->code,
                'tax' => $product->productTax->code,
                'class' => $product->productClass?->code,
                'type' => $product->productType?->name,
                'totalStock' => $product->productDetails->sum('currentStock'),
                'image' => 'https://ui-avatars.com/api/?name=' . urlencode($product->name) . '&color=7F9CF5&background=EBF4FF',
                'batchList' => $product->productDetails->map(fn($productDetail) => [
                    'id' => $productDetail->id,
                    'batchNumber' => $productDetail->batchNumber,
                    'expireDate' => $productDetail->expireDate,
                    'stock' => $productDetail->currentStock
                ])
            ]);
    }

    public function getPosProducts(int $branchId, int $perPage, int $page, string|int|null  $searchQuery, int|null $productBranchCategoryID) //: LengthAwarePaginator
    {

        return ProductDetail::whereHas('product', function ($query) use ($branchId, $productBranchCategoryID) {
            $query->where('branch_id', $branchId)
                ->whereProductBranchCategoryId($productBranchCategoryID);
        })
            ->whereIsInStock()
            ->with(['product', 'product.productQuantityUnit:id,code', 'product.productTax:id,code,rate', 'product.productPackingUnit:id,code'])
            ->latest()
            ->when($searchQuery, fn($query) => $query->whereHas('product', fn($query) => $query->where('name', 'like', '%' . $searchQuery . '%')))
            ->paginate(perPage: $perPage, page: $page)
            ->through(fn($productDetail) => [
                'id' => $productDetail->product->id,
                'name' => $productDetail->product->name,
                'itemCode' => $productDetail->product->itemCode,
                'quantityUnit' => $productDetail->product->productQuantityUnit->code,
                'packagingUnit' => $productDetail->product->productPackingUnit->code,
                'soldInSubUnit' => $productDetail->product->soldInSubUnit,
                'conversionFactor' => $productDetail->product->conversionFactor,
                'tax' => $productDetail->product->productTax->code,
                'taxRate' => $productDetail->product->productTax->rate,
                'currentStock' => $productDetail->currentStock,
                'salePrice' => $productDetail->salesPrice,
                'batchNumber' => $productDetail->batchNumber,
                'expireDate' => $productDetail->expireDate,
                'discountRate' => $productDetail->discountRate,
                'image' => 'https://ui-avatars.com/api/?name=' . urlencode($productDetail->product->name) . '&color=7F9CF5&background=EBF4FF',
            ]);
    }

    public function purchaseProducts(int $branchId, int $perPage, int $page, string|int|null  $searchQuery): LengthAwarePaginator
    {

        return Product::where('branch_id', $branchId)
            ->when($searchQuery, fn($query) => $query->where('name', 'like', '%' . $searchQuery . '%'))
            ->with(['productDetails'])
            ->paginate(perPage: $perPage, page: $page)
            ->through(fn($product) => [
                'id' => $product->id,
                'name' => $product->name,
                'itemCode' => $product->itemCode,
                'currentStock' => $product->productDetails->sum('currentStock'),
                'image' => 'https://ui-avatars.com/api/?name=' . urlencode($product->name) . '&color=7F9CF5&background=EBF4FF',
            ]);
    }

    public function  getProductById(int $branchId, int $productId): array
    {

        $product = Product::where('branch_id', $branchId)->where('id', $productId)->exists();
        throw_if(!$product, NotFound::class, 'Product not found');

        $product = Product::where('branch_id', $branchId)
            ->where('id', $productId)
            ->with([
                'productCategory:id,name',
                'productQuantityUnit:id,code',
                'productPackingUnit:id,code',
                'productCountry:id,name',
                'productTax:id,code,rate',
                'productClass:id,code',
                'productType:id,name',
                'productBranchCetegory:id,name',
                'productDetails' => fn($query) => $query
                    ->orWhere('currentStock', '>', 0)
                    ->orWhere('status', ProductStockStatusEnums::NO_STOCK->value)
                    ->orderBy('expireDate', 'desc')
            ])
            ->first();

        $productDetailsArray = $product->productDetails->map(function ($detail) {
            return [
                'id' => $detail->id,
                'currentStock' => $detail->currentStock,
                'purchasePrice' => $detail->purchasePrice,
                'salesPrice' => $detail->salesPrice,
                'expireDate' => $detail->expireDate,
                'batchNumber' => $detail->batchNumber,
                'status' => $detail->status,
                'discountRate' => $detail->discountRate,
            ];
        })->toArray();

        $totalStock = $product->productDetails->sum('currentStock');

        return [
            'id' => $product->id,
            'name' => $product->name,
            'itemCode' => $product->itemCode,
            'category' => $product->productCategory?->name,
            'categoryID' => $product->productCategory?->id,
            'quantityUnit' => $product->productQuantityUnit->code,
            'quantityUnitID' => $product->productQuantityUnit->id,
            'packagingUnit' => $product->productPackingUnit?->code,
            'packagingUnitID' => $product->productPackingUnit?->id,
            'country' => $product->productCountry?->name,
            'countryID' => $product->productCountry?->id,
            'tax' => $product->productTax->code,
            'taxID' => $product->productTax->id,
            'class' => $product->productClass?->code,
            'classID' => $product->productClass?->id,
            'type' => $product->productType?->name,
            'typeID' => $product->productType?->id,
            'branchProductCategoryID' => $product->productBranchCetegory?->id,
            'branchProductCategory' => $product->productBranchCetegory?->name,
            'productDetails' => $productDetailsArray,
            'totalStock' => $totalStock,
            'soldInSubUnit' => $product->soldInSubUnit,
            'conversionFactor' => $product->conversionFactor,
            'image' => "https://ui-avatars.com/api/?name=" . urlencode($product->name) . "&color=7F9CF5&background=EBF4FF",
            'hasStock' => $product->description_three
        ];
    }

    public function getCommonProduct(int $perPage, int $page, string|int|null  $searchQuery): LengthAwarePaginator
    {

        return Product::where('branch_id', null)
            ->when($searchQuery, fn($query) => $query->where(function ($query) use ($searchQuery) {
                $query->where('name', 'like', '%' . $searchQuery . '%')
                    ->orWhere('description', 'like', '%' . $searchQuery . '%')
                    ->orWhere('description_two', 'like', '%' . $searchQuery . '%')
                    ->orWhere('itemCode', 'like', '%' . $searchQuery . '%')
                    ->orWhere('description_three', 'like', '%' . $searchQuery . '%');
            }))
            ->paginate(perPage: $perPage, page: $page)
            ->through(fn($product) => [
                'id' => $product->id,
                'name' => $product->name,
                'itemCode' => $product->itemCode,
                'price' => $product->price,
                'description' => $product->description,
                'description_two' => $product->description_two,
                'quantityUnit' => $product->productQuantityUnit->name,
                'image' => 'https://ui-avatars.com/api/?name=' . urlencode($product->name) . '&color=7F9CF5&background=EBF4FF',
            ]);
    }

    /**
     * @return ProductTaxData[]
     * @var PaymentItemData[] $items
     */

    public function calculateTax(array $items, int $branchId)
    {

        $productIds = array_unique(array_map(fn($item) => $item->productID, $items));

        $productsWithTaxInfo = Product::whereIn('id', $productIds)
            ->where('branch_id', $branchId)
            ->with([
                'productTax:id,code,rate',
                'productPackingUnit:id,code',
                'productQuantityUnit:id,code',
                'productClass:id,code',
                'productDetails:id,product_id,expireDate,batchNumber'
            ])
            ->get();

        $missingProducts = array_diff($productIds, $productsWithTaxInfo->pluck('id')->toArray());

        throw_if(!empty($missingProducts), NotFound::class, 'Invalid products passed in');

        $taxData = [];

        foreach ($items as $item) {
            $productDetails = $productsWithTaxInfo->where("id", $item->productID)->first();

            $itemAmount = $item->units * $item->unitPrice;
            $discountAmount = $itemAmount * ($item->discountRate / 100);
            $taxableAmount = $itemAmount - $discountAmount;

            $taxAmount =  $productDetails->productTax->code === 'B' ? round($taxableAmount * ($productDetails->productTax->rate / 118), 2) : 0;

            $taxData[] = ProductTaxData::from([
                "id" => $item->productID,
                "code" => $productDetails->itemCode,
                "name" => $productDetails->name,
                "batchNumber" => $item->batchNumber,
                "class" => $productDetails->productClass?->code,
                "units" => $item->units,
                "unitPrice" => $item->unitPrice,
                "packagingUnit" => $productDetails->productPackingUnit?->code,
                "quantityUnit" => $productDetails->productQuantityUnit->code,
                "taxableAmount" => $taxableAmount,
                "taxAmount" => $taxAmount,
                "taxName" => $productDetails->productTax->code,
                "taxRate" => $productDetails->productTax->rate,
                "discountRate" => $item->discountRate,
                "discountAmount" => $discountAmount,
                "movingUnit" => $item->movingUnit,
                "expireDate" => $productDetails->productDetails->where('batchNumber', $item->batchNumber)->first()?->expireDate
            ]);
        }

        return $taxData;
    }

    public function update(UpdateProductData $data, CompanyBranch $branch, int  $productId): void
    {

        $product = Product::where('id', $productId)
            ->where('branch_id', $branch->id)
            ->first();

        throw_if(!$product, NotFound::class, 'Product not found');

        $validateName = Product::where('name', $data->name)->where('branch_id', $branch->id)
            ->when($productId, fn($query) => $query->where('id', '!=', $productId))
            ->exists();
        throw_if($validateName, BadRequest::class, 'Product with the same name already exists');

        $product->update([
            'name' => $data->name,
            'branch_product_category_id' => $data->branchProductCategoryID,
        ]);
    }

    public function getStockHistory(
        int $branchId,
        int $perPage,
        int $page,
        ?string  $searchQuery,
        ?string $stockType,
        ?string $fromDate,
        ?string $toDate,
        ?int $productID,
        ?int $cashierID,
        ?int $partyID
    ): LengthAwarePaginator {

        return ProductStockHistory::where('branch_id', $branchId)
            ->with(['product:id,name', 'branch:id,name', 'user:id,name', 'party:id,name'])
            ->when($stockType, fn($query) => $query->where('stockType', $stockType))
            ->when($productID, fn($query) => $query->where('product_id', $productID))
            ->when($cashierID, fn($query) => $query->where('user_id', $cashierID))
            ->when($partyID, fn($query) => $query->where('party_id', $partyID))
            ->when($searchQuery, fn($query) =>
            $query->where('description', 'like', '%' . $searchQuery . '%')
                ->orWhere('productCode', 'like', '%' . $searchQuery . '%')
                ->orWhere('batchNumber', 'like', '%' . $searchQuery . '%')
                ->orWhereHas('product', fn($query) => $query->where('name', 'like', '%' . $searchQuery . '%')))
            ->FilterByDate($fromDate, $toDate)

            ->latest()
            ->paginate(perPage: $perPage, page: $page)
            ->through(fn($history) => [
                'id' => $history->id,
                'product' => $history->product->name,
                'user' => $history->user->name,
                'party' => $history?->party?->name,
                'branch' => $history->branch->name,
                'quantity' => $history->quantity,
                'stockType' => $history->stockType,
                'orderType' => $history->orderType,
                'created_at' => $history->date,
                'description' => $history->description,
                'productCode' => $history->productCode,
                'batchNumber' => $history->batchNumber,
                'expiryDate' => $history->expiryDate,
                'confirmationDate' => $history->confirmationDate
            ]);
    }

    public function syncEbm(CompanyBranch $branch): void
    {
        $products = $this->ebmService->syncProducts($branch);

        $productCount = is_countable($products) ? count($products) : 0;

        Log::info('Syncing products to database', ['count' => $productCount]);

        if ($productCount === 0) {
            return;
        }

        if (is_iterable($products)) {
            foreach ($products as $productData) {
                $product = Product::where('itemCode', $productData['itemCd'])
                    ->where('branch_id', $branch->id)
                    ->first();

                if (is_null($product)) {
                    try {
                        DB::beginTransaction();
                        $tax = Tax::where('code', $productData['taxTyCd'])->first();
                        $country = Country::where('code', $productData['orgnNatCd'])->first();
                        $type = ProductType::where('id', $productData['itemTyCd'])->first();
                        $packingUnit = ProductPackingUnit::where('code', $productData['pkgUnitCd'])->first();
                        $quantityUnit = ProductQuantityUnit::where('code', $productData['qtyUnitCd'])->first();
                        $class = ProductClass::where('code', $productData['itemClsCd'])->first();

                        if (is_null($class)) {
                            $class = ProductClass::where('code', '4320155400')->first();
                        }

                        Product::create([
                            'itemCode' => $productData['itemCd'],
                            'name' => $productData['itemNm'],
                            'country_id' => $country->id,
                            'type_id' => $type->id,
                            'packaging_unit_id' => $packingUnit->id,
                            'quantity_unit_id' => $quantityUnit->id,
                            'category_id' => ProductCategory::first()->id,
                            'tax_id' => $tax->id,
                            'class_id' => $class->id,
                            'slug' => strtolower(str_replace(' ', '-', $productData['itemNm'])),
                            'branch_id' => $branch->id,
                            'branch_product_category_id' => 1,
                        ]);

                        DB::commit();
                    } catch (\Exception $e) {
                        DB::rollBack();
                        Log::info("Error creating product: " . $e->getMessage());
                    }
                }
            }
        }
    }

    public function editBatch(CompanyBranch $branch, int $productId, int $batchId): array
    {

        $product = $this->getProductById($branch->id, $productId);

        $batch = ProductDetail::where('id', $batchId)->where('product_id', $productId)->first();
        throw_if(is_null($batch), NotFound::class, 'Batch not found');

        return [
            'batch' => $batch,
            'product' => $product
        ];
    }

    public function updateBatch(UpdateBatchData $data, CompanyBranch $branch, int $productId, int $batchId): void
    {

        $product = Product::where('id', $productId)->where('branch_id', $branch->id)->first();
        throw_if(is_null($product), NotFound::class, 'Product not found');

        $batch = ProductDetail::where('id', $batchId)->where('product_id', $productId)->first();
        throw_if(is_null($batch), NotFound::class, 'Batch not found');

        $batch->update([
            'salesPrice' => $data->salePrice,
            'purchasePrice' => $data->purchasePrice,
            'expireDate' => $data->expireDate,
            'discountRate' => $data->discountRate
        ]);

        Log::info('Batch updated', ['data' => $data, 'product' => $product, 'batch' => $batch]);

        return;
    }

    private function validateEbmProduct(NewProductData $data, int $branchId, $companyData, ?int $productId): void
    {

        $branch = collect($companyData['branches'])->where('id', $branchId)->first();

        throw_if(!$branch, NotFound::class, 'Branch not found');

        $product = Product::where('name', $data->name)->where('branch_id', $branchId)
            ->when($productId, fn($query) => $query->where('id', '!=', $productId))
            ->exists();
        throw_if($product, BadRequest::class, 'Product with the same name already exists');

        $quantityUnit = ProductQuantityUnit::where('id', $data->quantityUnitID)->exists();
        throw_if(!$quantityUnit, NotFound::class, 'Quantity unit not found');

        throw_if(is_null($data->packagingUnitID), BadRequest::class, 'Product Packaging Unit is required');
        $packingUnit = ProductPackingUnit::where('id', $data->packagingUnitID)->exists();
        throw_if(!$packingUnit, NotFound::class, 'Packing unit not found');

        throw_if(is_null($data->countryID), BadRequest::class, 'Product Country of origin  is required');
        $country = Country::where('id', $data->countryID)->exists();
        throw_if(!$country, NotFound::class, 'Country not found');

        $tax = Tax::where('id', $data->taxID)->exists();
        throw_if(!$tax, NotFound::class, 'Tax not found');

        if ($data->soldInSubUnit) {
            throw_if(is_null($data->conversionFactor), BadRequest::class, 'Conversion factor is required');

            throw_if($data->conversionFactor < 2, BadRequest::class, 'Conversion factor must be greater than 1');
        }

        throw_if(is_null($data->branchProductCategoryID), BadRequest::class, ' Product Category is required');
        $productCategory = BranchProductCategory::where('id', $data->branchProductCategoryID)
            ->whereCompanyId($branch->company_id)
            ->exists();

        throw_if(!$productCategory, NotFound::class, 'Product category not found');

        return;
    }

    private function validateProduct($data, int $branchId, $companyData, ?int $productId): void
    {

        $branch = collect($companyData['branches'])->where('id', $branchId)->first();

        throw_if(!$branch, NotFound::class, 'Branch not found');

        $product = Product::where('name', $data->name)->where('branch_id', $branchId)
            ->when($productId, fn($query) => $query->where('id', '!=', $productId))
            ->exists();

        throw_if($product, BadRequest::class, 'Product with the same name already exists');

        $quantityUnit = ProductQuantityUnit::where('id', $data->quantityUnitID)->exists();
        throw_if(!$quantityUnit, NotFound::class, 'Quantity unit not found');

        $tax = Tax::where('id', $data->taxID)->exists();
        throw_if(!$tax, NotFound::class, 'Tax not found');

        throw_if(is_null($data->branchProductCategoryID), BadRequest::class, ' Product Category is required');

        $branchProductCategoryValidation = BranchProductCategory::where('id', $data->branchProductCategoryID)
            ->whereCompanyId($branch->company_id)
            ->exists();

        throw_if(!$branchProductCategoryValidation, NotFound::class, 'Product category not found');

        if ($data->soldInSubUnit) {
            throw_if(is_null($data->conversionFactor), BadRequest::class, 'Conversion factor is required');

            throw_if($data->conversionFactor < 2, BadRequest::class, 'Conversion factor must be greater than 1');
        }

        return;
    }

    public function validateProductStockQuantity(array $items): void
    {
        foreach ($items as $item) {

            $productValidation = Product::where('id', $item->productID)->exists();

            throw_if(!$productValidation, NotFound::class, 'Product not found');

            $productDetailValidation = ProductDetail::where('product_id', $item->productID)
                ->where('batchNumber', $item->batchNumber)
                ->where('currentStock', '>=', $item->units)
                ->orWhere('status', ProductStockStatusEnums::NO_STOCK->value)
                ->exists();

            throw_if(!$productDetailValidation, BadRequest::class, 'Insufficient stock quantity or invalid batch number');
        }

        return;
    }

    private function saveProductToEbm(EbmNewProductData $data, CompanyBranch $branch): bool
    {
        if ($branch->cluster == 'demo') {
            Log::info('Bypassing EBM save for demo cluster', ['branchId' => $data->bhfId]);
            return true;
        }

        if (!$branch->isInitialised) {
            Log::info('Bypassing EBM save for uninitialised branch', ['branchId' => $data->bhfId]);
            return true;
        }

        $url = $this->ebmService->buildUrl($branch->cluster, $branch->clusterName, $this->endpoint);
        Log::info('Sending request to EBM', ['url' => $url, 'payload' => $data->toArray()]);

        $saveEbmProduct = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])->withOptions(['allow_redirects' => true])->post($url, $data->toArray());

        if (!$saveEbmProduct->successful()) {
            Log::error('EBM request failed', [
                'tin' => $data->tin,
                'branchId' => $data->bhfId,
                'url' => $url,
                'status' => $saveEbmProduct->status(),
                'headers' => $saveEbmProduct->headers(),
                'body' => $saveEbmProduct->body(),
            ]);
            throw new BadRequest('EBM service returned an error: ' . $saveEbmProduct->body());
        }

        $responseJson = $saveEbmProduct->json();
        if (!$responseJson || !isset($responseJson['resultCd'])) {
            Log::error('Invalid JSON response from EBM', [
                'tin' => $data->tin,
                'branchId' => $data->bhfId,
                'url' => $url,
                'body' => $saveEbmProduct->body(),
            ]);
            throw new BadRequest('Invalid response format from EBM service');
        }

        if ($responseJson['resultCd'] !== '000') {
            Log::error('Product not saved to EBM', [
                'tin' => $data->tin,
                'branchId' => $data->bhfId,
                'url' => $url,
                'response' => $responseJson,
            ]);
            throw new BadRequest($responseJson['resultMsg'] ?? 'Unknown error');
        }

        Log::info('Product saved to EBM', [
            'tin' => $data->tin,
            'branchId' => $data->bhfId,
            'url' => $url,
            'response' => $responseJson,
            'payload' => $data->toArray(),
        ]);
        return true;
    }
}
