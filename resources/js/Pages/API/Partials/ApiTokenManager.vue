<script setup>
import { ref } from 'vue';
import { useForm } from '@inertiajs/vue3';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';

const props = defineProps({
    tokens: Array,
    availablePermissions: Array,
    defaultPermissions: Array,
});

const createApiTokenForm = useForm({
    name: '',
    permissions: props.defaultPermissions,
});

const updateApiTokenForm = useForm({
    permissions: [],
});

const deleteApiTokenForm = useForm({});

const displayingToken = ref(false);
const managingPermissionsFor = ref(null);
const apiTokenBeingDeleted = ref(null);

const createApiToken = () => {
    createApiTokenForm.post(route('api-tokens.store'), {
        preserveScroll: true,
        onSuccess: () => {
            displayingToken.value = true;
            createApiTokenForm.reset();
        },
    });
};

const manageApiTokenPermissions = (token) => {
    updateApiTokenForm.permissions = token.abilities;
    managingPermissionsFor.value = token;
};

const updateApiToken = () => {
    updateApiTokenForm.put(route('api-tokens.update', managingPermissionsFor.value), {
        preserveScroll: true,
        preserveState: true,
        onSuccess: () => (managingPermissionsFor.value = null),
    });
};

const confirmApiTokenDeletion = (token) => {
    apiTokenBeingDeleted.value = token;
};

const deleteApiToken = () => {
    deleteApiTokenForm.delete(route('api-tokens.destroy', apiTokenBeingDeleted.value), {
        preserveScroll: true,
        preserveState: true,
        onSuccess: () => (apiTokenBeingDeleted.value = null),
    });
};
</script>

<template>
    <div class="space-y-8">
        <Card>
            <CardHeader>
                <CardTitle>Create API Token</CardTitle>
            </CardHeader>
            <CardContent>
                <form @submit.prevent="createApiToken" class="space-y-6">
                    <div>
                        <Label for="name">Name</Label>
                        <Input id="name" v-model="createApiTokenForm.name" type="text" autofocus />
                        <p v-if="createApiTokenForm.errors.name" class="text-red-500 text-sm mt-1">
                            {{ createApiTokenForm.errors.name }}
                        </p>
                    </div>

                    <div v-if="availablePermissions.length > 0" class="space-y-4">
                        <Label>Permissions</Label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div v-for="permission in availablePermissions" :key="permission" class="flex items-center">
                                <Checkbox
                                    :id="permission"
                                    v-model:checked="createApiTokenForm.permissions"
                                    :value="permission"
                                />
                                <Label :for="permission" class="ml-2 text-sm text-gray-600">{{ permission }}</Label>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center gap-4">
                        <Button
                            type="submit"
                            :disabled="createApiTokenForm.processing"
                            :class="{ 'opacity-25': createApiTokenForm.processing }"
                        >
                            Create
                        </Button>
                        <Alert v-if="createApiTokenForm.recentlySuccessful" class="flex-1">
                            <AlertDescription>Created.</AlertDescription>
                        </Alert>
                    </div>
                </form>
            </CardContent>
        </Card>

        <Card v-if="tokens.length > 0">
            <CardHeader>
                <CardTitle>Manage API Tokens</CardTitle>
                <p class="text-sm text-gray-600">You may delete any of your existing tokens if they are no longer needed.</p>
            </CardHeader>
            <CardContent>
                <div class="space-y-4">
                    <div v-for="token in tokens" :key="token.id" class="flex items-center justify-between border p-3 rounded bg-gray-50 hover:cursor-pointer">
                        <div class="break-all text-sm">{{ token.name }}</div>
                        <div class="flex items-center gap-4">
                            <span v-if="token.last_used_ago" class="text-sm text-gray-400">
                                Last used {{ token.last_used_ago }}
                            </span>
                            <Button
                                v-if="availablePermissions.length > 0"
                                variant="link"
                                class="text-sm text-gray-400"
                                @click="manageApiTokenPermissions(token)"
                            >
                                Permissions
                            </Button>
                            <Button
                                variant="link"
                                class="text-sm text-red-500"
                                @click="confirmApiTokenDeletion(token)"
                            >
                                Delete
                            </Button>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>

        <Dialog :open="displayingToken" @update:open="displayingToken = $event">
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>API Token</DialogTitle>
                    <DialogDescription>
                        Please copy your new API token. For your security, it won't be shown again.
                    </DialogDescription>
                </DialogHeader>
                <div v-if="$page.props.jetstream.flash.token" class="mt-4 bg-gray-100 px-4 py-2 rounded font-mono text-sm text-gray-500 break-all">
                    {{ $page.props.jetstream.flash.token }}
                </div>
            </DialogContent>
        </Dialog>

        <Dialog :open="managingPermissionsFor != null" @update:open="managingPermissionsFor = null">
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>API Token Permissions</DialogTitle>
                </DialogHeader>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div v-for="permission in availablePermissions" :key="permission" class="flex items-center">
                        <Checkbox
                            :id="'update-' + permission"
                            v-model:checked="updateApiTokenForm.permissions"
                            :value="permission"
                        />
                        <Label :for="'update-' + permission" class="ml-2 text-sm text-gray-600">{{ permission }}</Label>
                    </div>
                </div>
                <DialogFooter>
                    <Button
                        :disabled="updateApiTokenForm.processing"
                        :class="{ 'opacity-25': updateApiTokenForm.processing }"
                        @click="updateApiToken"
                    >
                        Save
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>

        <Dialog :open="apiTokenBeingDeleted != null" @update:open="apiTokenBeingDeleted = null">
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Delete API Token</DialogTitle>
                    <DialogDescription>
                        Are you sure you would like to delete this API token?
                    </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <Button
                        variant="destructive"
                        :disabled="deleteApiTokenForm.processing"
                        :class="{ 'opacity-25': deleteApiTokenForm.processing }"
                        @click="deleteApiToken"
                    >
                        Delete
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    </div>
</template>