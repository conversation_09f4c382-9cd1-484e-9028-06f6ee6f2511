<?php

namespace App\Services;

use App\Data\NewCompanyPartyData;
use App\Data\NewCustomerData;
use App\Exceptions\NotFound;
use App\Models\CompanyInsurance;
use App\Models\CompanyParty;
use App\Models\PartyType;
use App\Models\PatientDetails;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Exceptions\ServerError;

class CompanyPartyService
{


    public function partyTypes(int $companyId): array
    {
        return  PartyType::select('id', 'name', 'description')
            ->whereCompanyId($companyId)
            ->get()
            ->toArray();
    }

    public function parties(int $companyId, ?string $partytype): array
    {

        return CompanyParty::select('id', 'name', 'phone', 'email', 'tin', 'address', 'party_type_id',  'updated_at', 'company_id')
            ->when($partytype, function ($query, $partytype) {
                return $query->whereHas('partyType', function ($query) use ($partytype) {
                    $query->where('name', $partytype);
                });
            })
            ->where('company_id', $companyId)
            ->with('partyType:id,name')
            ->get()
            ->toArray();
    }

    public function companyParties(int $companyId, ?string $partytype = null): array
    {
        return CompanyParty::select('id', 'name', 'phone', 'email', 'tin', 'address', 'party_type_id', 'updated_at', 'company_id')
            ->when($partytype, function ($query, $partytype) {
                return $query->whereHas('partyType', function ($query) use ($partytype) {
                    $query->where('name', $partytype);
                });
            })
            ->where('company_id', $companyId)
            ->with('partyType:id,name')
            ->get()
            ->map(function ($party) {
                $party['image'] = 'https://ui-avatars.com/api/?name=' . urlencode($party['name']) . '&color=7F9CF5&background=EBF4FF';
                return $party;
            })
            ->toArray();
    }

    public function storeParty(NewCompanyPartyData $data, $companyId): void
    {

        $validatePartyType = PartyType::where('id', $data->partyTypeID)
            ->whereCompanyId($companyId)
            ->exists();

        throw_if(!$validatePartyType, NotFound::class, 'Invalid Party Type');

        $validateParty = CompanyParty::where('name', $data->name)
            ->whereCompanyId($companyId)
            ->where('party_type_id', $data->partyTypeID)
            ->exists();

        throw_if($validateParty, NotFound::class, 'Party already exists');

        $slug = str_replace(' ', '-', $data->name);

        CompanyParty::create([
            'name' => $data->name,
            'slug' =>  $slug,
            'phone' => $data->phone,
            'email' => $data->email,
            'address' => $data->address,
            'tin' => $data->tin,
            'party_type_id' => $data->partyTypeID,
            'company_id' => $companyId
        ]);
    }

    public function storeCustomer(NewCustomerData $data, $companyId): void
    {

        $validatePartyType = PartyType::where('name', 'Customer')
            ->whereCompanyId($companyId)
            ->first();

        throw_if(is_null($validatePartyType), NotFound::class, 'Invalid Party Type');

        $validateParty = CompanyParty::where('name', $data->name)
            ->whereCompanyId($companyId)
            ->where('party_type_id', $validatePartyType->id)
            ->exists();

        throw_if($validateParty, NotFound::class, 'Party already exists');

        $slug = str_replace(' ', '-', $data->name);

        try {
            DB::beginTransaction();
            $party = CompanyParty::create([
                'name' => $data->name,
                'slug' =>  $slug,
                'phone' => $data->phone,
                'email' => $data->email,
                'address' => $data->address,
                'tin' => $data->tin,
                'party_type_id' => $validatePartyType->id,
                'company_id' => $companyId
            ]);

            if ($data->hasInsurance) {
                $insurance = CompanyInsurance::where('id', $data->insuranceID)
                    ->whereCompanyId($companyId)
                    ->exists();

                throw_if(is_null($insurance), NotFound::class, 'Invalid Insurance');

                PatientDetails::create([
                    'party_id' => $party->id,
                    'insurance_id' => $data->insuranceID,
                    'code' => $data->code,
                    'percentage' => $data->percentage,
                    'expirationDate' => strtotime( $data->expirationDate),
                    'gender' => $data->gender,
                    'hasAffiliation' => $data->hasAffiliation,
                    'affiliationNumber' => $data->affiliationNumber,
                    'affiliateFirstName' => $data->affiliateFirstName,
                    'affiliateLastName' => $data->affiliateLastName,
                    'relationship' => $data->relationship,

                    'beneficiaryFirstName' => $data->beneficiaryFirstName,
                    'beneficiaryLastName' => $data->beneficiaryLastName,
                    'beneficiaryNumber' => $data->beneficiaryNumber,
                    'dateOfBirth' => strtotime($data->dateOfBirth),
                    'department' => $data->department,
                    'status' => $data->status,
                ]);
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw new ServerError('Failed to create party');
        }
    }

    public function updateCustomer(NewCustomerData $data, int $customerId)
    {

        $party = CompanyParty::where('id', $customerId)
            ->where('party_type_id', PartyType::where('name', 'Customer')->first()->id)
            ->first();

        throw_if(!$party, NotFound::class, 'Party not found');

        $validateParty = CompanyParty::where('name', $data->name)
            ->whereCompanyId($party->company_id)
            ->where('party_type_id', $party->party_type_id)
            ->where('id', '!=', $customerId)
            ->exists();

        throw_if($validateParty, NotFound::class, 'Party already exists');

        $patientDetails = PatientDetails::where('party_id', $customerId)->first();

        if ($data->hasInsurance) {
            $insurance = CompanyInsurance::where('id', $data->insuranceID)
                ->whereCompanyId($party->company_id)
                ->exists();

            throw_if(is_null($insurance), NotFound::class, 'Invalid Insurance');
        }
        try {
            DB::beginTransaction();
            $party->update([
                'name' => $data->name,
                'phone' => $data->phone,
                'email' => $data->email,
                'address' => $data->address,
                'tin' => $data->tin,
            ]);

            if (!is_null($patientDetails)) {
                $patientDetails->update([
                    'insurance_id' => $data->insuranceID,
                    'code' => $data->code,
                    'percentage' => $data->percentage,
                    'expirationDate' => $data->expirationDate,
                    'gender' => $data->gender,
                    'hasAffiliation' => $data->hasAffiliation,
                    'affiliationNumber' => $data->affiliationNumber,
                    'affiliateFirstName' => $data->affiliateFirstName,
                    'affiliateLastName' => $data->affiliateLastName,
                    'relationship' => $data->relationship,

                    'beneficiaryFirstName' => $data->beneficiaryFirstName,
                    'beneficiaryLastName' => $data->beneficiaryLastName,
                    'dateOfBirth' => $data->dateOfBirth,
                    'department' => $data->department,
                    'status' => $data->status,
                ]);
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ServerError('Failed to update company');
        }
    }

    public function partyById(int $id, int | null $companyId  ): array
    {
        $party =  CompanyParty::where('id', $id)
        ->when($companyId, function ($query, $companyId) {
            return $query->where('company_id', $companyId);
        })
            ->select('id', 'name', 'phone', 'email', 'address', 'tin', 'party_type_id', 'created_at', 'updated_at', 'company_id')
            ->with(['partyType:id,name', 'patientDetails'])
            ->first();

        throw_if(is_null($party), NotFound::class, 'Party not found');

        $patientDetails = null;
        if (!is_null($party->patientDetails)) {
            $patientDetails = [
                'insuranceID' => $party->patientDetails->insurance_id,
                'code' => $party->patientDetails->code,
                'percentage' => $party->patientDetails->percentage,
                'expirationDate' => $party->patientDetails->expirationDate,
                'gender' => $party->patientDetails->gender,
                'hasAffiliation' => $party->patientDetails->hasAffiliation,
                'affiliationNumber' => $party->patientDetails->affiliationNumber,
                'affiliateFirstName' => $party->patientDetails->affiliateFirstName,
                'affiliateLastName' => $party->patientDetails->affiliateLastName,
                'relationship' => $party->patientDetails->relationship,
                'beneficiaryFirstName' => $party->patientDetails->beneficiaryFirstName,
                'beneficiaryLastName' => $party->patientDetails->beneficiaryLastName,
                'beneficiaryNumber' => $party->patientDetails->beneficiaryNumber,
                'dateOfBirth' => $party->patientDetails->dateOfBirth,
                'department' => $party->patientDetails->department,
                'status' => $party->patientDetails->status,
                'insuranceName' => $party->patientDetails->insurance->name,
            ];
        }

        return [
            'id' => $party->id,
            'name' => $party->name,
            'phone' => $party->phone,
            'email' => $party->email,
            'address' => $party->address,
            'tin' => $party->tin,
            'party_type_id' => $party->party_type_id,
            'party_type' => $party->partyType->name,
            'updated_at' => $party->updated_at,
            'patientDetails' => $patientDetails,
            'hasInsurance' => $party->patientDetails ? true : false,
            'image' => 'https://ui-avatars.com/api/?name=' . urlencode($party->name) . '&color=7F9CF5&background=EBF4FF'
        ];
    }

    public function  updateParty(NewCompanyPartyData $data, int $partyId, int $companyId): void
    {

        $party = CompanyParty::where('id', $partyId)
            ->where('company_id', $companyId)
            ->first();

        throw_if(!$party, NotFound::class, 'Party not found');

        $validatePartyType = PartyType::where('id', $data->partyTypeID)
            ->whereCompanyId($companyId)
            ->exists();

        throw_if(!$validatePartyType, NotFound::class, 'Invalid Party Type');

        $validateParty = CompanyParty::where('name', $data->name)
            ->whereCompanyId($companyId)
            ->where('party_type_id', $data->partyTypeID)
            ->where('id', '!=', $partyId)
            ->exists();

        throw_if($validateParty, NotFound::class, 'Party already exists');

        $slug = str_replace(' ', '-', $data->name);

        $party->update([
            'name' => $data->name,
            'slug' =>  $slug,
            'phone' => $data->phone,
            'email' => $data->email,
            'address' => $data->address,
            'tin' => $data->tin,
            'party_type_id' => $data->partyTypeID,
            'company_id' => $companyId
        ]);
    }
}
