<?php

use App\Http\Controllers\AccountSubscriptionController;
use App\Http\Controllers\AffiliateController;
use App\Http\Controllers\CompanyBranchController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\CompanyPartyController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\TransactionAccountController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\DpoController;
use App\Http\Controllers\GoogleAuthLogic;
use App\Http\Controllers\ImportationController;
use App\Http\Controllers\InsuranceController;
use App\Http\Controllers\LogTrackController;
use App\Http\Controllers\PurchaseController;
use App\Http\Controllers\TaxComplianceController;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;



Route::get('', [HomeController::class, 'index'])->name('welcome');
Route::post('sign-up', [UserController::class, 'signUp'])->name('sign-up');

Route::post('zata-callback', [AccountSubscriptionController::class, 'zataCallback'])->name('zata-callback');

Route::controller(GoogleAuthLogic::class)->group(function () {
    Route::get('auth/google', 'redirect')->name('google-auth');
    Route::get('auth/google/call-back', 'callbackgoogle')->name('google-auth-call');
});

Route::fallback(function (Request $request) {
    if ($request->is('api/*') || str_starts_with($request->path(), 'api/') || $request->expectsJson()) {
        return response()->json([
            'message' => 'Oops! Page not found.',
            'timestamp' => now()->toDateTimeString(),
            'path' => $request->path()
        ], 404);
    }

    return redirect()->route('welcome');
});


Route::prefix('tax-compliance')->group(function () {
    Route::get('', [TaxComplianceController::class, 'index'])->name('tax-compliance.index');
    Route::post('calculate', [TaxComplianceController::class, 'calculate'])->name('tax-compliance.calculate');
});

Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified', Inertia\EncryptHistoryMiddleware::class])->group(function () {

    Route::prefix('affiliate')->group(function () {
        Route::get('', [AffiliateController::class, 'index'])->name('affiliate.index');
    });

    Route::get('dashboard', [HomeController::class, 'dashbaord'])->name('dashboard');
    Route::get('dashboard-json', [HomeController::class, 'dashboardJson'])->name('dashboard-json');

    Route::prefix('company')->group(function () {

        Route::get('', [CompanyController::class, 'index'])->name('company.index');
        Route::get('list', [CompanyController::class, 'list'])->name('company.list');
        Route::get('switch/{companyId}', [CompanyController::class, 'switch'])->name('company.switch')->whereNumber('companyId');

        Route::get('new', [CompanyController::class, 'new'])->name('company.new');
        Route::post('', [CompanyController::class, 'store'])->name('company.store');
        Route::get('edit/{companyId}', [CompanyController::class, 'edit'])->name('company.edit')->whereNumber('companyId');
        Route::put('{companyId}', [CompanyController::class, 'update'])->name('company.update')->whereNumber('companyId');

        Route::prefix('branch')->group(function () {

            Route::get('', [CompanyBranchController::class, 'new'])->name('branch.new');
            Route::post('store', [CompanyBranchController::class, 'store'])->name('branch.store');
            Route::get('edit/{id}', [CompanyBranchController::class, 'edit'])->name('branch.edit')->whereNumber('id');
            Route::put('{id}', [CompanyBranchController::class, 'update'])->name('branch.update')->whereNumber('id');
            Route::get('default/{branchId}', [CompanyBranchController::class, 'default'])->name('branch.setDefault')->whereNumber('branchId');
        });
    });

    Route::prefix('subscription')->group(function () {
        Route::get('', [AccountSubscriptionController::class, 'index'])->name('subscription.index');
        Route::get('update', [AccountSubscriptionController::class, 'update'])->name('subscription.update');
        Route::put('update', [AccountSubscriptionController::class, 'pay'])->name('subscription.pay');

        Route::get('payment/{paymentId}', [AccountSubscriptionController::class, 'validatePayment'])->name('subscription.payment')->whereNumber('paymentId');
    });

    Route::prefix('party')->group(function () {
        Route::get('customer', [CompanyPartyController::class, 'customer'])->name('customer.index');
        Route::post('customer', [CompanyPartyController::class, 'storeCustomer'])->name('customer.store');
        Route::get('customer/new', [CompanyPartyController::class, 'newCustomer'])->name('customer.new');
        Route::get('customer/edit/{partyId}', [CompanyPartyController::class, 'editCustomer'])->name('customer.edit')->whereNumber('partyId');
        Route::put('customer/{partyId}', [CompanyPartyController::class, 'updateCustomer'])->name('customer.update')->whereNumber('partyId');

        Route::get('supplier', [CompanyPartyController::class, 'supplier'])->name('supplier.index');
        Route::post('supplier', [CompanyPartyController::class, 'storeSupplier'])->name('supplier.store');
        Route::get('supplier/new', [CompanyPartyController::class, 'newSupplier'])->name('supplier.new');

        Route::get('{partyId}', [CompanyPartyController::class, 'show'])->name('party.show')->whereNumber('partyId');
        Route::get('edit/{partyId}', [CompanyPartyController::class, 'edit'])->name('party.edit')->whereNumber('partyId');
        Route::put('{partyId}', [CompanyPartyController::class, 'update'])->name('party.update')->whereNumber('partyId');
    });

    Route::prefix('insurance')->group(function () {
        Route::get('', [InsuranceController::class, 'index'])->name('insurance.index');
    });

    Route::prefix('expense')->group(function () {
        Route::get('', [ExpenseController::class, 'index'])->name('expense.index');
        Route::post('', [ExpenseController::class, 'store'])->name('expense.store');
        Route::put('{expenseId}', [ExpenseController::class, 'update'])->name('expense.update')->whereNumber('expenseId');
        Route::get('new', [ExpenseController::class, 'new'])->name('expense.new');
        Route::get('{expenseId}', [ExpenseController::class, 'show'])->name('expense.show')->whereNumber('expenseId');
        Route::get('edit/{expenseId}', [ExpenseController::class, 'edit'])->name('expense.edit')->whereNumber('expenseId');
    });

    Route::prefix('product')->group(function () {
        Route::get('', [ProductController::class, 'index'])->name('product.index');
        Route::get('new', [ProductController::class, 'new'])->name('product.new');
        Route::get('sync', [ProductController::class, 'sync'])->name('product.sync');
        Route::post('confirm-sync', [ProductController::class, 'confirmSync'])->name('product.confirmSync');
        Route::post('', [ProductController::class, 'store'])->name('product.store');
        Route::get('edit/{productId}', [ProductController::class, 'edit'])->name('product.edit')->whereNumber('productId');
        Route::put('{productId}', [ProductController::class, 'update'])->name('product.update')->whereNumber('productId');
        Route::get('{productId}', [ProductController::class, 'show'])->name('product.show')->whereNumber('productId');

        Route::get('stock-in/{productId}', [ProductController::class, 'stockIn'])->name('product.stockIn')->whereNumber('productId');
        Route::put('stock-in/{productId}', [ProductController::class, 'stockInStore'])->name('product.stockInStore')->whereNumber('productId');
        Route::get('stock-out/{productId}', [ProductController::class, 'stockOut'])->name('product.stockOut')->whereNumber('productId');
        Route::put('stock-out/{productId}', [ProductController::class, 'stockOutStore'])->name('product.stockOutStore')->whereNumber('productId');

        Route::get('stock-history', [ProductController::class, 'stockHistory'])->name('product.stockHistory');
        Route::get('stock-history/download', [ProductController::class, 'stockHistoryDownload'])->name('product.stockHistoryDownload');

        Route::get('stock-transfer/{productId}', [ProductController::class, 'stockTransfer'])->name('product.stockTransfer')->whereNumber('productId');
        Route::put('stock-transfer/{productId}', [ProductController::class, 'stockTransferStore'])->name('product.stockTransferStore')->whereNumber('productId');

        Route::get('sync-ebm', [ProductController::class, 'syncEbm'])->name('product.syncEbm');
        Route::get('batch/{productId}/{batchId}', [ProductController::class, 'editBatch'])->name('product.editBatch')->whereNumber('productId')->whereNumber('batchId');
        Route::post('batch/{productId}/{batchId}', [ProductController::class, 'updateBatch'])->name('product.updateBatch')->whereNumber('productId')->whereNumber('batchId');
    });

    Route::prefix('transaction')->group(function () {

        Route::get('sale', [TransactionController::class, 'sale'])->name('transaction.sale');

        Route::prefix('proforma')->group(function () {
            Route::get('', [TransactionController::class, 'proforma'])->name('transaction.proforma');
            Route::get('create', [TransactionController::class, 'createProforma'])->name('transaction.proforma.create');
            Route::post('store', [TransactionController::class, 'storeProforma'])->name('transaction.proforma.store');
            Route::get('{proformaId}', [TransactionController::class, 'convertProformaToSale'])->name('transaction.proforma.convert')->whereNumber('proformaId');
            Route::post('{proformaId}', [TransactionController::class, 'convertProformaToSaleStore'])->name('transaction.proforma.convert.store')->whereNumber('proformaId');
        });

        Route::prefix('refund')->group(function () {
            Route::get('', [TransactionController::class, 'refund'])->name('transaction.refund');
            Route::get('create', [TransactionController::class, 'createRefund'])->name('transaction.refund.create');
            Route::post('store', [TransactionController::class, 'storeManualRefund'])->name('transaction.refund.manual.store');
            Route::get('{transactionId}', [TransactionController::class, 'refundSale'])->name('transaction.refundSale')->whereNumber('transactionId');
            Route::post('{transactionId}', [TransactionController::class, 'storeRefund'])->name('transaction.refund.store')->whereNumber('transactionId');
        });

        Route::prefix('pos')->group(function () {
            Route::get('', [TransactionController::class, 'pos'])->name('pos');
            Route::get('json/{clearCart?}', [TransactionController::class, 'posJson'])->name('pos-json');
            Route::post('store', [TransactionController::class, 'storePos'])->name('pos.store');
            Route::post('calculate', [TransactionController::class, 'calculatePos'])->name('pos.calculate');
        });

        Route::prefix('purchase')->group(function () {
            Route::get('', [TransactionController::class, 'purchase'])->name('transaction.purchase');
            Route::get('create', [TransactionController::class, 'createPurchase'])->name('transaction.purchase.create');
            Route::post('', [TransactionController::class, 'storePurchase'])->name('transaction.purchase.store');
            Route::get('{purchaseId}', [TransactionController::class, 'purchaseById'])->name('transaction.purchase.show')->whereNumber('purchaseId');
            Route::get('download/{purchaseId}', [TransactionController::class, 'downloadPurchase'])->name('transaction.purchase.download')->whereNumber('purchaseId');
        });

        Route::get('account', [TransactionAccountController::class, 'account'])->name('transaction.account');
        Route::get('payment', [TransactionAccountController::class, 'payment'])->name('transaction.payment');

        Route::get('{transactionId}/small', [TransactionController::class, 'showSmall'])->name('transaction.show.small')->whereNumber('transactionId');
        Route::get('{transactionId}', [TransactionController::class, 'show'])->name('transaction.show')->whereNumber('transactionId');
        Route::get('download/{transactionId}', [TransactionController::class, 'download'])->name('transaction.download')->whereNumber('transactionId');
        Route::get('download/{transactionId}/small', [TransactionController::class, 'downloadSmall'])->name('transaction.download.small')->whereNumber('transactionId');
        Route::get('download/{transactionId}/small58', [TransactionController::class, 'downloadSmall58'])->name('transaction.download.small58')->whereNumber('transactionId');
        Route::get('delivery-note/{transactionId}', [TransactionController::class, 'deliveryNote'])->name('transaction.download.deliveryNote')->whereNumber('transactionId');
    });

    Route::prefix('import')->group(function () {

        Route::get('', [ImportationController::class, 'index'])->name('import.index');
        Route::get('sync-ebm',  [ImportationController::class, 'checkImportation'])->name('import.sync');
        Route::get('{importId}',  [ImportationController::class, 'showImport'])->name('import.show')->whereNumber('importId');
        Route::put('{importId}', [ImportationController::class, 'update'])->name('import.update')->whereNumber('importId');
    });

    Route::prefix('purchase')->group(function () {
        Route::get('', [PurchaseController::class, 'index'])->name('purchase.index');
        Route::get('sync-ebm', [PurchaseController::class, 'checkPurchase'])->name('purchase.sync');
        Route::get('{purchaseId}', [PurchaseController::class, 'showPurchaseById'])->name('purchase.show')->whereNumber('purchaseId');
        Route::put('{purchaseId}', [PurchaseController::class, 'update'])->name('purchase.update')->whereNumber('purchaseId');
    });

    Route::prefix('user')->group(function () {
        Route::get('', [UserController::class, 'companyUser'])->name('user.company');
        Route::get('create-invitiation', [UserController::class, 'createInvitation'])->name('user.createInvitation');
        Route::post('create-invitiation', [UserController::class, 'storeInvitation'])->name('user.storeInvitation');
        Route::get('accept-invitation/{invitation}', [UserController::class, 'acceptInvitation'])->name('user.acceptInvitation');
        Route::get('confirm-invitation/{invitation}', [UserController::class, 'confirmInvitation'])->name('user.confirmInvitation');
        Route::get('update/{userId}',  [UserController::class, 'updateUser'])->name('user.update');
        Route::put('{userId}',  [UserController::class, 'updateSave'])->name('user.update.save')->whereNumber('userId');
    });

    Route::prefix('admin')->group(function () {
        Route::get('', [AdminController::class, 'index'])->name('admin.index');
        Route::get('search', [AdminController::class,'search'])->name('admin.search');
        Route::get('search-json', [AdminController::class,'searchJson'])->name('admin.search-json');
        Route::get('company/{companyId}', [AdminController::class, 'company'])->name('admin.company')->whereNumber('companyId');
        Route::get('company/{companyId}/edit', [AdminController::class, 'editCompany'])->name('admin.company.edit')->whereNumber('companyId');
        Route::put('company/{companyId}', [AdminController::class, 'updateCompany'])->name('admin.company.update')->whereNumber('companyId');

        Route::get('company/{companyId}/branch/{branchId}', [AdminController::class, 'editBranch'])->name('admin.branch.edit')->whereNumber('companyId', 'branchId');
        Route::put('company/{companyId}/branch/{branchId}', [AdminController::class, 'updateBranch'])->name('admin.branch.update')->whereNumber('companyId', 'branchId');

        Route::get('branch/initialize/{branchId}', [AdminController::class, 'initializeBranch'])->name('admin.branch.initialize')->whereNumber('branchId');
        Route::get('branch/initialize/auto/{branchId}', [AdminController::class, 'autoInitializeBranch'])->name('admin.branch.initialize.auto')->whereNumber('branchId');

        Route::prefix('account')->group(function () {
            Route::get('', [AdminController::class, 'accountIndex'])->name('account.index');
            Route::get('{accountId}', [AdminController::class, 'accountShow'])->name('account.show')->whereNumber('accountId');
            Route::put('{accountId}', [AdminController::class, 'accountUpdate'])->name('account.update')->whereNumber('accountId');
        });
    });

    Route::get('log-track', [LogTrackController::class, 'index'])->name('logTrack');
});


Route::prefix('payments')->group(function () {
    Route::post('/create', [DpoController::class, 'createPayment']);
    Route::post('/verify', [DpoController::class, 'verifyPayment']);
    Route::post('/mobile', [DpoController::class, 'processMobilePayment']);
    Route::get('/mobile-options', [DpoController::class, 'getMobileOptions']);
    Route::post('/refund', [DpoController::class, 'processRefund']);
    Route::get('/balance', [DpoController::class, 'getBalance']);
});

// TODO , adjust moving unit in entire system sub and main Wsdljd@123nmKLJPlopTNYRVoda