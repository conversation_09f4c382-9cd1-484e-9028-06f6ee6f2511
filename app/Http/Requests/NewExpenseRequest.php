<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class NewExpenseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount' => 'required|numeric|min:1',
            'description' => 'required|string|max:255',
            'categoryID' => 'required|integer|min:1',
            'paymentModeID' => 'required|integer|min:1',
            'date' => 'required|date|date_format:Y-m-d',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */

    public function messages(): array
    {
        return [
            'amount.required' => 'Amount is required',
            'amount.integer' => 'Amount must be an integer',
            'amount.min' => 'Amount must be at least 1',
            'description.required' => 'Description is required',
            'description.string' => 'Description must be a string',
            'description.max' => 'Description must not exceed 255 characters',
            'categoryID.required' => 'Category is required',
            'categoryID.integer' => 'Category must be an integer',
            'categoryID.min' => 'Category must be at least 1',
            'paymentModeID.required' => 'Payment mode is required',
            'paymentModeID.integer' => 'Payment mode must be an integer',
            'paymentModeID.min' => 'Payment mode must be at least 1',
            'date.required' => 'Date is required',
            'date.date' => 'Date must be a date',
            'date.date_format' => 'Date must be in the format Y-m-d',
        ];
    }
}
