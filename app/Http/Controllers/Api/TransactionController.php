<?php

namespace App\Http\Controllers\Api;

use App\Data\CalculateOrderData;
use App\Data\TransactionPosData;
use App\Data\TransactionRefundData;
use App\Enums\InvoiceSizeEnums;
use App\Http\Requests\CalculateOrderRequest;
use App\Services\ProductService;
use App\Services\TransactionService;
use OpenApi\Attributes as OA;
use App\Http\Controllers\Controller;
use App\Http\Requests\TransactionPosRequest;
use App\Http\Requests\TransactionRefundRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\URL;
use App\Services\PdfService;
use Illuminate\Support\Facades\Log;

class TransactionController extends Controller
{

    protected $user;

    public function __construct(
        protected TransactionService $service,
        protected ProductService $productService,
        protected PdfService $pdfService,
    ) {
        $this->user = Auth::user();
    }

    #[OA\Get(
        path: "/api/v1/transaction",
        description: "Get all transactions",
        security: [["authentication" => []]],
        summary: "Get all transactions",
        tags: ["Transaction"],
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        required: true,
        example: 1
    )]
    #[OA\QueryParameter(
        name: "perPage",
        description: "Number of items per page",
        required: false,
        example: 100
    )]
    #[OA\QueryParameter(
        name: "page",
        description: "Page number",
        required: false,
        example: 1
    )]
    #[OA\QueryParameter(
        name: "fromDate",
        description: "From date",
        required: false,
        example: "2023-01-01"
    )]
    #[OA\QueryParameter(
        name: "toDate",
        description: "To date",
        required: false,
        example: "2023-01-01"
    )]
    #[OA\QueryParameter(
        name: "invoiceType",
        description: "Invoice type",
        required: false,
        example: "sale"
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                'data' => [
                    [
                        'id' => 1,
                        'invoiceType' => 'sale',
                        'invoiceNumber' => 'INV001',
                        'clientName' => 'John Doe',
                        'salesDate' => '2023-01-01',
                        'totalAmount' => 1000,
                        'synced' => true,
                        'isRefunded' => false,
                        'status' => 'approved',
                        'itemsCount' => 2,
                    ]
                ],
                "currentPage" => 1,
                "lastpage" => 1,
                "itemsPerPage" => 10,
                "pageItems" => 1,
                "total" => 1,
            ]
        )
    )]

    public function index()
    {
        $defaultBranch = branchData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $fromDate = \request()->query('fromDate', null);
        $toDate = \request()->query('toDate', null);
        $invoiceType = \request()->query('invoiceType', null);

        $transaction = $this->pagination($this->service->getTransaction(
            branch: $defaultBranch,
            invoiceType: $invoiceType,
            perPage: $perPage,
            page: $page,
            userID: null,
            customerID: null,
            fromDate: $fromDate,
            toDate: $toDate
        ));
        return response()->json($transaction);
    }

    #[OA\Post(
        path: "/api/v1/transaction/sale",
        description: "Create a new sale transaction",
        security: [["authentication" => []]],
        summary: "Sale transaction creation",
        tags: ["Transaction"],
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        required: true,
        example: 1
    )]
    #[OA\RequestBody(
        description: "Data to create a new sale transaction",
        required: true,
        content: new OA\MediaType(
            mediaType: "application/json",
            schema: new OA\Schema(
                required: ["paymentModeId", "transactionDate", "products"],
                properties: [
                    new OA\Property(property: "purchaseCode", type: "string", maxLength: 200, example: "43034"),
                    new OA\Property(property: "paymentMethodID", type: "integer", minimum: 1),
                    new OA\Property(property: "customerID", type: "integer", minimum: 1, example: 1),
                    new OA\Property(property: "transactionDate", type: "string", format: "date", example: "2024-01-01"),
                    new OA\Property(property: "note", type: "string", maxLength: 200),

                    new OA\Property(property: "customerTIN", type: "string", minimum: 9, maximum: 9, example: "*********"),
                    new OA\Property(property: "customerName", type: "string", maxLength: 200, example: "John Doe"),
                    new OA\Property(property: "customerPhone", type: "string", maxLength: 200, example: "*********"),
                    new OA\Property(
                        property: "items",
                        type: "array",
                        items: new OA\Items(
                            required: ["prductID", "units", "unitPrice", "discountRate"],
                            properties: [
                                new OA\Property(property: "productID", type: "integer", minimum: 1),
                                new OA\Property(property: "units", type: "number", minimum: 1, example: 12.5),
                                new OA\Property(property: "unitPrice", type: "number", minimum: 1, example: 1000),
                                new OA\Property(property: "discountRate", type: "number", minimum: 0, maximum: 100),
                                new OA\Property(property: "batchNumber", type: "string", maxLength: 100),

                            ],
                            type: "object"
                        )
                    )
                ],
                type: "object"
            )
        )
    )]
    #[OA\Response(
        response: 200,
        description: "Response on success",
        content: new OA\JsonContent(
            example: [
                "message" => "Sale transaction created successfully",
                "invoiceID" => 1
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: "Business not found",
        content: new OA\JsonContent(
            example: ["message" => "Business not found"]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Unexpected sale transaction result code",
        content: new OA\JsonContent(
            example: ["message" => "Something went wrong"]
        )
    )]
    public function createSale(TransactionPosRequest $request): JsonResponse
    {

        $company = companyData();
        $defaultBranch = branchData();

        $data = TransactionPosData::from($request->validated());

        $invoice =  $this->service->storeSale($data, $defaultBranch, $company, $this->user);
        return response()->json(['message' => 'Sale transaction created successfully', 'invoiceID' => $invoice]);
    }
    #[OA\Post(
        path: "/api/v1/transaction/refund/{transactionId}",
        description: "Create a new refund transaction",
        security: [["authentication" => []]],
        summary: "Refund transaction creation",
        tags: ["Transaction"],
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        required: true,
        example: 1
    )]
    #[OA\PathParameter(
        name: "transactionId",
        required: true,
        example: 1
    )]
    #[OA\RequestBody(
        description: "Data to create a new refund transaction",
        required: true,
        content: new OA\MediaType(
            mediaType: "application/json",
            schema: new OA\Schema(
                required: ["transactionDate"],
                properties: [
                    new OA\Property(property: "transactionDate", type: "string", example: "8943786743"),
                    new OA\Property(property: "purchaseCode", type: "date", example: "2024-01-01"),
                    new OA\Property(property: "note", type: "string", maxLength: 200),
                ]
            )
        )
    )]
    #[OA\Response(
        response: 200,
        description: "Response on success",
        content: new OA\JsonContent(
            example: [
                "message" => "Refund transaction created successfully",
                "invoiceID" => 1
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: "Business not found",
        content: new OA\JsonContent(
            example: ["message" => "Business not found"]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Unexpected refund transaction result code",
        content: new OA\JsonContent(
            example: ["message" => "Something went wrong"]
        )
    )]
    public function createRefund(TransactionRefundRequest $request, int $id): JsonResponse
    {

        $company = companyData();
        $defaultBranch = branchData();
        $user = $this->user;
        $data = TransactionRefundData::from($request->validated());

        $invoice = $this->service->storeRefund($id, $data, $defaultBranch, $company, $user);

        return response()->json(['message' => 'Refund transaction created successfully', 'invoiceID' => $invoice]);
    }
    #[OA\Post(
        path: "/api/v1/transaction/proforma",
        description: "Create a new proforms transaction",
        security: [["authentication" => []]],
        summary: "Proforma transaction creation",
        tags: ["Transaction"],
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        required: true,
        example: 1
    )]
    #[OA\RequestBody(
        description: "Data to create a proforma transaction",
        required: true,
        content: new OA\MediaType(
            mediaType: "application/json",
            schema: new OA\Schema(
                required: ["paymentModeId", "transactionDate", "products"],
                properties: [

                    new OA\Property(property: "clientTin", type: "string", minimum: 9, maximum: 9, example: "*********"),
                    new OA\Property(property: "purchaseCode", type: "string", maxLength: 200, example: "43034"),
                    new OA\Property(property: "paymentMethodID", type: "integer", minimum: 1),
                    new OA\Property(property: "transactionDate", type: "string", format: "date", example: "2024-01-01"),
                    new OA\Property(property: "note", type: "string", maxLength: 200),
                    new OA\Property(
                        property: "items",
                        type: "array",
                        items: new OA\Items(
                            required: ["prductID", "units", "unitPrice", "discountRate"],
                            properties: [
                                new OA\Property(property: "productID", type: "integer", minimum: 1),
                                new OA\Property(property: "units", type: "number", minimum: 1, example: 12.5),
                                new OA\Property(property: "unitPrice", type: "number", minimum: 1, example: 1000),
                                new OA\Property(property: "discountRate", type: "number", minimum: 0, maximum: 100),
                                new OA\Property(property: "batchNumber", type: "string", maxLength: 100),
                            ],
                            type: "object"
                        )
                    )
                ],
                type: "object"
            )
        )
    )]
    #[OA\Response(
        response: 200,
        description: "Response on success",
        content: new OA\JsonContent(
            example: [
                "message" => "Proforma transaction created successfully",
                "invoiceID" => 1
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: "Business not found",
        content: new OA\JsonContent(
            example: ["message" => "Business not found"]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Unexpected sale transaction result code",
        content: new OA\JsonContent(
            example: ["message" => "Something went wrong"]
        )
    )]
    public function createProforma(TransactionPosRequest $request): JsonResponse
    {

        $company = companyData();
        $defaultBranch = branchData();
        $user = $this->user;
        $data = TransactionPosData::from($request->validated());
        $invoice =  $this->service->storeProforma($data, $defaultBranch, $company, $user);
        return response()->json(['message' => 'Proforma transaction created successfully', 'invoiceID' => $invoice]);
    }

    #[OA\Post(
        path: "/api/v1/transaction/calculate",
        description: "calculate a new  transaction",
        security: [["authentication" => []]],
        summary: "calculate transaction creation",
        tags: ["Transaction"],
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        required: true,
        example: 1
    )]
    #[OA\RequestBody(
        description: "Data to calculate a new transaction",
        required: true,
        content: new OA\MediaType(
            mediaType: "application/json",
            schema: new OA\Schema(
                required: ["items"],
                properties: [
                    new OA\Property(
                        property: "items",
                        type: "array",
                        items: new OA\Items(
                            required: ["prductID", "units", "unitPrice", "discountRate"],
                            properties: [
                                new OA\Property(property: "productID", type: "integer", minimum: 1),
                                new OA\Property(property: "units", type: "number", minimum: 1, example: 12.5),
                                new OA\Property(property: "unitPrice", type: "number", minimum: 1, example: 1000),
                                new OA\Property(property: "discountRate", type: "number", minimum: 0, maximum: 100),
                                new OA\Property(property: "batchNumber", type: "string", maxLength: 100),
                            ],
                            type: "object"
                        )
                    )
                ],
                type: "object"
            )
        )
    )]
    #[OA\Response(
        response: 200,
        description: "Response on success",
        content: new OA\JsonContent(
            example: [
                [
                    'id' => 1212,
                    'code' => 'RWAX20200001',
                    'name' => 'Product Name',
                    'units' => 90,
                    'unitPrice' => 1000,
                    'discountAmount' => 100,
                    'discountRate' => 10,
                    'taxAmount' => 180,
                    'taxRate' => 18,
                    'taxableAmount' => 900,
                    'taxName' => 'B',
                    'batchNumber' => 'aj9kk89'
                ]
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: "Business not found",
        content: new OA\JsonContent(
            example: ["message" => "Business not found"]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Unexpected sale transaction result code",
        content: new OA\JsonContent(
            example: ["message" => "Something went wrong"]
        )
    )]
    public function calculate(CalculateOrderRequest $request): JsonResponse
    {

        $defaultBranch = branchData();
        $data = CalculateOrderData::from($request->validated());
        $response = $this->productService->calculateTax($data->items, $defaultBranch->id);

        return response()->json($response);
    }

    #[OA\Get(
        path: "/api/v1/transaction/{transactionId}",
        description: "get a transaction by id",
        security: [["authentication" => []]],
        summary: "get transaction by id",
        tags: ["Transaction"],
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        required: true,
        example: 1
    )]
    #[OA\PathParameter(
        name: "transactionId",
        required: true,
        example: 1
    )]
    #[OA\Response(
        response: 200,
        description: "Response on success",
        content: new OA\JsonContent(
            example: [
                'id' => 1,
                'invoiceNumber' => 1,
                'originalInvoiceNumber' => 1,
                'clientTin' => '',
                'clientName' => '',
                'clientPhoneNumber' => '',
                'salesTypeCode' => '',
                'receiptTypeCode' => '',
                'paymentTypeCode' => '',
                'salesStatusCode' => '',
                'confirmationDate' => '',
                'type' => 'NS',
                'isRefunded' => true,
                'taxblAmtA' => 300.00,
                'taxblAmtB' => 300.00,
                'taxblAmtC' => 300.00,
                'taxblAmtD' => 300.00,
                'taxAmtA' => 300.00,
                'taxAmtB' => 300.00,
                'taxAmtC' => 300.00,
                'taxAmtD' => 300.00,
                'totTaxblAmt' => 300.00,
                'totTaxAmt' => 300.00,
                'totAmt' => 300.00,
                'salesDate' => '2022-01-01 00:00:00',
                'note' => '',
                'items' => [
                    [
                        'productName' => 'Product Name',
                        'units' => 10,
                        'unitPrice' => 1000,
                        'taxAmount' => 180,
                        'taxRate' => 18,
                        'taxName' => 'B',
                        'totalAmount'   => 900,
                        'totalDiscount' => 100,
                        'discountRate' =>  100
                    ]
                ]
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: "Business not found",
        content: new OA\JsonContent(
            example: ["message" => "Business not found"]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "something went wrong",
        content: new OA\JsonContent(
            example: ["message" => "Something went wrong"]
        )
    )]
    public function getTransactionById(int $transactionId): JsonResponse
    {

        $defaultBranch = branchData();
        $transaction = $this->service->transactionById($transactionId, $defaultBranch->id);
        return response()->json($transaction);
    }

    #[OA\Get(
        path: "/api/v1/transaction/download-signature/{transactionId}",
        description: "get transaction download signature",
        security: [["authentication" => []]],
        summary: "transaction download signature",
        tags: ["Transaction"],
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        required: true,
        example: 1
    )]
    #[OA\PathParameter(
        name: "transactionId",
        required: true,
        example: 1
    )]
    #[OA\QueryParameter(
        name: "downloadSize",
        description: "default pdf download size, option: A4 for normal paper, m80 for 80mm and m58 for 58mm",
        required: false,
        example: 'A4'
    )]
    #[OA\Response(
        response: 200,
        description: "Response on success",
        content: new OA\JsonContent(
            example: [
                'url' => 'https://example.com/api/v1/transaction/download?expires=1746382451&id=17&signature=b264d3a5730fdcbf',
                'expires_at' => '2022-01-01 00:00:00',
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: "Business not found",
        content: new OA\JsonContent(
            example: ["message" => "Business not found"]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "something went wrong",
        content: new OA\JsonContent(
            example: ["message" => "Something went wrong"]
        )
    )]
    public function downloadTransaction(int $transactionId)
    {
        $downloadSize = \request('downloadSize', 'A4');
        $defaultBranch = branchData();
        companyData();
        $this->service->showTransaction($transactionId, $defaultBranch);
        $url = URL::temporarySignedRoute(
            'download.signed.pdf',
            now()->addMinutes(15),
            [
                'id' => $transactionId,
                'downloadSize' => $downloadSize
            ]
        );

        return response()->json([
            'url' => $url,
            'expires_at' => now()->addMinutes(15)->toIso8601String(),
        ]);
    }

    #[OA\Get(
        path: "/api/v1/transaction/download",
        description: "Download transaction using signed url",
        security: [["authentication" => []]],
        summary: "download signed transaction",
        tags: ["Transaction"],
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        required: true,
        example: 1
    )]
    #[OA\QueryParameter(
        name: "expires",
        required: true,
        example: 1
    )]
    #[OA\QueryParameter(
        name: "id",
        required: true,
        example: 1
    )]
    #[OA\QueryParameter(
        name: "signature",
        required: true,
        example: "b264d3a5730fdcbf"
    )]
    #[OA\Response(
        response: 200,
        description: "Response on success",
        content: new OA\MediaType(
            mediaType: 'application/pdf',
            schema: new OA\Schema(
                type: 'string',
                format: 'binary'
            )
        )
    )]
    #[OA\Response(
        response: 404,
        description: "Business not found",
        content: new OA\JsonContent(
            example: ["message" => "Business not found"]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "something went wrong",
        content: new OA\JsonContent(
            example: ["message" => "Something went wrong"]
        )
    )]
    public function downloadPdf()
    {

        $id = \request('id', 0);
        $downloadSize = \request('downloadSize', InvoiceSizeEnums::A4->value);
        $defaultBranch = branchData();
        $company = companyData();
        $transaction = $this->service->showTransaction($id, $defaultBranch);

        $downloadName = "zata-point-invoice-" . date('Y-m-d-H-i-s') . '.pdf';

        $copy = false;

        $pdfMethod = 'transactionInvoice';

        switch ($downloadSize) {
            case InvoiceSizeEnums::M80->value:
                $pdfMethod = 'smallTransactionInvoice';
                break;
            case InvoiceSizeEnums::M58->value:
                $pdfMethod = 'smallTransactionInvoice58';
                break;
            default:
                $pdfMethod = 'transactionInvoice';
                break;
        }

        return response()->streamDownload(
            function () use ($transaction, $company, $defaultBranch, $copy, $pdfMethod) {
                echo $this->pdfService->$pdfMethod($transaction, $company, $defaultBranch, $copy);
            },
            $downloadName,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="' . $downloadName . '"',
                'X-Content-Type-Options' => 'nosniff',
            ]
        );
    }
}
