<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Exceptions\NotFound;
use App\Exceptions\ServerError;
use App\Exceptions\BadRequest;
use App\Exceptions\CompanyNotFound;
use App\Exceptions\Forbidden;
use App\Exceptions\NotAuthorized;
use Illuminate\Http\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Illuminate\Console\Scheduling\Schedule;
use App\Services\CodeService;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(append: [
            \App\Http\Middleware\HandleInertiaRequests::class,
            \Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets::class,
        ]);

        $middleware->trustProxies(at: '*');

        $middleware->api(append: [
            \App\Http\Middleware\ForceJsonResponse::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {

        if (isApi()) {
            $exceptions->render(function (NotFound $e) {
                return response()->json(['message' => $e->getMessage(), "timestamp" => now()->format("Y-m-d, H:i:s"),], Response::HTTP_NOT_FOUND);
            });

            $exceptions->renderable(function (ServerError $e) {
                return response()->json([
                    "message" => $e->getMessage() ?? "Something went wrong.",
                    "timestamp" => now()->format("Y-m-d, H:i:s")
                ], Response::HTTP_INTERNAL_SERVER_ERROR);
            });

            $exceptions->renderable(function (BadRequest $e) {
                return response()->json([
                    'message' => $e->getMessage(),
                    "timestamp" => now()->format("Y-m-d, H:i:s")
                ], Response::HTTP_BAD_REQUEST);
            });

            $exceptions->renderable(function (Forbidden $e) {
                return response()->json([
                    'message' => $e->getMessage(),
                    "timestamp" => now()->format("Y-m-d, H:i:s")
                ], Response::HTTP_FORBIDDEN);
            });

            $exceptions->renderable(function (NotAuthorized $e) {
                return response()->json([
                    'message' => $e->getMessage(),
                    "timestamp" => now()->format("Y-m-d, H:i:s")
                ], Response::HTTP_UNAUTHORIZED);
            });

            $exceptions->renderable(function (AccessDeniedHttpException $e) {
                return response()->json([
                    'message' => "Access Denied",
                    "timestamp" => now()->format("Y-m-d, H:i:s")
                ], Response::HTTP_FORBIDDEN);
            });

            $exceptions->renderable(function (NotFoundHttpException $e) {
                return response()->json([
                    'message' => "Resource not found",
                    "timestamp" => now()->format("Y-m-d, H:i:s")
                ], Response::HTTP_NOT_FOUND);
            });

            $exceptions->renderable(function (\Illuminate\Routing\Exceptions\InvalidSignatureException $e) {
                return response()->json([
                    'message' => "Invalid signature.",
                    "timestamp" => now()->format("Y-m-d, H:i:s")
                ], Response::HTTP_BAD_REQUEST);
            });
        } else {

            $exceptions->renderable(function (BadRequest $e) {
                return redirect()->back()->dangerBanner($e->getMessage() ?: 'An error occurred while processing your request.');
            });

            $exceptions->renderable(function (Forbidden $e) {
                return redirect()->back()->dangerBanner($e->getMessage() ?: 'You are not authorized to perform this action.');
            });

            $exceptions->renderable(function (NotAuthorized $e) {
                return redirect()->back()->dangerBanner($e->getMessage() ?: 'You are not authorized to perform this action.');
            });

            $exceptions->renderable(function (NotFound $e) {
                return redirect()->back()->dangerBanner($e->getMessage() ?: 'The requested resource could not be found.');
            });

            $exceptions->renderable(function (ServerError $e) {
                return redirect()->back()->dangerBanner($e->getMessage() ?: 'Oops! Something went wrong. Please try again. If the issue persists, contact support.');
            });

            $exceptions->renderable(function (AccessDeniedHttpException $e) {
                return redirect()->back()->dangerBanner('Access Denied');
            });

            $exceptions->renderable(function (NotFoundHttpException $e) {
                return redirect()->back()->dangerBanner('The requested page could not be found.');
            });

            $exceptions->renderable(function (CompanyNotFound $e) {
                return redirect()->route('company.list');
            });
        }
    })->withSchedule(function (Schedule $schedule) {
        $schedule->call(function () {

            $service = app(CodeService::class);
            $service->upsertProductsCountriesOfOrigin();
            $service->upsertProductPackingUnit();
            $service->upsertProductQuantityUnit();
            $service->upsertProductItemType();
            $service->upsertProductTaxType();
            $service->upsertPaymentModes();
            $service->upsertItemClassCode();
        })->hourly()->name("Fetching rra codes")->withoutOverlapping();

        // Schedule::command('telescope:prune')->daily();
        
    })
    ->create();
