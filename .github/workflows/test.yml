name: Run Tests On Pull Request

on:
  workflow_dispatch:
  push:
    paths:
      - '.github/workflows/test.yml'

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: zata_backend
          MYSQL_USER: user
          MYSQL_PASSWORD: password
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: "8.3"
          extensions: mbstring, zip, pdo, pdo_mysql, bcmath, intl, pcntl, exif, gd, opcache, xdebug

      - name: Install Composer Dependencies
        run: COMPOSER_AUTH=$COMPOSER_AUTH composer install --prefer-dist --no-progress --no-suggest

      - name: Copy .env.example.testing
        run: cp .env.example.testing .env.testing

      - name: Create Storage Directories
        run: |
          mkdir -p storage/framework/cache
          mkdir -p storage/framework/sessions
          mkdir -p storage/framework/views
          mkdir -p storage/logs

      - name: Set Directory Permissions
        run: chmod -R 777 storage bootstrap/cache

      - name: Run Tests
        run: php artisan test
