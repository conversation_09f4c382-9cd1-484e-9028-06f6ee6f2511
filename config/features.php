<?php

return [
    [
        'company' => [
            'create' => false,
            'read'  => true,
            'update' => true,
            'delete' => false,
            'readReport' => true,
            'activityLog' => true,
        ],
        'description' => 'Company management'
    ],
    [
        'companySubscription' => [
            'read' => true,
            'renew' => true,
        ],
        'description' => 'Company subscription management'
    ],
    [
        'companyAffiliate' => [
            'create' => true,
            'read' => true,
            'update' => true,
            'delete' => false,
        ],
        'description' => 'Company affiliate management'
    ],
    [
        'companyRole' => [
            'create' => true,
            'read' => true,
            'update' => true,
            'delete' => false,
        ],
        'description' => 'Company role management'
    ],
    [
        'companyParty' => [
            'create' => true,
            'read' => true,
            'update' => true,
            'delete' => false
        ],
        'description' => 'Company party management'
    ],
    [
        'companyUser' => [
            'invite' => true,
            'cancelInvite' => true,
            'read' => true,
            'update' => true,
            'delete' => false,
            'listUser' => true,
            'assignRole' => true,
        ],
        'description' => 'Company user management'
    ],
    [
        'warehouse' => [
            'create' => true,
            'read' => true,
            'update' => true,
            'delete' => false
        ],
        'description' => 'Warehouse management'
    ],
    [
        'warehouseStore' => [
            'create' => true,
            'read' => true,
            'update' => true,
            'delete' => false
        ],
        'description' => 'Warehouse store management'
    ],
    [
        'product' => [
            'create' => true,
            'read' => true,
            'update' => true,
            'delete' => false,
        ],
        'description' => 'Product management'
    ],
    [
        'productCategory' => [
            'create' => true,
            'read' => true,
            'update' => true,
            'delete' => false,
        ],
        'description' => 'Product category management'
    ],
    [
        'warehouseStoreProduct' => [
            'create' => true,
            'read' => true,
            'update' => true,
            'delete' => false
        ],
        'description' => 'Warehouse store product management'
    ],
    [
        'expense' => [
            'create' => true,
            'read' => true,
            'update' => true,
            'delete' => false,
        ],
        'description' => 'Expense management'
    ],
    [
        'expenseCategory' => [
            'create' => true,
            'read' => true,
            'update' => true,
            'delete' => false,
        ],
        'description' => 'Expense category management'
    ],
    [
        'transaction' => [
            'readFinanceTransaction' => true,
            'readAccountTransaction' => true,
        ],
        'description' => 'Transaction management'
    ]

];