<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateImportsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'productID' => ['required', 'numeric', 'min:1'],
            'status' => 'required|boolean',
            'salePrice' => 'required|numeric|min:1',
            'purchasePrice' => 'numeric|min:1|lte:salePrice',
            'batchNumber' => 'required|string|max:100',
            'expireDate' => 'required|date|after:today',
            'discountRate' => 'nullable|numeric|min:0|max:100',
        ];
    }
}
