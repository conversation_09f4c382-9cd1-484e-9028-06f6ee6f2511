<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class CompanyParty extends Model
{
    /** @use HasFactory<\Database\Factories\CompanyPartyFactory> */
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'CompanyParty';

    protected $fillable = [
        'company_id',
        'party_type_id',
        'account_id',
        'slug',
        'name',
        'email',
        'tin',
        'phone',
        'address',
        'isActive'
    ];

    protected $casts = [
        'isActive' => 'boolean'
    ];

    public function scopeWhereCompanyId($query, int $companyId)
    {
        return $query->where(function ($query) use ($companyId) {
            $query->where('company_id', $companyId)
                ->orWhereNull('company_id');
        });
    }

    public function partyType(): BelongsTo
    {
        return $this->belongsTo(PartyType::class, 'party_type_id');
    }

    public function patientDetails(): HasOne
    {
        return $this->hasOne(PatientDetails::class, 'party_id');
    }
}
