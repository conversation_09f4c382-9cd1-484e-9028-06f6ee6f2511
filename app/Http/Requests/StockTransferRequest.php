<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StockTransferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'branchID' => 'required|numeric|min:1',
            'batchNumber' => 'required|string|max:100',
            'wholeStock' => 'required|boolean',
            'movingUnit' => 'required|string|in:main,sub',
            'quantity' => 'required_if:wholeStock,false|nullable|integer|min:1',
            'note' => 'required|string|max:200',
        ];
    }

    public function messages() : array
    {
        return [
            'quantity.required_if' => 'Quantity is required',
            'branchID.required' => 'Branch is required',
            'batchNumber.required' => 'Batch number is required',
            'note.required' => 'Reason is required',
            'movingUnit.required' => 'Moving unit is required',
        ];
    }
}
