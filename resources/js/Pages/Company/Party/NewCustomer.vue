<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref, watch } from "vue";
import { useForm } from "@inertiajs/vue3";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  RadioGroup,
  RadioGroupItem,
} from '@/components/ui/radio-group';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';

const props = defineProps({
  Insurance: Array,
});

const form = useForm({
  name: '',
  email: '',
  phone: '',
  address: '',
  tin: '',
  insuranceID: null,
  hasInsurance: 0,
  hasAffiliation: 0,
  gender: 'Male',
  code: '',
  percentage: '',
  expirationDate: null,
  affiliationNumber: '',
  affiliateFirstName: '',
  affiliateLastName: '',
  relationship: '',
  beneficiaryFirstName: '',
  beneficiaryLastName: '',
  beneficiaryNumber: '',
  dateOfBirth: null,
  department: '',
  status: 'Activated',
});

const expirationDate = ref(null);
const dateOfBirth = ref(null);

function createNewParty() {
  form.expirationDate = expirationDate.value ? format(expirationDate.value, 'yyyy-MM-dd') : null;
  form.dateOfBirth = dateOfBirth.value ? format(dateOfBirth.value, 'yyyy-MM-dd') : null;
  form.post(route('customer.store'));
}

const options = [
  { value: 0, label: 'No' },
  { value: 1, label: 'Yes' },
];

const insuranceOptions = props.Insurance.map(insurance => ({
  value: insurance.id,
  label: insurance.name,
}));

const statusOptions = [
  { value: 'Activated', label: 'Activated' },
  { value: 'Deactivated', label: 'Deactivated' },
];

const genderOptions = [
  { value: 'Male', label: 'Male' },
  { value: 'Female', label: 'Female' },
];

watch(() => form.insuranceID, (newInsuranceID) => {
  const selectedInsurance = props.Insurance.find(i => i.id === newInsuranceID);
  form.percentage = selectedInsurance ? selectedInsurance.rate : '';
});
</script>

<template>
  <AppLayout title="New Customer" >
    <template #header>
      <h2 class="font-semibold text-2xl text-gray-900 leading-tight">
        New Customer
      </h2>
    </template>

    <div class="py-6 px-4 sm:px-6 lg:px-8">
      <Card class=" border max-w-3xl mx-auto rounded-md">
        <CardHeader class="bg-gray-50 border-b">
          <CardTitle class="text-xl font-semibold text-gray-800">
            Customer Information
          </CardTitle>
        </CardHeader>
        <CardContent class="p-6">
          <form @submit.prevent="createNewParty" class="space-y-6">
            <div class="space-y-2">
              <Label for="name">Name</Label>
              <Input id="name" v-model="form.name" type="text" />
              <p v-if="form.errors.name" class="text-red-500 text-sm mt-1">{{ form.errors.name }}</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <Label for="email">Email</Label>
                <Input id="email" v-model="form.email" type="email" />
                <p v-if="form.errors.email" class="text-red-500 text-sm mt-1">{{ form.errors.email }}</p>
              </div>
              <div class="space-y-2">
                <Label for="phone">Phone</Label>
                <Input id="phone" v-model="form.phone" type="text" />
                <p v-if="form.errors.phone" class="text-red-500 text-sm mt-1">{{ form.errors.phone }}</p>
              </div>
              <div class="space-y-2">
                <Label for="address">Address</Label>
                <Input id="address" v-model="form.address" type="text" />
                <p v-if="form.errors.address" class="text-red-500 text-sm mt-1">{{ form.errors.address }}</p>
              </div>
              <div class="space-y-2">
                <Label for="tin">Tin</Label>
                <Input id="tin" v-model="form.tin" type="text" />
                <p v-if="form.errors.tin" class="text-red-500 text-sm mt-1">{{ form.errors.tin }}</p>
              </div>
            </div>

            <div class="space-y-2 hidden">
              <Label>Has Insurance</Label>
              <RadioGroup v-model="form.hasInsurance" class="flex space-x-4">
                <div v-for="option in options" :key="option.value" class="flex items-center space-x-2">
                  <RadioGroupItem :value="option.value" :id="`has-insurance-${option.value}`" />
                  <Label :for="`has-insurance-${option.value}`">{{ option.label }}</Label>
                </div>
              </RadioGroup>
              <p v-if="form.errors.hasInsurance" class="text-red-500 text-sm mt-1">{{ form.errors.hasInsurance }}</p>
            </div>

            <div v-if="form.hasInsurance" class="space-y-6">
              <div class="space-y-2">
                <Label>Insurance</Label>
                <Select v-model="form.insuranceID" @update:modelValue="form.insuranceID = $event">
                  <SelectTrigger>
                    <SelectValue placeholder="Select Insurance" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem v-for="option in insuranceOptions" :key="option.value" :value="option.value">
                      {{ option.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.insuranceID" class="text-red-500 text-sm mt-1">{{ form.errors.insuranceID }}</p>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                  <Label for="code">Code</Label>
                  <Input id="code" v-model="form.code" type="text" />
                  <p v-if="form.errors.code" class="text-red-500 text-sm mt-1">{{ form.errors.code }}</p>
                </div>
                <div class="space-y-2">
                  <Label for="percentage">Percentage</Label>
                  <Input id="percentage" v-model="form.percentage" type="text" />
                  <p v-if="form.errors.percentage" class="text-red-500 text-sm mt-1">{{ form.errors.percentage }}</p>
                </div>
              </div>

              <div class="space-y-2">
                <Label>Expiration Date</Label>
                <Popover>
                  <PopoverTrigger as-child>
                    <Button
                      variant="outline"
                      class="w-full justify-start text-left font-normal"
                      :class="{ 'text-muted-foreground': !expirationDate }"
                    >
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      {{ expirationDate ? format(expirationDate, 'PPP') : 'Pick a date' }}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent class="w-auto p-0">
                    <Calendar v-model="expirationDate" />
                  </PopoverContent>
                </Popover>
                <p v-if="form.errors.expirationDate" class="text-red-500 text-sm mt-1">{{ form.errors.expirationDate }}</p>
              </div>

              <div class="space-y-2">
                <Label>Gender</Label>
                <RadioGroup v-model="form.gender" class="flex space-x-4">
                  <div v-for="option in genderOptions" :key="option.value" class="flex items-center space-x-2">
                    <RadioGroupItem :value="option.value" :id="`gender-${option.value}`" />
                    <Label :for="`gender-${option.value}`">{{ option.label }}</Label>
                  </div>
                </RadioGroup>
                <p v-if="form.errors.gender" class="text-red-500 text-sm mt-1">{{ form.errors.gender }}</p>
              </div>

              <div class="space-y-2">
                <Label>Has Affiliation</Label>
                <RadioGroup v-model="form.hasAffiliation" class="flex space-x-4">
                  <div v-for="option in options" :key="option.value" class="flex items-center space-x-2">
                    <RadioGroupItem :value="option.value" :id="`has-affiliation-${option.value}`" />
                    <Label :for="`has-affiliation-${option.value}`">{{ option.label }}</Label>
                  </div>
                </RadioGroup>
                <p v-if="form.errors.hasAffiliation" class="text-red-500 text-sm mt-1">{{ form.errors.hasAffiliation }}</p>
              </div>

              <div v-if="form.hasAffiliation" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                  <Label for="affiliationNumber">Affiliation Number</Label>
                  <Input id="affiliationNumber" v-model="form.affiliationNumber" type="text" />
                  <p v-if="form.errors.affiliationNumber" class="text-red-500 text-sm mt-1">{{ form.errors.affiliationNumber }}</p>
                </div>
                <div class="space-y-2">
                  <Label for="affiliateFirstName">Affiliation First Name</Label>
                  <Input id="affiliateFirstName" v-model="form.affiliateFirstName" type="text" />
                  <p v-if="form.errors.affiliateFirstName" class="text-red-500 text-sm mt-1">{{ form.errors.affiliateFirstName }}</p>
                </div>
                <div class="space-y-2">
                  <Label for="affiliateLastName">Affiliation Last Name</Label>
                  <Input id="affiliateLastName" v-model="form.affiliateLastName" type="text" />
                  <p v-if="form.errors.affiliateLastName" class="text-red-500 text-sm mt-1">{{ form.errors.affiliateLastName }}</p>
                </div>
                <div class="space-y-2">
                  <Label for="relationship">Relationship</Label>
                  <Input id="relationship" v-model="form.relationship" type="text" />
                  <p v-if="form.errors.relationship" class="text-red-500 text-sm mt-1">{{ form.errors.relationship }}</p>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                  <Label for="beneficiaryFirstName">Beneficiary First Name</Label>
                  <Input id="beneficiaryFirstName" v-model="form.beneficiaryFirstName" type="text" />
                  <p v-if="form.errors.beneficiaryFirstName" class="text-red-500 text-sm mt-1">{{ form.errors.beneficiaryFirstName }}</p>
                </div>
                <div class="space-y-2">
                  <Label for="beneficiaryLastName">Beneficiary Last Name</Label>
                  <Input id="beneficiaryLastName" v-model="form.beneficiaryLastName" type="text" />
                  <p v-if="form.errors.beneficiaryLastName" class="text-red-500 text-sm mt-1">{{ form.errors.beneficiaryLastName }}</p>
                </div>
              </div>

              <div class="space-y-2">
                <Label for="beneficiaryNumber">Beneficiary Number</Label>
                <Input id="beneficiaryNumber" v-model="form.beneficiaryNumber" type="text" />
                <p v-if="form.errors.beneficiaryNumber" class="text-red-500 text-sm mt-1">{{ form.errors.beneficiaryNumber }}</p>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                  <Label>Date of Birth</Label>
                  <Popover>
                    <PopoverTrigger as-child>
                      <Button
                        variant="outline"
                        class="w-full justify-start text-left font-normal"
                        :class="{ 'text-muted-foreground': !dateOfBirth }"
                      >
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        {{ dateOfBirth ? format(dateOfBirth, 'PPP') : 'Pick a date' }}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent class="w-auto p-0">
                      <Calendar v-model="dateOfBirth" />
                    </PopoverContent>
                  </Popover>
                  <p v-if="form.errors.dateOfBirth" class="text-red-500 text-sm mt-1">{{ form.errors.dateOfBirth }}</p>
                </div>
                <div class="space-y-2">
                  <Label for="department">Department</Label>
                  <Input id="department" v-model="form.department" type="text" />
                  <p v-if="form.errors.department" class="text-red-500 text-sm mt-1">{{ form.errors.department }}</p>
                </div>
              </div>

              <div class="space-y-2">
                <Label>Status</Label>
                <RadioGroup v-model="form.status" class="flex space-x-4">
                  <div v-for="option in statusOptions" :key="option.value" class="flex items-center space-x-2">
                    <RadioGroupItem :value="option.value" :id="`status-${option.value}`" />
                    <Label :for="`status-${option.value}`">{{ option.label }}</Label>
                  </div>
                </RadioGroup>
                <p v-if="form.errors.status" class="text-red-500 text-sm mt-1">{{ form.errors.status }}</p>
              </div>
            </div>

            <div class="flex justify-end">
              <Button 
                type="submit" 
                :disabled="form.processing"
                :class="{ 'opacity-25': form.processing }"
                
              >
                Save
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>

    <template #sidenav />
    <template #footer />
  </AppLayout>
</template>