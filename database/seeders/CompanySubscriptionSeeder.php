<?php

namespace Database\Seeders;

use App\Models\AccountSubscription;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CompanySubscriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        AccountSubscription::create([
            'user_id' => 1,
            'isActive' => true,
            'startDate' => now(),
            'subscriptionType' => 'Basic',
        ]);
    }
}
