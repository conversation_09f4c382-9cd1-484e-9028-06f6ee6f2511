<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class NewCompanyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:40|min:3',
            'phone' => 'required|string|max:12|min:10',
            'email' => 'nullable|string|max:255|min:5|email',
            'address' => 'required|string|max:255|min:3',
            'tin' => 'nullable|string|max:9|min:9',
            'isEBM' => 'required|boolean',
        ];
    }
}
