
<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref, computed, reactive, watch } from 'vue';
import { useForm } from '@inertiajs/vue3';
import { Link } from '@inertiajs/vue3';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';

const props = defineProps({
    Imports: Object,
    currentPage: Number,
    lastPage: Number,
    itemsPerPage: Number,
    total: Number,
    pageItems: Number,
});

// Define table headers based on the import data structure
const tableHeaders = ['ID', 'Name', 'Quantity', 'Unit', 'Supplier', 'Declaration No', 'Declaration Date', 'Invoice Amount', 'Currency', 'Status'];

// Map imports data to table rows
const tableRows = computed(() => {
    return props.Imports.map((importItem) => ({
        id: importItem.id,
        name: importItem.name,
        qty: importItem.qty,
        qtyUnitCd: importItem.qtyUnitCd,
        spplrNm: importItem.spplrNm,
        dclNo: importItem.dclNo,
        dclDe: importItem.dclDe,
        invcFcurAmt: importItem.invcFcurAmt,
        invcFcurCd: importItem.invcFcurCd,
        status: importItem.status,
    }));
});

// Pagination logic
const paginationState = reactive({
    currentPage: props.currentPage,
    itemsPerPage: props.itemsPerPage,
    totalPages: props.lastPage,
});

const paginationForm = useForm({
    page: paginationState.currentPage,
    perPage: paginationState.itemsPerPage,
});

const handleItemsPerPageChange = (value) => {
    paginationState.itemsPerPage = parseInt(value);
    paginationState.currentPage = 1;
    updatePagination();
};

const prevPage = () => {
    if (paginationState.currentPage > 1) {
        paginationState.currentPage -= 1;
        updatePagination();
    }
};

const nextPage = () => {
    if (paginationState.currentPage < paginationState.totalPages) {
        paginationState.currentPage += 1;
        updatePagination();
    }
};

const updatePagination = () => {
    paginationForm.page = paginationState.currentPage;
    paginationForm.perPage = paginationState.itemsPerPage;
    paginationForm.get(route('import.index'), {
        preserveState: true,
        preserveScroll: true,
    });
};

// Watch props changes
watch(() => [props.currentPage, props.lastPage], ([newCurrentPage, newLastPage]) => {
    paginationState.currentPage = newCurrentPage;
    paginationState.totalPages = newLastPage;
});

watch(() => props.itemsPerPage, (newItemsPerPage) => {
    paginationState.itemsPerPage = newItemsPerPage;
});
</script>

<template>
    <AppLayout title="Importation">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Imports
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="flex justify-between pb-3">
                    <h3 class="text-xl font-semibold text-gray-800">Imports</h3>
                    <div class="space-x-2">
                        <Button as-child>
                            <Link :href="route('import.sync')">
                            Sync from EBM
                            </Link>
                        </Button>
                    </div>
                </div>

                <div class="flex justify-between items-center mb-4">
                    <div class="text-sm text-gray-600">
                        Showing {{ props.pageItems }} of {{ props.total }} items
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-600">Items per page:</span>
                            <Select v-model="paginationState.itemsPerPage"
                                @update:modelValue="handleItemsPerPageChange">
                                <SelectTrigger class="w-20">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="5">5</SelectItem>
                                    <SelectItem value="10">10</SelectItem>
                                    <SelectItem value="20">20</SelectItem>
                                    <SelectItem value="50">50</SelectItem>
                                    <SelectItem value="100">100</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div class="flex items-center space-x-2">
                            <Button variant="outline" @click="prevPage" :disabled="paginationState.currentPage === 1">
                                Previous
                            </Button>
                            <span class="text-sm text-gray-600">
                                Page {{ paginationState.currentPage }} of {{ paginationState.totalPages }}
                            </span>
                            <Button variant="outline" @click="nextPage"
                                :disabled="paginationState.currentPage === paginationState.totalPages">
                                Next
                            </Button>
                        </div>
                    </div>
                </div>

                <div v-if="tableRows.length > 0" class="rounded-lg border">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead v-for="header in tableHeaders" :key="header" class="font-semibold">
                                    {{ header }}
                                </TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            <TableRow v-for="row in tableRows" :key="row.id" class="hover:bg-muted">
                                <TableCell>{{ row.id }}</TableCell>
                                <TableCell>{{ row.name || '-' }}</TableCell>
                                <TableCell>{{ row.qty || '-' }}</TableCell>
                                <TableCell>{{ row.qtyUnitCd || '-' }}</TableCell>
                                <TableCell>{{ row.spplrNm || '-' }}</TableCell>
                                <TableCell>{{ row.dclNo || '-' }}</TableCell>
                                <TableCell>{{ row.dclDe || '-' }}</TableCell>
                                <TableCell>{{ row.invcFcurAmt || '-' }}</TableCell>
                                <TableCell>{{ row.invcFcurCd || '-' }}</TableCell>
                                <TableCell>{{ row.status || '-' }}</TableCell>
                                <TableCell>
                                    <Link :href="route('import.show', row.id)" class="text-zata-primary-light underline"
                                        v-if="row.status === 'pending'">
                                    update
                                    </Link>
                                </TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                </div>

                <Card v-else class="mt-5">
                    <CardContent class="text-center text-gray-500 py-56">
                        No imports found.
                    </CardContent>
                </Card>
            </div>
        </div>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>