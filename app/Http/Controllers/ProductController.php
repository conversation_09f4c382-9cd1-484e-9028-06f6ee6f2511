<?php

namespace App\Http\Controllers;

use App\Data\ConfirmProductSyncData;
use App\Data\NewProductData;
use App\Data\StockTransferData;
use App\Data\UpdateBatchData;
use App\Data\UpdateProductData;
use App\Data\UpdateProductQuantityData;
use App\Data\UpdateProductQuantityOutData;
use App\Enums\SubscriptionActionEnums;
use App\Http\Requests\ConfirmProductSyncRequest;
use App\Http\Requests\NewProductRequest;
use App\Http\Requests\StockTransferRequest;
use App\Http\Requests\UpdateBatchRequest;
use App\Http\Requests\UpdateProductQuantityOutRequest;
use App\Http\Requests\UpdateProductQuantityRequest;
use App\Http\Requests\UpdateProductRequest;
use App\Services\AccountSubscriptionService;
use App\Services\CommonDataService;
use App\Services\CompanyPartyService;
use App\Services\PdfService;
use App\Services\ProductService;
use App\Services\UserService;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
use App\Enums\AccountTypeEnums;

class ProductController extends Controller
{

    protected $user;
    public function __construct(
        protected ProductService $service,
        protected CommonDataService $commonDataService,
        protected PdfService $pdfService,
        protected UserService $userService,
        protected CompanyPartyService $companyPartyService,
        protected AccountSubscriptionService $subscriptionService
    ) {

        $this->user = Auth::user();
    }

    public function index()
    {

        $defaultBranch = branchData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $searchQuery = \request()->query('searchQuery', null);
        $response =  $this->pagination($this->service->getProducts($defaultBranch->id, $perPage, $page, $searchQuery));

        return Inertia::render('Product/Index', [
            'Products' => $response['data'],
            'currentPage' => $response['currentPage'],
            'lastPage' => $response['lastPage'],
            'itemsPerPage' => $response['itemsPerPage'],
            'pageItems' => $response['pageItems'],
            'total' => $response['total'],
            'isEBM' => $defaultBranch->isEBM,
            'searchQuery' => $searchQuery
        ]);
    }

    public function new()
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $this->subscriptionService->restrict($branch->company_id, SubscriptionActionEnums::CREATE_PRODUCT, $branch->id);
        $productCategories = $this->commonDataService->getProductCategory();
        $productClasses = $this->commonDataService->getProductClass();
        $productQuantityUnits = $this->commonDataService->getProductQuantityUnit();
        $productPackagingUnits = $this->commonDataService->getProductPackagingUnit();
        $taxes = $this->commonDataService->getProductTax($branch->isEBM);
        $countries = $this->commonDataService->getProductCountry();

        $branchProductCategories = $this->commonDataService->getBranchProductCategory($branch);

        return Inertia::render('Product/New', [
            'isEBM' => $branch->isEBM,
            'ProductCategories' => $productCategories,
            'ProductClasses' => $productClasses,
            'ProductQuantityUnits' => $productQuantityUnits,
            'ProductPackagingUnits' => $productPackagingUnits,
            'Taxes' => $taxes,
            'Countries' => $countries,
            'BranchProductCategories' => $branchProductCategories,
        ]);
    }

    public function sync()
    {
        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $searchQuery = \request()->query('searchQuery', null);
        $commonProduct = $this->pagination($this->service->getCommonProduct($perPage, $page, $searchQuery));
        $taxes = $this->commonDataService->getProductTax($branch->isEBM);
        return Inertia::render('Product/Sync', [
            'Products' => $commonProduct['data'],
            'searchQuery' => $searchQuery,
            'currentPage' => $commonProduct['currentPage'],
            'lastPage' => $commonProduct['lastPage'],
            'itemsPerPage' => $commonProduct['itemsPerPage'],
            'pageItems' => $commonProduct['pageItems'],
            'total' => $commonProduct['total'],
            'Taxes' => $taxes

        ]);
    }

    public function confirmSync(ConfirmProductSyncRequest $request)
    {

            zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $data = ConfirmProductSyncData::from($request->validated());
        $this->service->confirmProductSync($data, $branch);

        return redirect()->route('product.sync')->banner('Product Synced Successfully');
    }

    public function store(NewProductRequest $request)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $defaultCompany = companyData();
        $defaultBranch = branchData();
        $this->subscriptionService->restrict($defaultCompany->id, SubscriptionActionEnums::CREATE_PRODUCT);
        $data = NewProductData::from($request->validated());

        $this->service->store($data, $defaultBranch, $defaultCompany);

        return redirect()->route('product.index')->banner('Product Created Successfully');
    }

    public function edit(int $productId)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $product = $this->service->getProductById($branch->id, $productId);

        $branchProductCategories = $this->commonDataService->getBranchProductCategory($branch);

        return Inertia::render('Product/Edit', [
            'Product' => $product,
            'BranchProductCategories' => $branchProductCategories
        ]);
    }

    public function update(UpdateProductRequest $request, int $productId)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $defaultBranch = branchData();
        $data = UpdateProductData::from($request->validated());
        $this->service->update($data, $defaultBranch, $productId);

        return redirect()->route('product.index')->banner('Product Updated Successfully');
    }

    public function show(int $productId)
    {
        $branch = branchData();
        $product = $this->service->getProductById($branch->id, $productId);

        return Inertia::render('Product/Show', [
            'Product' => $product
        ]);
    }

    public function stockIn(int $productId)
    {
        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $product = $this->service->getProductById($branch->id, $productId);

        return Inertia::render('Product/StockIn', [
            'Product' => $product
        ]);
    }
    public function stockInStore(UpdateProductQuantityRequest $request, int $productId)
    {
        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $defaultBranch = branchData();
        $data = UpdateProductQuantityData::from($request->validated());

        $this->service->increaseProductQuantity($data, $defaultBranch, $productId, $this->user->id);

        return redirect()->route('product.index')->banner('Stock Added Successfully');
    }

    public function stockOut(int $productId)
    {
        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $product = $this->service->getProductById($branch->id, $productId);

        return Inertia::render('Product/StockOut', [
            'Product' => $product
        ]);
    }

    public function stockOutStore(UpdateProductQuantityOutRequest $request, int $productId)
    {
        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $defaultBranch = branchData();
        $data = UpdateProductQuantityOutData::from($request->validated());

        $this->service->reduceProductQuantity($data, $defaultBranch, $productId, $this->user->id);

        return redirect()->route('product.index')->banner('Stock Deducted Successfully');
    }

    public function stockTransfer(int $productId)
    {
        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $company = companyData();
        $product = $this->service->getProductById($branch->id, $productId);

        return Inertia::render('Product/StockTransfer', [
            'Product' => $product,
            'Branches' => $company->branches
                ->where('id', '!=', $branch->id)
                ->map(fn($branch) => ['id' => $branch->id, 'name' => $branch->name])
                ->values()
                ->toArray()
        ]);
    }

    public function stockTransferStore(StockTransferRequest $request, int $productId)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $defaultBranch = branchData();
        $company = companyData();
        $data = StockTransferData::from($request->validated());
        $this->service->stockTransfer($data, $company, $defaultBranch, $productId, $this->user->id);

        return redirect()->route('product.show', $productId)->banner('Stock Transfered Successfully');
    }

    public function stockHistory()
    {

        $defaultBranch = branchData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $searchQuery = \request()->query('searchQuery', null);
        $stockType = \request()->query('stockType', null);
        $fromDate = \request()->query('fromDate', null);
        $toDate = \request()->query('toDate', null);
        $productID = \request()->query('productID', null);
        $cashierID = \request()->query('cashierID', null);
        $partyID = \request()->query('partyID', null);
        $products =  $this->pagination($this->service->getProducts($defaultBranch->id, perPage: $perPage, page: $page, searchQuery: null));
        $cashiers = $this->userService->getCompanyUsers($defaultBranch->id);
        $party = $this->companyPartyService->companyParties($defaultBranch->id);
        $response =  $this->pagination($this->service->getStockHistory(
            $defaultBranch->id,
            perPage: $perPage,
            page: $page,
            searchQuery: $searchQuery,
            stockType: $stockType,
            fromDate: $fromDate,
            toDate: $toDate,
            productID: $productID,
            cashierID: $cashierID,
            partyID: $partyID
        ));

        $parties = array_map(function ($value) {
            return [
                'id' => $value['id'],
                'name' => $value['name'],
            ];
        }, $party);

        return Inertia::render('Product/StockHistory', [
            'Products' => $products['data'],
            'Cashiers' => $cashiers,
            'Parties' => $parties,
            'StockHistories' => $response['data'],
            'currentPage' => $response['currentPage'],
            'lastPage' => $response['lastPage'],
            'itemsPerPage' => $response['itemsPerPage'],
            'pageItems' => $response['pageItems'],
            'total' => $response['total'],
            'searchQuery' => $searchQuery,
            'stockType' => $stockType,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'productID' => $productID,
            'cashierID' => $cashierID,
            'partyID' => $partyID
        ]);
    }

    public function stockHistoryDownload()
    {

        $company = companyData();
        $defaultBranch = branchData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $searchQuery = \request()->query('searchQuery', null);
        $stockType = \request()->query('stockType', null);
        $fromDate = \request()->query('fromDate', null);
        $toDate = \request()->query('toDate', null);
        $productID = \request()->query('productID', null);
        $cashierID = \request()->query('cashierID', null);
        $partyID = \request()->query('partyID', null);
        $transaction =  $this->pagination($this->service->getStockHistory(
            $defaultBranch->id,
            perPage: $perPage,
            page: $page,
            searchQuery: $searchQuery,
            stockType: $stockType,
            fromDate: $fromDate,
            toDate: $toDate,
            productID: $productID,
            cashierID: $cashierID,
            partyID: $partyID
        ));
        $transaction = $transaction['data'];

        if (!$transaction) {
            return response()->json(['error' => 'Transaction not found'], 404);
        }

        $downloadName = "zata-stock-history-" . date('Y-m-d-H-i') . '.pdf';

        return response()->streamDownload(
            function () use ($transaction, $company, $defaultBranch) {
                echo $this->pdfService->stockHistory($transaction, $company, $defaultBranch);
            },
            $downloadName,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="' . $downloadName . '"',
            ]
        );
    }

    public function syncEbm()
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $defaultBranch = branchData();
        $this->service->syncEbm($defaultBranch);
    }

    public function editBatch(int $productId, int $batchId)
    {
        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $defaultBranch = branchData();

        $data =  $this->service->editBatch($defaultBranch, $productId, $batchId);

        return Inertia::render('Product/EditBatch', [
            'Batch' => $data['batch'],
            'Product' => $data['product'],
        ]);
    }

    public function updateBatch(UpdateBatchRequest $request, int $productId, int $batchId)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $defaultBranch = branchData();
        $data = UpdateBatchData::from($request->validated());

        $this->service->updateBatch($data, $defaultBranch, $productId, $batchId);

        return redirect()->route('product.index')->banner('Product Updated Successfully');
    }
}
