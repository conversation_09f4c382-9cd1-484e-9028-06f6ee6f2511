<script setup>
import { ref } from 'vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import DialogModal from '@/Components/DialogModal.vue';

const props = defineProps({
    Subscription: Object,
    SubscriptionList: Array,
});

// Reactive state
const isYearly = ref(false);
const salesModalControl = ref(false); // Sales modal

// Modal controls
const openSalesModal = () => {
    salesModalControl.value = true;
};

const closeSalesModal = () => {
    salesModalControl.value = false;
};

// Formatting and logic
const formatPrice = (price) => {
    if (price === null) return 'Custom';
    if (price === 0) return 'Frw 0';
    return `Frw ${price.toLocaleString()}`;
};

const isCurrentSubscription = (subscriptionName) => {
    return props.Subscription?.subscription?.subscriptionType === subscriptionName;
};

const getButtonConfig = (subscription) => {
    if (subscription.name === 'Basic') {
        return { show: false, text: '', variant: '' };
    }
    return {
        show: true,
        text: 'Talk to Sales',
        variant: 'secondary',
    };
};

// Actions
const handleButtonClick = () => {
    openSalesModal();
};
</script>

<template>
    <AppLayout title="Update Subscription">
        <template #header>
            <h2 class="font-bold text-3xl text-gray-900 tracking-tight">
                Choose Your Subscription Plan
            </h2>
            <p class="mt-2 text-lg text-gray-600">
                Select the plan that best fits your business needs.
            </p>
            <p class="mt-2 text-lg text-red-600">
                Payment processing is currently under maintenance. Please contact our sales team for more information.
            </p>
        </template>

        <div class="mx-auto mt-12 max-w-7xl">
            <!-- Billing toggle -->
            <div class="mb-8 flex justify-center">
                <div class="inline-flex rounded-md bg-gray-100 p-1 shadow-sm">
                    <button
                        @click="isYearly = false"
                        class="px-4 py-2 text-sm font-medium rounded-md transition-colors"
                        :class="!isYearly ? 'bg-white text-gray-900 shadow-sm' : 'bg-transparent text-gray-600 hover:text-gray-900'"
                    >
                        Monthly
                    </button>
                    <button
                        @click="isYearly = true"
                        class="px-4 py-2 text-sm font-medium rounded-md transition-colors"
                        :class="isYearly ? 'bg-white text-gray-900 shadow-sm' : 'bg-transparent text-gray-600 hover:text-gray-900'"
                    >
                        Yearly
                    </button>
                </div>
            </div>

            <!-- Subscription cards -->
            <div class="grid grid-cols-1 gap-8 md:grid-cols-3">
                <Card
                    v-for="(subscription, index) in SubscriptionList"
                    :key="index"
                    class="flex min-h-[600px] flex-col rounded-md border bg-white transition-shadow duration-300"
                    :class="isCurrentSubscription(subscription.name) ? 'border-zata-primary-light' : 'border-gray-200'"
                >
                    <CardHeader class="relative rounded-t-md bg-gradient-to-r from-gray-50 to-gray-100 py-4">
                        <CardTitle class="text-xl font-semibold text-gray-900">
                            {{ subscription.name }}
                        </CardTitle>
                        <div
                            v-if="isCurrentSubscription(subscription.name)"
                            class="absolute right-4 top-4 rounded bg-zata-primary-dark px-2 py-1 text-xs font-semibold text-white"
                        >
                            Current Plan
                        </div>
                        <p class="mt-2 text-3xl font-bold text-gray-900">
                            {{ formatPrice(isYearly ? subscription.yearly_price : subscription.monthly_price) }}
                            <span
                                v-if="(isYearly ? subscription.yearly_price : subscription.monthly_price) !== null && (isYearly ? subscription.yearly_price : subscription.monthly_price) !== 0"
                                class="text-sm font-normal text-gray-600"
                            >
                                {{ isYearly ? '/yr' : '/mo' }}
                            </span>
                        </p>
                    </CardHeader>
                    <CardContent class="flex-grow p-6">
                        <ul class="space-y-4 text-gray-700">
                            <li class="flex items-center">
                                <svg class="mr-2 h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>{{ subscription.allowed_branches === 'unlimited' ? 'Unlimited' : subscription.allowed_branches }} Branches</span>
                            </li>
                            <li class="flex items-center">
                                <svg class="mr-2 h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>{{ subscription.allowed_users === 'unlimited' ? 'Unlimited' : subscription.allowed_users }} Users</span>
                            </li>
                            <li class="flex items-center">
                                <svg class="mr-2 h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>{{ subscription.allowed_products_per_branch === 'unlimited' ? 'Unlimited' : subscription.allowed_products_per_branch }} Products per Branch</span>
                            </li>
                            <li class="flex items-center">
                                <svg class="mr-2 h-5 w-5" :class="subscription.api_access ? 'text-green-500' : 'text-gray-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="subscription.api_access ? 'M5 13l4 4L19 7' : 'M6 18L18 6M6 6l12 12'" />
                                </svg>
                                <span>API Access</span>
                            </li>
                            <li class="flex items-center">
                                <svg class="mr-2 h-5 w-5" :class="subscription.custom_plugins ? 'text-green-500' : 'text-gray-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="subscription.custom_plugins ? 'M5 13l4 4L19 7' : 'M6 18L18 6M6 6l12 12'" />
                                </svg>
                                <span>Custom Plugins</span>
                            </li>
                            <li class="flex items-center">
                                <svg class="mr-2 h-5 w-5" :class="subscription.ai_assistant ? 'text-green-500' : 'text-gray-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="subscription.ai_assistant ? 'M5 13l4 4L19 7' : 'M6 18L18 6M6 6l12 12'" />
                                </svg>
                                <span>AI Assistant</span>
                            </li>
                            <li class="flex items-center">
                                <svg class="mr-2 h-5 w-5" :class="subscription.feature_requests ? 'text-green-500' : 'text-gray-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="subscription.feature_requests ? 'M5 13l4 4L19 7' : 'M6 18L18 6M6 6l12 12'" />
                                </svg>
                                <span>Feature Requests</span>
                            </li>
                            <li class="flex items-center">
                                <svg class="mr-2 h-5 w-5" :class="subscription.ebm ? 'text-green-500' : 'text-gray-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="subscription.ebm ? 'M5 13l4 4L19 7' : 'M6 18L18 6M6 6l12 12'" />
                                </svg>
                                <span>EBM Support</span>
                            </li>
                        </ul>
                        <Button
                            v-if="getButtonConfig(subscription).show"
                            :variant="getButtonConfig(subscription).variant"
                            class="mt-6 w-full text-lg font-semibold"
                            @click="handleButtonClick(subscription)"
                        >
                            {{ getButtonConfig(subscription).text }}
                        </Button>
                    </CardContent>
                </Card>
            </div>
        </div>

        <!-- Sales Contact Modal -->
        <DialogModal :show="salesModalControl" @close="closeSalesModal">
            <template #title>Contact Sales</template>
            <template #content>
                <div class="p-6">
                    <p class="text-lg text-gray-700 text-center">
                        Reach out to our sales team for plan details or custom solutions.
                    </p>
                    <div class="mt-6 space-y-4">
                        <div class="flex items-center">
                            <svg class="mr-3 h-6 w-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <a href="mailto:<EMAIL>" class="text-blue-600 hover:underline">
                               <EMAIL>
                            </a>
                        </div>
                        <div class="flex items-center">
                            <svg class="mr-3 h-6 w-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                            <a href="tel:+250798684904" class="text-blue-600 hover:underline">
                                +250 798 684 904
                            </a>
                        </div>
                    </div>
                    <div class="mt-8 flex justify-end">
                        <Button variant="outline" @click="closeSalesModal">Close</Button>
                    </div>
                </div>
            </template>
        </DialogModal>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>

<style scoped>
</style>