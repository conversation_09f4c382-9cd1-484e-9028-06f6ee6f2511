<script setup>
import { computed } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticationCard from '@/Components/AuthenticationCard.vue';
import AuthenticationCardLogo from '@/Components/AuthenticationCardLogo.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryLink from '@/Components/SecondaryLink.vue';
import { Button } from '@/components/ui/button';

const props = defineProps({
    status: String,
});

const form = useForm({});

const submit = () => {
    form.post(route('verification.send'));
};

const verificationLinkSent = computed(() => props.status === 'verification-link-sent');
</script>

<template>

    <Head title="Email Verification" />

    <AuthenticationCard>
        <template #logo>
            <AuthenticationCardLogo />
        </template>

        <div class="mb-4 text-sm text-gray-600">
            Before continuing, could you verify your email address by clicking on the link we just emailed to you? If
            you didn't
            receive the email, we will gladly send you another.
        </div>

        <div v-if="verificationLinkSent" class="mb-4 font-medium text-sm text-green-600">
            A new verification link has been sent to the email address you provided in your profile settings.
        </div>

        <form @submit.prevent="submit">
            <div class="mt-4 flex items-center justify-between">
                <Button :class="{ 'opacity-25': form.processing }" :disabled="form.processing" class="w-full" type="submit">
                    Resend verification email
                </Button>


            </div>
            <div class="flex justify-between py-4">
                <Button as-child variant="secondary" class="w-full text-center" type="button">
                   
                 <Link  :href="route('profile.show')"> Edit profile</Link>
                </Button>

            </div>


            <div class="flex justify-center py-4">
                <!-- <Link class="text-sm text-red-600 underline text-center" :href="route('logout')" method="post"
                    as="button">
                Log out
                </Link> -->

                <Button as-child="" variant="destructive" class="w-full text-center" type="button">
                    <Link :href="route('logout')" method="post" as="button">
                        Log out
                    </Link>
                </Button>
            </div>
        </form>
    </AuthenticationCard>
</template>
