<?php

namespace Database\Seeders;

use App\Enums\PartyTypeEnums;
use App\Models\CompanyParty;
use App\Models\PartyType;
use Illuminate\Database\Seeder;

class CompanyPartySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        CompanyParty::factory()->create([

            'company_id' => null,
            'party_type_id' => PartyType::where('name', PartyTypeEnums::CUSTOMER->value)->first()->id,
            'name' => 'Walkin Customer',
            'slug' => 'walkin-customer',
            'tin' => null,
            'phone' => null,
            'address' => null,
            'email' => null,
        ]);

        CompanyParty::factory()->create([

            'company_id' => null,
            'party_type_id' => PartyType::where('name', PartyTypeEnums::SUPPLIER->value)->first()->id,
            'name' => 'Walkin Supplier',
            'slug' => 'walkin-supplier',
            'tin' => null,
            'phone' => null,
            'address' => null,
            'email' => null
        ]);
    }
}
