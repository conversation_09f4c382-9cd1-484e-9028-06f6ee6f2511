<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PaymentMode;

class PaymentModeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $paymentModes = [
            [
                'cd' => '01',
                'cdNm' => 'CASH',
                'cdDesc' => 'CASH',
                'srtOrd' => 1,
            ],
            [
                'cd' => '02',
                'cdNm' => 'CREDIT',
                'cdDesc' => 'CREDIT',
                'srtOrd' => 2,
            ],
            [
                'cd' => '03',
                'cdNm' => 'CASH/CREDIT',
                'cdDesc' => 'CASH/CREDIT',
                'srtOrd' => 3,
            ],
            [
                'cd' => '04',
                'cdNm' => 'BANK CHECK',
                'cdDesc' => 'BANK CHECK PAYMENT',
                'srtOrd' => 4,
            ],
            [
                'cd' => '05',
                'cdNm' => 'DEBIT & CREDIT CARD',
                'cdDesc' => 'PAYMENT USING CARD',
                'srtOrd' => 5,
            ],
            [
                'cd' => '06',
                'cdNm' => 'MOBILE MONEY',
                'cdDesc' => 'ANY TRANSACTION USING MOBILE MONEY SYSTEM',
                'srtOrd' => 6,
            ],
            [
                'cd' => '07',
                'cdNm' => 'OTHER',
                'cdDesc' => 'OTHER MEANS OF PAYMENT',
                'srtOrd' => 7,
            ],
        ];

        foreach ($paymentModes as $key => $value) {
            PaymentMode::factory()->create([
                'name' => $value['cdNm'],
                'description' => $value['cdDesc'],
                'code' => $value['cd'],
                'forEbm' => true,
            ]);
        }
    }
}