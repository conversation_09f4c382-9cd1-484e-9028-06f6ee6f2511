<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('LogTrack', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->nullable()->references('id')->on('Company')->onDelete('restrict');
            $table->foreignId('branch_id')->nullable()->references('id')->on('CompanyBranch')->onDelete('restrict');
            $table->foreignId("user_id")->references('id')->on('users')->onDelete('restrict');
            $table->string('message');
            $table->enum("action", ["Delete", "Update", "Read", "Create"]);
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('LogTrack');
    }
};
