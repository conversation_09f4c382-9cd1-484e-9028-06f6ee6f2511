<script setup>
import PrimaryLink from '@/Components/PrimaryLink.vue';
import SecondaryLink from '@/Components/SecondaryLink.vue';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
    Insurance: Array,
});

</script>

<template>
    <AppLayout title="Supported Insurance">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Supported Insurance
            </h2>
        </template>

        <section class="text-gray-600 body-font">
            <div class="container px-5  mx-auto">
                <div class="flex flex-col w-full mb-20">
                    <h1 class="sm:text-3xl text-2xl font-medium title-font mb-4 text-gray-900">Company Supported
                        Insurance</h1>
                    <p class="lg:w-2/3  leading-relaxed text-base">
                        All the insurance companies that are supported by this company.
                    </p>
                </div>
                <div class="flex flex-wrap -m-2" v-if="Insurance.length > 0">
                    <div class="p-2 lg:w-1/3 md:w-1/2 w-full" v-for="ins in Insurance" :key="ins.id">
                        <div class="h-full flex items-center border-gray-200 border p-4 rounded-lg hover:bg-gray-200 bg-gray-100">
                            <img class="w-16  bg-gray-100 object-cover object-center flex-shrink-0 rounded-full mr-4"
                                :src="ins.image">
                            <div class="flex-grow">
                                <h2 class="text-gray-900 title-font font-medium">{{ ins.name }}</h2>
                                <p class="text-gray-500">Rate : {{ ins.rate }} %</p>
                            </div>
                        </div>
                    </div>

                </div>
                <div v-else>
                    <p class="text-center text-gray-500">No insurance company supported yet</p>
                </div>
            </div>
        </section>
        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>
