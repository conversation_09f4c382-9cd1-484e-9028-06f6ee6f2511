<?php

namespace App\Http\Controllers;

use App\Data\NewExpenseData;
use App\Http\Requests\NewExpenseRequest;
use App\Services\CommonDataService;
use App\Services\ExpenseService;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
use App\Enums\AccountTypeEnums;

class ExpenseController extends Controller
{

    protected $user;
    public function __construct(
        protected ExpenseService $service,
        protected CommonDataService $commonDataService
    ) {
        $this->user = Auth::user();
    }

    public function index()
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $defaultBranch = branchData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $searchQuery = \request()->query('searchQuery', null);
        $expense = $this->pagination($this->service->expenses($defaultBranch->id, $perPage, $page, $searchQuery));
        return Inertia::render('Expense/Index', [
            'Expenses' => $expense['data'],
            'currentPage' => $expense['currentPage'],
            'lastPage' => $expense['lastPage'],
            'itemsPerPage' => $expense['itemsPerPage'],
            'pageItems' => $expense['pageItems'],
            'total' => $expense['total'],
            'searchQuery' => $searchQuery
        ]);
    }

    public function  new()
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $company = companyData();
        $expenseCategories = $this->service->expenseCategories($company->id);
        $paymentModes = $this->commonDataService->getPaymentMode($company->id);
        return Inertia::render('Expense/New', [
            'ExpenseCategories' => $expenseCategories,
            'PaymentMethods' => $paymentModes
        ]);
    }

    public function store(NewExpenseRequest $request)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $company = companyData();
        $defaultBranch = branchData();
        $user = $this->user;
        $data = NewExpenseData::from($request->validated());
        $this->service->storeExpense($data, $user->id, $defaultBranch->id, $company->id);
        return redirect()->route('expense.index')->banner('Expense Created Successfully');
    }

    public function show(int $expenseId)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $expense = $this->service->expenseById($expenseId, $branch->id);

        return Inertia::render('Expense/Show', [
            'Expense' => $expense
        ]);
    }

    public function edit(int $expenseId)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $company = companyData();
        $defaultBranch = branchData();
        $expense = $this->service->expenseById($expenseId, $defaultBranch->id);

        $expenseCategories = $this->service->expenseCategories($company->id);
        $paymentModes = $this->commonDataService->getPaymentMode($company->id);
        return Inertia::render('Expense/Edit', [
            'Expense' => $expense,
            'ExpenseCategories' => $expenseCategories,
            'PaymentMethods' => $paymentModes
        ]);
    }

    public function update(NewExpenseRequest $request, int $expenseId)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $company = companyData();
        $defaultBranch = branchData();
        $data = NewExpenseData::from($request->validated());
        $this->service->updateExpense($data, $expenseId, $defaultBranch->id, $company->id);
        return redirect()->route('expense.index')->banner('Expense Updated Successfully');
    }
}
