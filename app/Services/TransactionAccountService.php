<?php

namespace App\Services;

use App\Data\TransactionAccountData;
use App\Enums\TransactionAccountTypesEnum;
use App\Models\PaymentMode;
use App\Models\TransactionAccount;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;


class TransactionAccountService
{

    public function createTransaction(TransactionAccountData $data): void
    {

        TransactionAccount::create([
            'branch_id' => $data->branch_id,
            'payment_mode_id' => $data->payment_mode_id,
            'user_id' => $data->user_id,
            'amount' => $data->amount,
            'type' => $data->type,
            'sourceType' => $data->sourceType,
            'source_id' => $data->source_id,
            'date' => strtotime(Carbon::parse( now())->format('Y-m-d h:i:s')),
            'description' => $data->description
        ]);
    }

    public function account(int $branchId, int $companyId) : array
    {

        $accounts = PaymentMode::whereCompanyId($companyId)
            ->select('id', 'name', 'code', 'description', 'company_id')
            ->withCount([
                'transactions as credit_transactions_count' => fn($query) =>
                $query->where('branch_id', $branchId)
                    ->where('type', TransactionAccountTypesEnum::CREDIT->value),
                'transactions as debit_transactions_count' => fn($query) =>
                $query->where('branch_id', $branchId)
                    ->where('type', TransactionAccountTypesEnum::DEBIT->value)
            ])
            ->withSum([
                'transactions as total_credit_amount' => fn($query) =>
                $query->where('branch_id', $branchId)
                    ->where('type', TransactionAccountTypesEnum::CREDIT->value),
                'transactions as total_debit_amount' => fn($query) =>
                $query->where('branch_id', $branchId)
                    ->where('type', TransactionAccountTypesEnum::DEBIT->value)
            ], 'amount')
            ->with(['transactions' => fn($query) => $query->where('branch_id', $branchId)])
            ->get()
            ->map(fn($account) => $account->setAttribute('total_difference', $account->total_credit_amount - $account->total_debit_amount));

        return $accounts->map(fn($account) => [
            'name' => $account->name,
            'code' => $account->code,
            'description' => $account->description,
            'creditCount' => $account->credit_transactions_count ?? 0,
            'debitCount' => $account->debit_transactions_count ?? 0,
            'creditAmount' => $account->total_credit_amount ?? 0,
            'debitAmount' => $account->total_debit_amount ?? 0,
            'totalDifference' => $account->total_difference,
            'transactions' => $account->transactions
        ])->toArray();
    }

    public function paymentTransaction(int $branchId, int $companyId, int $perPage, int $page, $type = null, $startDate = null, $endDate = null): LengthAwarePaginator
    {

        return TransactionAccount::where('branch_id', $branchId)
            ->with(['paymentMode', 'user'])
            ->filterByDate($startDate, $endDate)
            ->whereTransactionType($type)
            ->latest()
            ->paginate(perPage: $perPage, page: $page)
            ->through(fn($transaction) => [
                'id' => $transaction->id,
                'paymentMode' => $transaction->paymentMode->name,
                'userName' => $transaction->user->name,
                'amount' => $transaction->amount,
                'type' => $transaction->type,
                'sourceType' => $transaction->sourceType,
                'sourceId' => $transaction->source_id,
                'description' => $transaction->description,
                'date' => $transaction->date,
            ]);
    }
}
