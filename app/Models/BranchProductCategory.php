<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BranchProductCategory extends Model
{
    /** @use HasFactory<\Database\Factories\BranchProductCategoryFactory> */
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'BranchProductCategory';

    protected $fillable = [
        'company_id',
        'branch_category_id',
        'name',
        'slug',
        'image',
        'isCommon',
    ];

    protected $casts = [
        'isCommon' => 'boolean',
    ];

    public function scopeWhereCompanyId($query, int $companyId)
    {
        return $query->where(function ($query) use ($companyId) {
            $query->where('company_id', $companyId)
                ->orWhereNull('company_id');
        });
    }
}
