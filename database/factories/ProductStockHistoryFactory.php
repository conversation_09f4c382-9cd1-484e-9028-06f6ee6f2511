<?php

namespace Database\Factories;

use App\Enums\TransactionTypeEnums;
use App\Models\CompanyBranch;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductStockHistory>
 */
class ProductStockHistoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_id' => Product::factory(),
            'branch_id' => CompanyBranch::factory(),
            'user_id' => User::factory(),
            'quantity' => $this->faker->randomFloat(2, 0, 1000),
            'oldQuantity' => $this->faker->randomFloat(2, 0, 1000),
            'orderType' => TransactionTypeEnums::SALES->value,
            'stockType' => $this->faker->randomElement(['in', 'out']),
            'description' => $this->faker->text,
        ];
    }
}
