<?php

namespace App\Models;

use App\Enums\ProductStockStatusEnums;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductDetail extends Model
{
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'ProductDetails';

    protected $fillable = [
        'product_id',
        'currentStock',
        'purchasePrice',
        'salesPrice',
        'stockQuantityAlert',
        'openingStock',
        'status',
        'expireDate',
        'batchNumber',
        'discountRate',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'expireDate' => 'datetime',
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function getExpireDateAttribute($value)
    {

        return $value ? \Carbon\Carbon::createFromTimestamp($value)->format('Y-m-d H:i:s') : null;
    }


    public function scopeWhereIsInStock($query)
    {
        return $query->where('currentStock', '>', 0)
           ->orWhere('status', ProductStockStatusEnums::NO_STOCK->value);
    }
}
