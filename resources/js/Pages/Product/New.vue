<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm } from "@inertiajs/vue3";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  RadioGroup,
  RadioGroupItem,
} from '@/components/ui/radio-group';

const props = defineProps({
  isEBM: Boolean,
  ProductCategories: Array,
  ProductClasses: Array,
  ProductQuantityUnits: Array,
  ProductPackagingUnits: Array,
  Taxes: Array,
  Countries: Array,
  BranchProductCategories: Array,
});

const form = useForm({
  'name': null,
  'quantityUnitID': 8,
  'categoryID': 1,
  'packagingUnitID': 6,
  'countryID': 178,
  'taxID': props.isEBM ? 1 : 5,
  'classID': 1,
  'typeID': 2,
  'branchProductCategoryID': '',
  'soldInSubUnit': '0',
  'conversionFactor': 2,
  'hasStock': 'yes',
});

function createProduct() {
  form.post(route('product.store'));
}

const options = [
  { value: '0', label: 'No' },
  { value: '1', label: 'Yes' },
];
const hasStockptions = [
  { value: 'no', label: 'No' },
  { value: 'yes', label: 'Yes' },
];
</script>

<template>
  <AppLayout title="New Product">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        New Product
      </h2>
    </template>

    <Card class="max-w-md mx-auto">
      <CardHeader>
        <CardTitle class="text-lg">Product Information</CardTitle>
      </CardHeader>
      <CardContent>
        <form @submit.prevent="createProduct" class="space-y-4">
          <div>
            <Label for="name">Name</Label>
            <Input id="name" v-model="form.name" type="text" />
            <p v-if="form.errors.name" class="text-red-500 text-sm mt-1">
              {{ form.errors.name }}
            </p>
          </div>

          <div>
            <Label>Quantity Unit</Label>
            <Select v-model="form.quantityUnitID">
              <SelectTrigger>
                <SelectValue placeholder="Select a unit" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="unit in ProductQuantityUnits" :key="unit.id" :value="unit.id">
                  {{ unit.name }}
                </SelectItem>
              </SelectContent>
            </Select>
            <p v-if="form.errors.quantityUnitID" class="text-red-500 text-sm mt-1">
              {{ form.errors.quantityUnitID }}
            </p>
          </div>

          <div v-if="isEBM">
            <Label>Country of origin</Label>
            <Select v-model="form.countryID">
              <SelectTrigger>
                <SelectValue placeholder="Select a country" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="country in Countries" :key="country.id" :value="country.id">
                  {{ country.name }}
                </SelectItem>
              </SelectContent>
            </Select>
            <p v-if="form.errors.countryID" class="text-red-500 text-sm mt-1">
              {{ form.errors.countryID }}
            </p>
          </div>

          <div v-if="isEBM">
            <Label>Tax</Label>
            <Select v-model="form.taxID">
              <SelectTrigger>
                <SelectValue placeholder="Select a tax" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="tax in Taxes" :key="tax.id" :value="tax.id">
                  {{ tax.name }}
                </SelectItem>
              </SelectContent>
            </Select>
            <p v-if="form.errors.taxID" class="text-red-500 text-sm mt-1">
              {{ form.errors.taxID }}
            </p>
          </div>

          <div>
            <Label>Product category</Label>
            <Select v-model="form.branchProductCategoryID">
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="category in BranchProductCategories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </SelectItem>
              </SelectContent>
            </Select>
            <p v-if="form.errors.branchProductCategoryID" class="text-red-500 text-sm mt-1">
              {{ form.errors.branchProductCategoryID }}
            </p>
          </div>

          <div>
            <Label>Has stock</Label>
            <RadioGroup v-model="form.hasStock" class="flex space-x-4 py-2">
              <div v-for="option in hasStockptions" :key="option.value" class="flex items-center space-x-2">
                <RadioGroupItem :value="option.value" :id="`option-${option.value}`" />
                <Label :for="`option-${option.value}`">{{ option.label }}</Label>
              </div>
            </RadioGroup>
            <p v-if="form.errors.hasStock" class="text-red-500 text-sm mt-1">
              {{ form.errors.hasStock }}
            </p>
          </div>

          <div class="hidden">
            <Label>Sold Sub Unit</Label>
            <RadioGroup v-model="form.soldInSubUnit" class="flex space-x-4">
              <div v-for="option in options" :key="option.value" class="flex items-center space-x-2">
                <RadioGroupItem :value="option.value" :id="`option-${option.value}`" />
                <Label :for="`option-${option.value}`">{{ option.label }}</Label>
              </div>
            </RadioGroup>
            <p v-if="form.errors.soldInSubUnit" class="text-red-500 text-sm mt-1">
              {{ form.errors.soldInSubUnit }}
            </p>
          </div>

          <div v-if="form.soldInSubUnit == '1'">
            <Label>Packaging unit</Label>
            <Select v-model="form.packagingUnitID">
              <SelectTrigger>
                <SelectValue placeholder="Select a unit" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="unit in ProductPackagingUnits" :key="unit.id" :value="unit.id">
                  {{ unit.name }}
                </SelectItem>
              </SelectContent>
            </Select>
            <p v-if="form.errors.packagingUnitID" class="text-red-500 text-sm mt-1">
              {{ form.errors.packagingUnitID }}
            </p>
          </div>

          <div v-if="form.soldInSubUnit == '1'">
            <Label for="conversionFactor">Conversion factor</Label>
            <Input id="conversionFactor" v-model="form.conversionFactor" type="text" required />
            <p v-if="form.errors.conversionFactor" class="text-red-500 text-sm mt-1">
              {{ form.errors.conversionFactor }}
            </p>
          </div>

          <div class="flex justify-end">
            <Button 
              type="submit" 
              :disabled="form.processing"
              :class="{ 'opacity-25': form.processing }"
              class="w-full"
            >
              Save
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>

    <template #sidenav />
    <template #footer />
  </AppLayout>
</template>