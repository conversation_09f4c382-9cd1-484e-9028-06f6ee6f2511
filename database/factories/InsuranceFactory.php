<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Insurance>
 */
class InsuranceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
        
            'company_id' => null,
            'name' => $this->faker->name,
            'code' => $this->faker->word,
            'rate' => $this->faker->randomNumber(2),
            'image' => $this->faker->imageUrl(),
            'isActive' => $this->faker->boolean,
        ];
    }
}
