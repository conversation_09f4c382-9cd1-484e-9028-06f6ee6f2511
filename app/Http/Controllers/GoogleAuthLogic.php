<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\AccountSubscriptionService;
use Lara<PERSON>\Socialite\Facades\Socialite as Socialite;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Exception;
use Illuminate\Support\Facades\DB;


class GoogleAuthLogic extends Controller
{

    public function __construct(protected AccountSubscriptionService $subscriptionService) {}
    public function redirect()
    {
        return Inertia::location(Socialite::driver('google')->redirect());
    }

    public function callbackgoogle()
    {

        $google_user = Socialite::driver('google')->stateless()->user();
        $user = DB::table('users')
            ->where('email', $google_user->getEmail())
            ->first();

        if (!$user) {
            DB::table('users')->insert([
                'name' => $google_user->getName(),
                'email' => $google_user->getEmail(),
                'google_id' => $google_user->getId(),
                'email_verified_at' => strtotime(date('Y-m-d H:i:s'))
            ]);
            $user = DB::table('users')
                ->where('email', $google_user->getEmail())
                ->first();
            $this->subscriptionService->storeSubscription($user->id);
            Auth::loginUsingId($user->id);
            return Inertia::location(config('app.url') . '/dashboard');
        } else {
            Auth::loginUsingId($user->id);
            return Inertia::location(config('app.url') . '/dashboard');
        }
    }
}
