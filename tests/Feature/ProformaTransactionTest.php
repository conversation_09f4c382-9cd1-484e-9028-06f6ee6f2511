<?php

use App\Models\Company;
use App\Models\CompanyBranch;
use App\Models\PaymentMode;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Models\Tax;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use App\Models\Role;
use App\Models\RolePermission;

beforeEach(function () {

    $this->user = User::factory()->create();

    $this->company = Company::factory()->create(["user_id" =>  $this->user->id, "isEBM" => true]);
    $this->branch = CompanyBranch::factory()->create(["company_id" => $this->company->id, 'isEBM' => true]);

    $this->permission = RolePermission::factory()->create();
    $this->role = Role::factory()->create([
        'user_id' => $this->user->id,
        'company_id' => $this->company->id,
        'role_permission_id' => $this->permission
    ]);

    $this->apiUrl = '/api/v1/transaction/proforma';

    $tax = [['code' => 'A', 'rate' => 0], ['code' => 'B', 'rate' => 18], ['code' => 'C', 'rate' => 0], ['code' => 'D', 'rate' => 0]];

    foreach ($tax as $item) {
        Tax::factory()->create([
            'code' => $item['code'],
            'rate' => $item['rate']
        ]);
    }

    Sanctum::actingAs($this->user);
});

it("can create proforma transaction ", function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $response = $this->postJson($this->apiUrl, $payload);

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Proforma transaction created successfully',
        ])->assertExactJsonStructure(['message', 'invoiceID']);
})->with([
    function () {

        $product = Product::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 100,
            'batchNumber' => '*********',
        ]);

        return [
            'paymentMethodID' => PaymentMode::factory()->create(
                [
                    'forEbm' => true
                ]
            )->id,
            'transactionDate' => now()->format('Y-m-d'),
            'note' => 'Note',
            'items' => [
                [
                    'productID' => $product->id,
                    'units' => 10,
                    'unitPrice' => 1000,
                    'discountRate' => 10,
                    'batchNumber' => '*********',
                    'movingUnit' => 'sub',
                ]
            ]

        ];
    }
]);

it("can throw errors during create sale transaction ", function (array $payload) {

    $this->withHeader('companyId', $this->company->id);

    $ebmData = $payload['ebm'];
    $response = $this->postJson($this->apiUrl, $ebmData);

    $response->assertStatus(404)
        ->assertJson([
            'message' => $payload['message'],
        ]);
})->with([

    function () {

        $this->withHeader('branchId', $this->branch->id);

        $product = Product::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        $stock =  ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 100,
            'batchNumber' => '*********',
        ]);

        return [
            'ebm' => [
                'paymentMethodID' => 90000,
                'transactionDate' => now()->format('Y-m-d'),
                'note' => 'Note',
                'items' => [
                    [
                        'productID' => $product->id,
                        'units' => 10,
                        'unitPrice' => 1000,
                        'discountRate' => 10,
                        'batchNumber' => $stock->batchNumber,
                        'movingUnit' => 'sub',
                    ]
                ]
            ],
            'message' => 'Payment method not found',
        ];
    },

    function () {

        $this->withHeader('branchId', $this->branch->id);

        $product = Product::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 100,
        ]);

        return [
            'ebm' => [
                'paymentMethodID' => PaymentMode::factory()->create([
                    'forEbm' => true
                ])->id,
                'transactionDate' => now()->format('Y-m-d'),
                'note' => 'Note',
                'items' => [
                    [
                        'productID' => 1212,
                        'units' => 10,
                        'unitPrice' => 1000,
                        'discountRate' => 10,
                        'batchNumber' => '*********',
                        'movingUnit' => 'sub',
                    ]
                ]
            ],
            'message' => 'Product not found',

        ];
    },

]);
