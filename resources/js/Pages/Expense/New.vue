<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from "@inertiajs/vue3";
import FormSection from '@/Components/FormSection.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import TextAreaInput from '@/Components/TextAreaInput.vue';
import SelectOption from '@/Components/SelectOption.vue';

const props = defineProps({
    ExpenseCategories: Object | Array,
    PaymentMethods: Object | Array,
});

const form = useForm({
    'amount': null,
    'categoryID': null,
    'paymentModeID': null,
    'description': null,
    'date': new Date().toISOString().split('T')[0],
});

function createNewExpense() {
    form.post(route('expense.store'), {

    });
}

</script>

<template>
    <AppLayout title="New Expense">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                New Expense
            </h2>
        </template>
        <FormSection>
            <template #title>
                Expense Information
            </template>

            <template #form>
                <form class="" @submit.prevent="createNewExpense">
                    <div class="pb-4">
                        <InputLabel for="amount" value="Amount" />
                        <TextInput id="amount" v-model="form.amount" type="text" />
                        <InputError class="mt-2" :message="form.errors.amount" />
                    </div>
                    <div class="grid grid-cols-6 gap-6">

                        <div class="col-span-6 sm:col-span-3">
                            <SelectOption id="categoryID" v-model="form.categoryID" :options="props.ExpenseCategories"
                                label="Category" valueKey="id" labelKey="name" />
                            <InputError class="mt-2" :message="form.errors.categoryID" />
                        </div>

                        <div class="col-span-6 sm:col-span-3">
                            <SelectOption id="paymentMethodID" v-model="form.paymentModeID"
                                :options="props.PaymentMethods" label="Payment Method" valueKey="id" labelKey="name" />
                            <InputError class="mt-2" :message="form.errors.paymentModeID" />
                        </div>

                    </div>

                    <div class="pb-4">
                        <InputLabel for="description" value="Description" />
                        <TextAreaInput id="description" v-model="form.description" />
                        <InputError class="mt-2" :message="form.errors.description" />
                    </div>

                    <div class="pb-4">
                        <InputLabel for="date" value="Date" />
                        <TextInput id="date" v-model="form.date" type="date" />
                        <InputError class="mt-2" :message="form.errors.date" />
                    </div>


                    <div class="flex justify-end mt-4">
                        <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                            Save
                        </PrimaryButton>
                    </div>
                </form>
            </template>
        </FormSection>
        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>