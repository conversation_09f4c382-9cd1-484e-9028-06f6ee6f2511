<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from "@inertiajs/vue3";
import FormSection from '@/Components/FormSection.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import RadioOptionGroup from '@/Components/RadioOptionGroup.vue';

const props = defineProps({
    Insurance: Array,
    Customer: Object,
});

const form = useForm({
    name: props.Customer.name,
    email: props.Customer.email,
    phone: props.Customer.phone,
    address:  props.Customer.address,
    tin:  props.Customer.tin,
    insuranceID: props.Customer.patientDetails ? props.Customer.patientDetails.insuranceID : '',
    hasInsurance: props.Customer.hasInsurance ? 1 : 0,
    hasAffiliation: props.Customer.patientDetails ? ( props.Customer.patientDetails.hasAffiliation ? 1:0) : 0,
    gender: props.Customer.patientDetails ? props.Customer.patientDetails.gender : '',
    code: props.Customer.patientDetails ? props.Customer.patientDetails.code : '',
    percentage: props.Customer.patientDetails ? props.Customer.patientDetails.percentage : '',
    expirationDate: props.Customer.patientDetails ? props.Customer.patientDetails.expirationDate : '',
    affiliationNumber: props.Customer.patientDetails ? props.Customer.patientDetails.affiliationNumber : '',
    affiliateFirstName: props.Customer.patientDetails ? props.Customer.patientDetails.affiliateFirstName : '',
    affiliateLastName: props.Customer.patientDetails ? props.Customer.patientDetails.affiliateLastName : '',
    relationship: props.Customer.patientDetails ? props.Customer.patientDetails.relationship : '',
    beneficiaryFirstName: props.Customer.patientDetails ? props.Customer.patientDetails.beneficiaryFirstName : '',
    beneficiaryLastName: props.Customer.patientDetails ? props.Customer.patientDetails.beneficiaryLastName : '',
    beneficiaryNumber: props.Customer.patientDetails ? props.Customer.patientDetails.beneficiaryNumber: '',
    dateOfBirth: props.Customer.patientDetails ? props.Customer.patientDetails.dateOfBirth : '',
    department: props.Customer.patientDetails ? props.Customer.patientDetails.department : '',
    status: props.Customer.patientDetails ? props.Customer.patientDetails.status : '',
});

function createNewParty() {
    form.put(route('customer.update', props.Customer.id), {
    });
}

const options = [
    { value: 0, label: 'No' },
    { value: 1, label: 'Yes' },
];

const insuranceOptions = props.Insurance.map(insurance => ({
    value: insurance.id,
    label: insurance.name,
}));

const statusOptions = [
    { value: 'Activated', label: 'Activated' },
    { value: 'Deactivated', label: 'Deactivated' },
];

const genderOptions = [
    { value : 'Male', label : 'Male' },
    { value : 'Female', label : 'Female' },
];

</script>

<template>
    <AppLayout title="New  Customer">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                New Customer
            </h2>
        </template>

        <FormSection>
            <template #title>
                Customer Information
            </template>

            <template #form>
                <form class="" @submit.prevent="createNewParty">
                    <div class="pb-4">
                        <InputLabel for="name" value="Name" />
                        <TextInput id="name" v-model="form.name" type="text" />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>
                    <div class="grid grid-cols-6 gap-2 py-5">
                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="email" value="Email" />
                            <TextInput id="email" v-model="form.email" type="email" />
                            <InputError class="mt-2" :message="form.errors.email" />
                        </div>

                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="phone" value="Phone" />
                            <TextInput id="phone" v-model="form.phone" type="text" />
                            <InputError class="mt-2" :message="form.errors.phone" />
                        </div>
                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="address" value="Address" />
                            <TextInput id="address" v-model="form.address" type="text" />
                            <InputError class="mt-2" :message="form.errors.address" />
                        </div>

                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="tin" value="Tin" />
                            <TextInput id="tin" v-model="form.tin" type="text" />
                            <InputError class="mt-2" :message="form.errors.tin" />
                        </div>
                    </div>
                    <div v-if="form.hasInsurance">
                        <div class="py-1">
                            <RadioOptionGroup v-model="form.insuranceID" :options="insuranceOptions" name="insuranceID"
                                label="Insurance" />
                            <InputError class="mt-2" :message="form.errors.insuranceID" />
                        </div>
                            <div class="grid grid-cols-6 gap-2 py-5">
                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="code" value="Code" />
                            <TextInput id="code" v-model="form.code" type="text" />
                            <InputError class="mt-2" :message="form.errors.code" />
                        </div>
                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="percentage" value="Percentage" />
                            <TextInput id="percentage" v-model="form.percentage" type="text" />
                            <InputError class="mt-2" :message="form.errors.percentage" />
                        </div>
                        </div>
                        <div class="py-1">
                            <InputLabel for="expirationDate" value="Expiration Date" />
                            <TextInput id="expirationDate" v-model="form.expirationDate" type="date" />
                            <InputError class="mt-2" :message="form.errors.expirationDate" />
                        </div>
                        <div class="py-1">
                            <RadioOptionGroup v-model="form.gender" :options="genderOptions" name="gender"
                            label="Gender"  />
                            <InputError class="mt-2" :message="form.errors.gender" />
                            </div>

                        <div class="py-1">
                            <RadioOptionGroup v-model="form.hasAffiliation" :options="options" name="hasAffiliation"
                                label="Has affiliate" />
                            <InputError class="mt-2" :message="form.errors.hasInsurance" />
                        </div>

                        <div class="grid grid-cols-6 gap-2 py-5" v-if="form.hasAffiliation">
                            <div class="col-span-6 sm:col-span-3">
                                <InputLabel for="affiliationNumber" value="Affilation Number" />
                                <TextInput id="affiliationNumber" v-model="form.affiliationNumber" type="text" />
                                <InputError class="mt-2" :message="form.errors.affiliationNumber" />
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <InputLabel for="affiliateFirstName" value="Affilation First Name" />
                                <TextInput id="affiliateFirstName" v-model="form.affiliateFirstName" type="text" />
                                <InputError class="mt-2" :message="form.errors.affiliateFirstName" />
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <InputLabel for="affiliateLastName" value="Affilation Last Name" />
                                <TextInput id="affiliateLastName" v-model="form.affiliateLastName" type="text" />
                                <InputError class="mt-2" :message="form.errors.affiliateLastName" />
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <InputLabel for="relationship" value="Relationship" />
                                <TextInput id="relationship" v-model="form.relationship" type="text" />
                                <InputError class="mt-2" :message="form.errors.relationship" />
                            </div>
                        </div>

                        <div class="grid grid-cols-6 gap-6 py-5">
                            <div class="col-span-6 sm:col-span-3">
                                <InputLabel for="BeneficiaryFirstName" value="Beneficiary First Name" />
                                <TextInput id="BeneficiaryFirstName" v-model="form.beneficiaryFirstName" type="text" />
                                <InputError class="mt-2" :message="form.errors.beneficiaryFirstName" />
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <InputLabel for="BeneficiaryLastName" value="Beneficiary Last Name" />
                                <TextInput id="BeneficiaryLastName" v-model="form.beneficiaryLastName" type="text" />
                                <InputError class="mt-2" :message="form.errors.beneficiaryLastName" />
                            </div>
                        </div>
                        <div class="py-1">
                            <InputLabel for="beneficiaryNumber" value="Beneficiary number" />
                            <TextInput id="beneficiaryNumber" v-model="form.beneficiaryNumber" type="text" />
                            <InputError class="mt-2" :message="form.errors.beneficiaryNumber" />
                        </div>
                        <div class="grid grid-cols-6 gap-6 py-5">
                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="DateOfBirth" value="Date Of Birth" />
                            <TextInput id="DateOfBirth" v-model="form.dateOfBirth" type="date" />
                            <InputError class="mt-2" :message="form.errors.dateOfBirth" />
                        </div>
                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="department" value="Department" />
                            <TextInput id="department" v-model="form.department" type="text" />
                            <InputError class="mt-2" :message="form.errors.department" />
                        </div>
                        </div>

                        <div class="py-1">
                            <InputLabel for="status" value="Status" />
                            <RadioOptionGroup v-model="form.status" :options="statusOptions" name="status" />
                            <InputError class="mt-2" :message="form.errors.status" />
                        </div>
                    </div>
                    <div class="flex justify-end mt-4">
                        <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                            Update
                        </PrimaryButton>
                    </div>
                    <div class="pb-12">

                    </div>
                </form>
            </template>
        </FormSection>
        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>
