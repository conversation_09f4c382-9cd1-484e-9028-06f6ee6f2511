<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from "@inertiajs/vue3";
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';

const props = defineProps({
    Branch: Object,
    CompanyId: Number,
});

const form = useForm({
    name: props.Branch.name,
    email: props.Branch.email,
    phone: props.Branch.phone,
    address: props.Branch.address,
    topMessage: props.Branch.topMessage,
    bottomMessage: props.Branch.bottomMessage,
    mrc: props.Branch.mrc,
    branchCode: props.Branch.branchCode,
    mode: props.Branch.mode,
    deviceSerial: props.Branch.deviceSerial,
    cluster: props.Branch.cluster,
    clusterName: props.Branch.clusterName,
    invoiceNumber: props.Branch.invoice_number?.last_number ?? 0,
    isInitialized: props.Branch.isInitialised
});

function createNewCompanyBranch() {
    form.put(route('admin.branch.update', [props.CompanyId, props.Branch.id]));
}
</script>

<template>
    <AppLayout title="Edit Branch">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Edit Branch
            </h2>
        </template>

        <div class="py-12">
            <Card class="max-w-3xl mx-auto">
                <CardHeader>
                    <CardTitle class="text-lg">Branch Information</CardTitle>
                </CardHeader>
                <CardContent>
                    <form @submit.prevent="createNewCompanyBranch" class="space-y-6">
                        <div v-if="$page.props.errors && Object.keys($page.props.errors).length > 0" class="space-y-2">
                            <p class="font-bold text-red-600">Please fix the following errors:</p>
                            <ul class="text-red-600 text-sm">
                                <li v-for="(error, key) in $page.props.errors" :key="key">
                                    {{ error }}
                                </li>
                            </ul>
                        </div>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <div class="space-y-2">
                                <Label for="name">Name</Label>
                                <Input id="name" v-model="form.name" type="text" />
                                <p v-if="form.errors.name" class="text-red-500 text-sm">
                                    {{ form.errors.name }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="email">Email</Label>
                                <Input id="email" v-model="form.email" type="email" />
                                <p v-if="form.errors.email" class="text-red-500 text-sm">
                                    {{ form.errors.email }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="phone">Phone</Label>
                                <Input id="phone" v-model="form.phone" type="text" />
                                <p v-if="form.errors.phone" class="text-red-500 text-sm">
                                    {{ form.errors.phone }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="address">Address</Label>
                                <Input id="address" v-model="form.address" type="text" />
                                <p v-if="form.errors.address" class="text-red-500 text-sm">
                                    {{ form.errors.address }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="topMessage">Top Message</Label>
                                <Input id="topMessage" v-model="form.topMessage" type="text" />
                                <p v-if="form.errors.topMessage" class="text-red-500 text-sm">
                                    {{ form.errors.topMessage }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="bottomMessage">Bottom Message</Label>
                                <Input id="bottomMessage" v-model="form.bottomMessage" type="text" />
                                <p v-if="form.errors.bottomMessage" class="text-red-500 text-sm">
                                    {{ form.errors.bottomMessage }}
                                </p>
                            </div>

                        </div>
                        <p class="text-lg font-semibold mb-2 pt-16">
                            EBM Configuration
                        </p>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <div class="space-y-2">
                                <Label for="deviceSerial">Device Serial</Label>
                                <Input id="deviceSerial" v-model="form.deviceSerial" type="text" />
                                <p v-if="form.errors.deviceSerial" class="text-red-500 text-sm">
                                    {{ form.errors.deviceSerial }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="mrc">MRC</Label>
                                <Input id="mrc" v-model="form.mrc" type="text" />
                                <p v-if="form.errors.mrc" class="text-red-500 text-sm">
                                    {{ form.errors.mrc }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="branchCode">Branch Code</Label>
                                <Input id="branchCode" v-model="form.branchCode" type="text" />
                                <p v-if="form.errors.branchCode" class="text-red-500 text-sm">
                                    {{ form.errors.branchCode }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="mode">Mode</Label>
                                <Select v-model="form.mode">
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select mode" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="T">Training</SelectItem>
                                        <SelectItem value="N">Normal</SelectItem>
                                    </SelectContent>
                                </Select>
                                <p v-if="form.errors.mode" class="text-red-500 text-sm">
                                    {{ form.errors.mode }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="cluster">Cluster</Label>
                                <Input id="cluster" v-model="form.cluster" type="text" />
                                <p v-if="form.errors.cluster" class="text-red-500 text-sm">
                                    {{ form.errors.cluster }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="clusterName">Cluster Name</Label>
                                <Input id="clusterName" v-model="form.clusterName" type="text" />
                                <p v-if="form.errors.clusterName" class="text-red-500 text-sm">
                                    {{ form.errors.clusterName }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="invoiceNumber">Invoice Number</Label>
                                <Input id="invoiceNumber" v-model="form.invoiceNumber" type="text" />
                                <p v-if="form.errors.invoiceNumber" class="text-red-500 text-sm">
                                    {{ form.errors.invoiceNumber }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="isInitialized">EBM Status</Label>
                                <div v-if="form.isInitialized" class="flex items-center space-x-2">
                                    <p class="text-green-600 bg-gray-50 p-2 rounded w-full">
                                        Branch is initialized
                                    </p>
                                </div>
                                <div v-else class="flex items-center space-x-2">
                                    <p class="text-red-600 bg-gray-200 p-2 rounded w-full">
                                        Branch is not initialized
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <Button type="submit" :disabled="form.processing"
                                :class="{ 'opacity-25': form.processing }">
                                Save
                            </Button>
                        </div>
                    </form>


                </CardContent>
            </Card>



            <div class="flex justify-center py-12 gap-6">
                <Link :href="route('admin.branch.initialize', Branch.id)"
                    class="text-green-600 hover:text-green-900 underline">
                Auto Initialize
                </Link>

                <Link :href="route('admin.branch.initialize.auto', Branch.id)"
                    class="text-green-600 hover:text-green-900 underline">
               Manual Initialize
                </Link>

            </div>
        </div>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>