<script setup>
import PrimaryLink from '@/Components/PrimaryLink.vue';
import SecondaryLink from '@/Components/SecondaryLink.vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref, watch, reactive } from 'vue';
import { useForm, Link } from "@inertiajs/vue3";
import DialogModal from '@/Components/DialogModal.vue';
import RadioOptionGroup from '@/Components/RadioOptionGroup.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';

const props = defineProps({
    Products: Array,
    currentPage: Number,
    lastPage: Number,
    itemsPerPage: Number,
    pageItems: Number,
    total: Number,
    Taxes: Array,
});

const searchQuery = ref('');
const confirmProductSync = ref(false);

const state = reactive({
    selectedItemsPerPage: props.itemsPerPage,
    jumpToPage: props.currentPage,
});

const paginationForm = useForm({
    page: props.currentPage,
    perPage: props.itemsPerPage,
    searchQuery: '',
    productBranchCategoryID: '',
});

const updateItemsPerPage = () => {
    paginationForm.perPage = state.selectedItemsPerPage;
    paginationForm.page = 1;
    paginationForm.get(route('product.sync'), { preserveState: true, preserveScroll: true });
};

const prevPage = () => {
    if (props.currentPage > 1) {
        paginationForm.page = props.currentPage - 1;
        paginationForm.get(route('product.sync'), { preserveState: true, preserveScroll: true });
    }
};

const nextPage = () => {
    if (props.currentPage < props.lastPage) {
        paginationForm.page = props.currentPage + 1;
        paginationForm.get(route('product.sync'), { preserveState: true, preserveScroll: true });
    }
};

watch(searchQuery, (newQuery) => {
    paginationForm.searchQuery = newQuery;
    paginationForm.page = 1;
    paginationForm.get(route('product.sync'), { preserveState: true, preserveScroll: true });
});

const form = useForm({
    taxID: '',
    productID: '',
    processing: false,
});

const confirmProductSyncModel = (product) => {
    form.productID = product.id;
    confirmProductSync.value = true;
};

const closeModal = () => {
    confirmProductSync.value = false;
    form.reset();
};

const taxOptions = props.Taxes.map((tax) => {
    return {
        value: tax.id,
        label: tax.name,
        description: tax.description,
    };
});

const syncProduct = () => {
    form.post(route('product.confirmSync', form.productID), {
        preserveScroll: true,
        onSuccess: () => {
            confirmProductSync.value = false;
            form.reset();
        },
    }
    );

};
</script>

<template>
    <AppLayout title="Sync Products">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Sync Products
            </h2>
        </template>


        <section>

            <div>
                <p class="text-gray-500 text-center p-4 text-lg font-bold">
                    Sync products to your stock
                </p>
            </div>
            <div>
                <input type="text" v-model="searchQuery" placeholder="Search products..."
                    class="mb-4 p-2 border border-gray-300 rounded w-1/2" />
            </div>
            <div>
                <div v-if="Products.length > 0"
                    class="overflow-x-auto flex flex-col divide-y divide-gray-200 rounded-lg border border-gray-300"
                    style="min-height: 500px;">
                    <div class="flex-grow">
                        <table class="min-w-full divide-y divide-gray-200 table-auto">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="px-6 py-3 text-left text-sm font-semibold text-gray-600  tracking-wider">
                                        ID
                                    </th>
                                    <th class="px-6 py-3 text-left text-sm font-semibold text-gray-600  tracking-wider">
                                        Image
                                    </th>
                                    <th class="px-6 py-3 text-left text-sm font-semibold text-gray-600  tracking-wider">
                                        Drug code
                                    </th>
                                    <th class="px-6 py-3 text-left text-sm font-semibold text-gray-600  tracking-wider">
                                        Generic description
                                    </th>
                                    <th class="px-6 py-3 text-left text-sm font-semibold text-gray-600  tracking-wider">
                                        Designation
                                    </th>
                                    <th class="px-6 py-3 text-left text-sm font-semibold text-gray-600  tracking-wider">
                                        Instruction
                                    </th>
                                    <th class="px-6 py-3 text-left text-sm font-semibold text-gray-600  tracking-wider">
                                        Unit
                                    </th>
                                    <th class="px-6 py-3 text-left text-sm font-semibold text-gray-600  tracking-wider">
                                        Price
                                    </th>
                                    <th class="px-6 py-3 text-left text-sm font-semibold text-gray-600  tracking-wider">

                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">

                                <tr v-for="Product in Products" :key="Product.id" class="hover:bg-gray-100">

                                    <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                        {{ Product.id }}
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                        <img v-if="typeof Product.image === 'string' && (Product.image.startsWith('http') || /\.(jpg|jpeg|png|gif)$/i.test(Product.image))"
                                            :src="Product.image" alt="" class="w-10 object-cover rounded-full" />
                                        <span v-else>{{ Product.image !== null && Product.image !== undefined ?
                                            Product.image :
                                            '-' }}</span>
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                        {{ Product.itemCode }}
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                        {{ Product.name }}
                                    </td>
                                    <td class="px-6 py-4 text-xs font-medium text-gray-900">
                                        {{ Product.description }}
                                    </td>
                                    <td class="px-6 py-4 text-xs font-medium text-gray-900">
                                        {{ Product.description_two }}
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                        {{ Product.quantityUnit }}
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                        {{ Product.price }}
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                        <button @click="confirmProductSyncModel(Product)"
                                            class="underline text-zata-primary-dark hover:text-zata-primary-light">
                                            select
                                        </button>
                                    </td>

                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
                <div v-else class="text-gray-500 text-center p-32">No products found</div>
            </div>
            <div class="mt-auto flex justify-between items-center pt-4 ">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium">{{ (currentPage - 1) * itemsPerPage + 1 }}</span>
                        to
                        <span class="font-medium">{{ Math.min(currentPage * itemsPerPage, total) }}</span>
                        of
                        <span class="font-medium">{{ total }}</span>
                        results
                    </p>
                </div>
                <div class="flex items-center">
                    <label for="itemsPerPage" class="text-sm font-medium text-gray-700">Items per
                        page:</label>
                    <select id="itemsPerPage" v-model="state.selectedItemsPerPage" @change="updateItemsPerPage"
                        class="ml-2 border-gray-300 rounded">
                        <option :value="5">5</option>
                        <option :value="10">10</option>
                        <option :value="20">20</option>
                        <option :value="50">50</option>
                        <option :value="100">100</option>
                    </select>
                </div>
                <div class="flex space-x-2">
                    <button @click="prevPage" :disabled="currentPage === 1"
                        class="px-4 py-2 bg-white rounded hover:bg-gray-300 disabled:opacity-70 border border-gray-300">
                        Previous
                    </button>

                    <button @click="nextPage" :disabled="currentPage === lastPage"
                        class="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50">
                        Next
                    </button>
                </div>
            </div>
        </section>
        <template #footer />

        <DialogModal :show="confirmProductSync" @close="closeModal">
            <template #title>
                Confirm Sync
            </template>

            <template #content>
                <form @submit.prevent="syncProduct">
                    <div class="pb-4">
                        <RadioOptionGroup v-model="form.taxID" :options="taxOptions" name="taxID" label="Assign tax" />
                        <InputError class="mt-2" :message="form.errors.taxID" />
                    </div>

                    <div class="flex justify-end mt-4">
                        <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                            Sync
                        </PrimaryButton>
                    </div>
                </form>

            </template>
        </DialogModal>
        <template #sidenav />
    </AppLayout>
</template>
