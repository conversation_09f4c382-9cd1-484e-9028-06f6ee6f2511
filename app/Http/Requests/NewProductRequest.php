<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class NewProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|min:3|max:100',
            'quantityUnitID' => 'required|numeric|min:1',
            'packagingUnitID' => 'required|numeric|min:1',
            'countryID' => 'required|numeric|min:1',
            'taxID' => 'required|numeric|min:1',
            'branchProductCategoryID' => 'required|numeric|min:1',
            'soldInSubUnit' => 'nullable|boolean',
            'conversionFactor' => 'required_if:soldInSubUnit,true|numeric|min:2',
            'hasStock' => 'required|string|in:yes,no',
        ];
    }

    public function messages()
    {
        return [

            'TaxID.required' => 'Tax is required',
            'quantityUnitID.required' => 'Quantity unit is required',
            'packagingUnitID.required' => 'Packaging unit is required',
            'countryID.required' => 'Country of origin is required',
            'branchProductCategoryID.required' => 'Branch Product Category is required',
            'name.required' => 'Product name is required',
        ];
    }
}
