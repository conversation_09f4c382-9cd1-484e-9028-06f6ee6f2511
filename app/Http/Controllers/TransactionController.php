<?php

namespace App\Http\Controllers;

use App\Data\PurchaseData;
use App\Data\TransactionPosData;
use App\Data\TransactionRefundData;
use App\Enums\PartyTypeEnums;
use App\Enums\TransactionTypeEnums;
use App\Http\Requests\PurchaseRequest;
use App\Services\CommonDataService;
use App\Services\CompanyPartyService;
use App\Services\ProductService;
use App\Services\TransactionService;
use App\Http\Requests\TransactionPosRequest;
use App\Http\Requests\TransactionRefundRequest;
use App\Services\PdfService;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Services\UserService;
use App\Enums\AccountTypeEnums;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class TransactionController extends Controller
{

    protected $user;
    public function __construct(
        protected TransactionService $service,
        protected ProductService $productService,
        protected CommonDataService $commonDataService,
        protected CompanyPartyService $companyPartyService,
        protected PdfService $pdfService,
        protected UserService $userService,
    ) {

        $this->user = Auth::user();
    }

    public function sale()
    {

        $defaultBranch = branchData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $userID = \request()->query('userID', null);
        $customerID = \request()->query('customerID', null);

        $fromDate = \request()->query('fromDate', null);
        $toDate = \request()->query('toDate', null);
        $transactionType = TransactionTypeEnums::SALES->value;
        $customers = $this->companyPartyService->parties($defaultBranch->company_id, PartyTypeEnums::CUSTOMER->value);
        $cashiers = $this->userService->getCompanyUsers($defaultBranch->id);
        $transaction = $this->pagination($this->service->getTransaction(
            $defaultBranch,
            $transactionType,
            $perPage,
            $page,
            $userID,
            $customerID,
            $fromDate,
            $toDate
        ));
        return Inertia::render('Transaction/ListSale', [
            'Transactions' => $transaction['data'],
            'currentPage' => $transaction['currentPage'],
            'lastPage' => $transaction['lastPage'],
            'itemsPerPage' => $transaction['itemsPerPage'],
            'pageItems' => $transaction['pageItems'],
            'total' => $transaction['total'],
            'Customers' =>  $customers,
            'Cashiers' => $cashiers,
            'userID' => $userID,
            'customerID' => $customerID,
            'fromDate' => $fromDate,
            'toDate' => $toDate
        ]);
    }

    public function proforma()
    {
        $defaultBranch = branchData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $transactionType = TransactionTypeEnums::PROFORMA->value;
        $transaction = $this->pagination($this->service->getTransaction($defaultBranch, $transactionType, $perPage, $page));

        return Inertia::render('Transaction/ListProforma', [
            'Transactions' => $transaction['data'],
            'currentPage' => $transaction['currentPage'],
            'lastPage' => $transaction['lastPage'],
            'itemsPerPage' => $transaction['itemsPerPage'],
            'pageItems' => $transaction['pageItems'],
            'total' => $transaction['total']
        ]);
    }

    public function createProforma()
    {
        $defaultBranch = branchData();
        $company = companyData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $searchQuery = \request()->query('searchQuery', null);
        $productBranchCategoryID = \request()->query('productBranchCategoryID', null);
        $products = $this->pagination($this->productService->getPosProducts($defaultBranch->id, $perPage, $page, $searchQuery, $productBranchCategoryID));
        $paymentModes  = $this->commonDataService->getPaymentMode($company->id, $defaultBranch->isEBM);
        $customers = $this->companyPartyService->parties($company->id, PartyTypeEnums::CUSTOMER->value);
        $branchProductCategories = $this->commonDataService->getBranchProductCategory($defaultBranch);
        return Inertia::render('Transaction/PosProforma', [
            'Products' => $products['data'],
            'PaymentMethods' => $paymentModes,
            'Customers' => $customers,
            'currentPage' => $products['currentPage'],
            'lastPage' => $products['lastPage'],
            'itemsPerPage' => $products['itemsPerPage'],
            'pageItems' => $products['pageItems'],
            'total' => $products['total'],
            'isEBM' => $defaultBranch->isEBM,
            'BranchProductCategories' => $branchProductCategories
        ]);
    }

    public function refund()
    {
        $defaultBranch = branchData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $transactionType = TransactionTypeEnums::SALES_RETURN->value;
        $transaction = $this->pagination($this->service->getTransaction($defaultBranch, $transactionType, $perPage, $page));
        return Inertia::render('Transaction/ListRefund', [
            'Transactions' => $transaction['data'],
            'currentPage' => $transaction['currentPage'],
            'lastPage' => $transaction['lastPage'],
            'itemsPerPage' => $transaction['itemsPerPage'],
            'pageItems' => $transaction['pageItems'],
            'total' => $transaction['total']
        ]);
    }

    public function refundSale(int $id)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $defaultBranch = branchData();
        $transaction = $this->service->getRefundTransaction($id, $defaultBranch);
        return Inertia::render('Transaction/Refund', [
            'Transaction' => $transaction,
            'isEBM' => $defaultBranch->isEBM
        ]);
    }

    public function storeRefund(TransactionRefundRequest $request, int $id)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $company = companyData();
        $defaultBranch = branchData();
        $user = $this->user;
        $data = TransactionRefundData::from($request->validated());

        $this->service->storeRefund($id, $data, $defaultBranch, $company, $user);

        return redirect()->route('transaction.sale')->banner('Transaction created successfully.');
    }



    public function pos()
    {

        branchData();
        return Inertia::render('Transaction/Pos');
    }

    public function posJson(bool $clearCart = false)
    {

        $defaultBranch = branchData();
        $company = companyData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $searchQuery = \request()->query('searchQuery', null);
        $productBranchCategoryID = \request()->query('productBranchCategoryID', null);
        $products = $this->pagination($this->productService->getPosProducts($defaultBranch->id, $perPage, $page, $searchQuery, $productBranchCategoryID));
        $paymentModes  = $this->commonDataService->getPaymentMode($company->id, $defaultBranch->isEBM);
        $customers = $this->companyPartyService->parties($company->id, PartyTypeEnums::CUSTOMER->value);
        $branchProductCategories = $this->commonDataService->getBranchProductCategory($defaultBranch);

        return [
            'Products' => $products['data'],
            'PaymentMethods' => $paymentModes,
            'Customers' => $customers,
            'currentPage' => $products['currentPage'],
            'lastPage' => $products['lastPage'],
            'itemsPerPage' => $products['itemsPerPage'],
            'pageItems' => $products['pageItems'],
            'total' => $products['total'],
            'isEBM' => $defaultBranch->isEBM,
            'BranchProductCategories' => $branchProductCategories,
            'ClearCart' => $clearCart
        ];
    }

    public function createRefund(bool $clearCart = false)
    {

        $defaultBranch = branchData();
        $company = companyData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $searchQuery = \request()->query('searchQuery', null);
        $productBranchCategoryID = \request()->query('productBranchCategoryID', null);
        $products = $this->pagination($this->productService->getPosProducts($defaultBranch->id, $perPage, $page, $searchQuery, $productBranchCategoryID));
        $paymentModes  = $this->commonDataService->getPaymentMode($company->id, $defaultBranch->isEBM);
        $customers = $this->companyPartyService->parties($company->id, PartyTypeEnums::CUSTOMER->value);
        $branchProductCategories = $this->commonDataService->getBranchProductCategory($defaultBranch);
        return Inertia::render('Transaction/ManualRefund', [
            'Products' => $products['data'],
            'PaymentMethods' => $paymentModes,
            'Customers' => $customers,
            'currentPage' => $products['currentPage'],
            'lastPage' => $products['lastPage'],
            'itemsPerPage' => $products['itemsPerPage'],
            'pageItems' => $products['pageItems'],
            'total' => $products['total'],
            'isEBM' => $defaultBranch->isEBM,
            'BranchProductCategories' => $branchProductCategories,
            'ClearCart' => $clearCart
        ]);
    }

    public function storePos(TransactionPosRequest $request)
    {

        $company = companyData();
        $defaultBranch = branchData();
        $user = $this->user;
        $data = TransactionPosData::from($request->validated());

        Log::info('reach controller bt time ' . now()->toDateTimeString());
        $this->service->storeSale($data, $defaultBranch, $company, $user);

        return redirect()->route('pos', ['clearCart' => true])->banner('Transaction created successfully.');
    }

    public function storeProforma(TransactionPosRequest $request)
    {
        $company = companyData();
        $defaultBranch = branchData();
        $user = $this->user;
        $data = TransactionPosData::from($request->validated());
        $this->service->storeProforma($data, $defaultBranch, $company, $user);

        return redirect()->route('transaction.proforma.create')->banner('Transaction created successfully.');
    }

    public function storeManualRefund(TransactionPosRequest $request)
    {

        // $deviceCacheKey = 'device:' . request()->ip();
        // $device = Cache::get($deviceCacheKey);
        // if ($device !== request()->ip()) {
        //     return Inertia::render('Error/404');
        // }

        $company = companyData();
        $defaultBranch = branchData();
        $data = TransactionPosData::from($request->validated());

        $this->service->storeManualRefund($data, $defaultBranch, $company, $this->user);

        return redirect()->route('transaction.refund.create')->banner('Transaction created successfully.');
    }

    public function convertProformaToSale(int $id)
    {

        $defaultBranch = branchData();
        $transaction = $this->service->getTransactionById($id, $defaultBranch->id);
        return Inertia::render('Transaction/ConvertProformaToSale', [
            'Transaction' => $transaction,
            'isEBM' => $defaultBranch->isEBM
        ]);
    }

    public function convertProformaToSaleStore(TransactionRefundRequest $request, int $id)
    {

        $company = companyData();
        $defaultBranch = branchData();
        $user = $this->user;
        $data = TransactionRefundData::from($request->validated());

        $this->service->convertProformaToSale($id, $data, $defaultBranch, $company, $user);

        return redirect()->route('transaction.proforma')->banner('Transaction created successfully.');
    }

    public function calculatePos(TransactionPosRequest $request)
    {

        $company = companyData();
        $defaultBranch = branchData();
        $data = TransactionPosData::from($request->validated());
        $transaction = $this->service->calculatePos($data, $defaultBranch, $company);

        return response()->json($transaction);
    }

    public function show(int $id, bool $copy = false)
    {

        $company = companyData();
        $defaultBranch = branchData();
        $transaction = $this->service->showTransaction($id, $defaultBranch);
        return Inertia::render('Transaction/Show', [
            'Transaction' => $transaction,
            'Company' => [
                'name' => $company->name,
                'address' => $company->address,
                'phone' => $company->phone,
                'email' => $company->email,
                'tin' => $company->tin,
                'mrc' => $company->mrc,
            ],
            'Branch' => [
                'topMessage' => $defaultBranch->topMessage,
                'bottomMessage' => $defaultBranch->bottomMessage,
                'mrc' => $defaultBranch->mrc
            ],
            'Copy' => $copy
        ]);
    }


    public function showSmall(int $id, bool $copy = false)
    {

        $company = companyData();
        $defaultBranch = branchData();
        $transaction = $this->service->showTransaction($id, $defaultBranch);
        return Inertia::render('Transaction/ShowSmall', [
            'Transaction' => $transaction,
            'Company' => [
                'name' => $company->name,
                'address' => $company->address,
                'phone' => $company->phone,
                'email' => $company->email,
                'tin' => $company->tin,
                'mrc' => $company->mrc,
            ],
            'Branch' => [
                'topMessage' => $defaultBranch->topMessage,
                'bottomMessage' => $defaultBranch->bottomMessage,
                'mrc' => $defaultBranch->mrc
            ],
            'Copy' => $copy
        ]);
    }

    public function purchase()
    {

        $defaultBranch = branchData();
        $perPage = \request()->query('perPage', 11);
        $page = \request()->query('page', 1);
        $transactionType = TransactionTypeEnums::PURCHASE->value;
        $transaction = $this->pagination($this->service->getTransaction($defaultBranch, $transactionType, $perPage, $page));

        return Inertia::render('Transaction/ListPurchase', [
            'Transactions' => $transaction['data'],
            'currentPage' => $transaction['currentPage'],
            'lastPage' => $transaction['lastPage'],
            'itemsPerPage' => $transaction['itemsPerPage'],
            'pageItems' => $transaction['pageItems'],
            'total' => $transaction['total']
        ]);
    }

    public function createPurchase()
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $defaultBranch = branchData();
        $company = companyData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $searchQuery = \request()->query('searchQuery', null);
        $productBranchCategoryID = \request()->query('productBranchCategoryID', null);
        $products = $this->pagination($this->productService->purchaseProducts($defaultBranch->id, $perPage, $page, $searchQuery, $productBranchCategoryID));
        $paymentModes  = $this->commonDataService->getPaymentMode($company->id, $defaultBranch->isEBM);
        $customers = $this->companyPartyService->parties($company->id, PartyTypeEnums::SUPPLIER->value);
        $branchProductCategories = $this->commonDataService->getBranchProductCategory($defaultBranch);
        return Inertia::render('Transaction/CreatePurchase', [
            'Products' => $products['data'],
            'PaymentMethods' => $paymentModes,
            'Customers' => $customers,
            'currentPage' => $products['currentPage'],
            'lastPage' => $products['lastPage'],
            'itemsPerPage' => $products['itemsPerPage'],
            'pageItems' => $products['pageItems'],
            'total' => $products['total'],
            'isEBM' => $defaultBranch->isEBM,
            'BranchProductCategories' => $branchProductCategories
        ]);
    }

    public function storePurchase(PurchaseRequest $request)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $company = companyData();
        $defaultBranch = branchData();
        $user = $this->user;
        $data = PurchaseData::from($request->validated());

        $this->service->createPurchase($data, $defaultBranch, $company, $user);

        return redirect()->route('transaction.purchase')->banner('Transaction created successfully.');
    }

    public function purchaseById(int $id)
    {

        $defaultBranch = branchData();
        $company = companyData();
        $transaction = $this->service->showTransaction($id, $defaultBranch);
        return Inertia::render('Transaction/ShowPurchase', [
            'Transaction' => $transaction,
            'Company' => [
                'name' => $company->name,
                'address' => $company->address,
                'phone' => $company->phone,
                'email' => $company->email,
                'tin' => $company->tin,
                'mrc' => $company->mrc,
            ],
            'Branch' => [
                'topMessage' => $defaultBranch->topMessage,
                'bottomMessage' => $defaultBranch->bottomMessage,
                'mrc' => $defaultBranch->mrc
            ]
        ]);
    }

    public function downloadPurchase(int $id)
    {

        return $this->streamPdfResponse(
            $id,
            false,
            'purchaseInvoice',
            'zata-point-purchase-invoice'
        );
    }

    public function download(int $id, bool $copy = false)
    {
        return $this->streamPdfResponse(
            $id,
            $copy,
            'transactionInvoice',
            'zata-point-invoice'
        );
    }

    public function downloadSmall(int $id, bool $copy = false)
    {

        return $this->streamPdfResponse(
            $id,
            $copy,
            'smallTransactionInvoice',
            'zata-point-invoice'
        );
    }

    public function downloadSmall58(int $id, bool $copy = false){

        return $this->streamPdfResponse(
            $id,
            $copy,
            'SmallTransactionInvoice58',
            'zata-point-invoice'
        );
    }

    public function deliveryNote(int $id)
    {
        return $this->streamPdfResponse(
            $id,
            false,
            'deliveryNote',
            'zata-point-delivery-note'
        );
    }

    private function streamPdfResponse(int $id, bool $copy, string $pdfMethod, string $filePrefix)
    {

        $defaultBranch = branchData();
        $company = companyData();
        $transaction = $this->service->showTransaction($id, $defaultBranch);

        $downloadName = "{$filePrefix}-" . date('Y-m-d-H-i') . '.pdf';

        return response()->streamDownload(
            function () use ($transaction, $company, $defaultBranch, $copy, $pdfMethod) {
                echo $this->pdfService->$pdfMethod($transaction, $company, $defaultBranch, $copy);
            },
            $downloadName,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="' . $downloadName . '"',
                'X-Content-Type-Options' => 'nosniff',
            ]
        );
    }
}
