<!DOCTYPE html>
<html>
<head>
    <title>Zata Point Invoice - 58mm</title>
    <style>
        @media print {
            body { margin: 0; padding: 0; }
            .no-print { display: none; }
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.2;
            margin: 0;
            padding: 0;
            background: white;
        }
        
        .receipt-container {
            width: 58mm;
            max-width: 58mm;
            margin: 0 auto;
            padding: 2mm;
            box-sizing: border-box;
        }
        
        .header-logos {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .header-logos img {
            width: 15mm;
            height: auto;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 8px;
        }
        
        .company-name {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 2px;
        }
        
        .company-details {
            font-size: 9px;
            margin-bottom: 1px;
        }
        
        .transaction-type {
            text-align: center;
            font-weight: bold;
            margin: 6px 0;
            font-size: 10px;
        }
        
        .refund-info {
            text-align: center;
            font-weight: bold;
            font-size: 9px;
            margin: 4px 0;
        }
        
        .top-message {
            text-align: center;
            font-weight: bold;
            margin: 6px 0;
            font-size: 10px;
        }
        
        .reference {
            font-size: 9px;
            margin: 4px 0;
        }
        
        .customer-info {
            border-top: 1px dashed #000;
            border-bottom: 1px dashed #000;
            padding: 4px 0;
            margin: 6px 0;
            font-size: 9px;
        }
        
        .customer-info div {
            margin-bottom: 1px;
        }
        
        .item {
            margin: 4px 0;
            font-size: 9px;
        }
        
        .item-name {
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .item-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1px;
        }
        
        .discount-line {
            display: flex;
            justify-content: space-between;
            font-size: 8px;
            color: #666;
        }
        
        .separator {
            border-top: 1px dashed #000;
            margin: 6px 0;
        }
        
        .not-official {
            text-align: center;
            font-weight: bold;
            font-size: 10px;
            margin: 4px 0;
            border-top: 1px dashed #000;
            border-bottom: 1px dashed #000;
            padding: 4px 0;
        }
        
        .totals {
            border-bottom: 1px dashed #000;
            padding: 4px 0;
            margin: 4px 0;
        }
        
        .total-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
            font-size: 9px;
        }
        
        .total-line.main {
            font-weight: bold;
            font-size: 10px;
        }
        
        .payment-info {
            border-bottom: 1px dashed #000;
            padding: 4px 0;
            margin: 4px 0;
        }
        
        .insurance-info {
            border: 1px solid #000;
            padding: 4px;
            margin: 4px 0;
            font-size: 9px;
        }
        
        .copy-notice {
            text-align: center;
            font-weight: bold;
            border-bottom: 1px dashed #000;
            padding: 4px 0;
            margin: 4px 0;
        }
        
        .mode-notice {
            text-align: center;
            font-weight: bold;
            padding: 4px 0;
            margin: 4px 0;
        }
        
        .sdc-info {
            border-bottom: 1px dashed #000;
            padding: 4px 0;
            margin: 4px 0;
            font-size: 8px;
        }
        
        .sdc-title {
            text-align: center;
            font-weight: bold;
            font-size: 9px;
            margin-bottom: 4px;
        }
        
        .sdc-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        
        .signature-text {
            font-size: 7px;
            word-break: break-all;
        }
        
        .qr-container {
            text-align: center;
            margin: 4px 0;
        }
        
        .receipt-footer {
            border-bottom: 1px dashed #000;
            padding: 4px 0;
            margin: 4px 0;
        }
        
        .thank-you {
            text-align: center;
            font-weight: bold;
            font-size: 9px;
            margin: 4px 0;
        }
        
        .version {
            text-align: center;
            font-size: 7px;
            margin: 2px 0;
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Header with logos -->
        <div class="header-logos">
            <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('ebm_logo_2.png'))) }}" alt="EBM Logo 1">
            <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('ebm_logo_1.png'))) }}" alt="EBM Logo 2">
        </div>

        <!-- Company Information -->
        <div class="company-info">
            <div class="company-name">{{ $company->name }}</div>
            <div class="company-details"><strong>{{ $company->address }}</strong></div>
            <div class="company-details"><strong>TEL: {{ $company->phone }}</strong></div>
            <div class="company-details" style="display: none;"><strong>EMAIL: {{ $company->email }}</strong></div>
            <div class="company-details"><strong>TIN: {{ $company->tin }}</strong></div>
        </div>

        <!-- Transaction Type -->
        <div class="transaction-type">
            @if ($transaction->type == 'NS')
                <span style="display: none;">SALES</span>
            @elseif ($transaction->type == 'TS')
                TRAINING SALES
            @elseif ($transaction->type == 'TR')
                TRAINING REFUND
            @elseif ($transaction->type == 'PS')
                PROFORMA
            @endif
        </div>

        <!-- Refund Information -->
        @if ($transaction->type == 'NR' || $transaction->type == 'TR')
            <div class="refund-info">
                @if ($transaction->type == 'NR')
                    <div>REFUND</div>
                @endif
                <div>REF. NORMAL RECEIPT:</div>
                <div>{{ $transaction->originalInvoiceNumber }}</div>
                <div class="separator"></div>
                <div>REFUND IS APPROVED ONLY FOR ORIGINAL SALES RECEIPT</div>
            </div>
        @endif

        <!-- Top Message -->
        <div class="top-message">
            {{ $branch->topMessage }}
        </div>

        <!-- Reference -->
        <div class="reference">
            <strong>Reference:</strong> Served by {{ $transaction->user->name }}
        </div>

        <!-- Customer Information -->
        <div class="customer-info">
            <div>Customer</div>
            <div><strong>TIN:</strong> {{ $transaction->clientTin ?? '-' }}</div>
            <div><strong>Name:</strong> {{ $transaction->clientName ?? '-' }}</div>
            <div><strong>Phone:</strong> {{ $transaction->clientPhoneNumber ?? '-' }}</div>
        </div>

        <!-- Items -->
        @foreach ($transaction->items as $item)
            <div class="item">
                <div class="item-name">{{ $item->productName }}</div>
                <div class="item-details">
                    <span>{{ number_format($item->price, 2) }} X {{ $item->quantity }}</span>
                    <span>
                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                        @if ($item->discount > 0)
                            {{ number_format($item->totalAmount + $item->totalDiscount, 2) }}
                        @else
                            {{ number_format($item->totalAmount, 2) }}
                        @endif
                        @if ($item->taxName == 'A')A-EX
                        @elseif ($item->taxName == 'B')B
                        @elseif ($item->taxName == 'C')C
                        @elseif ($item->taxName == 'D')D
                        @endif
                    </span>
                </div>
                @if ($item->discount > 0)
                    <div class="discount-line">
                        <span>Discount {{ $item->discount }}%</span>
                        <span>
                            @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                            {{ number_format($item->totalAmount, 2) }}
                        </span>
                    </div>
                @endif
            </div>
        @endforeach

        <div class="separator"></div>

        <!-- Not Official Notice -->
        @if ($transaction->type == 'PS' || $transaction->type == 'TS' || $transaction->type == 'TR' || $copy == true)
            <div class="not-official">
                THIS IS NOT AN OFFICIAL RECEIPT
            </div>
        @endif

        <!-- Totals -->
        <div class="totals">
            <div class="total-line main">
                <span>TOTAL</span>
                <span>
                    @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                    {{ number_format($transaction->totAmt, 2, '.', ',') }} Frw
                </span>
            </div>

            @if ($transaction->taxblAmtA > 0)
                <div class="total-line">
                    <span>TOTAL A-EX</span>
                    <span>
                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                        {{ number_format($transaction->taxblAmtA, 2) }}
                    </span>
                </div>
            @endif

            @if ($transaction->taxblAmtB > 0)
                <div class="total-line">
                    <span>TOTAL B-18%</span>
                    <span>
                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                        {{ number_format($transaction->taxblAmtB, 2) }}
                    </span>
                </div>
            @endif

            @if ($transaction->taxAmtB > 0 || $transaction->taxAmtB < 0)
                <div class="total-line">
                    <span>TOTAL TAX B</span>
                    <span>
                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                        {{ number_format($transaction->taxAmtB, 2) }}
                    </span>
                </div>
            @endif

            @if ($transaction->taxblAmtC > 0)
                <div class="total-line">
                    <span>TOTAL C</span>
                    <span>
                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                        {{ number_format($transaction->taxblAmtC, 2) }}
                    </span>
                </div>
            @endif

            @if ($transaction->taxblAmtD > 0)
                <div class="total-line">
                    <span>TOTAL D</span>
                    <span>
                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                        {{ number_format($transaction->taxblAmtD, 2) }}
                    </span>
                </div>
            @endif

            <div class="total-line">
                <span>TOTAL TAX</span>
                <span>
                    @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                    {{ number_format($transaction->totTaxAmt, 2) }}
                </span>
            </div>
        </div>

        <!-- Payment Information -->
        <div class="payment-info">
            <div class="total-line main">
                <span>
                    @if ($transaction->paymentTypeCode == '01')CASH
                    @elseif ($transaction->paymentTypeCode == '02')CREDIT
                    @elseif ($transaction->paymentTypeCode == '03')CASH/CREDIT
                    @elseif ($transaction->paymentTypeCode == '04')BANK CHECK
                    @elseif ($transaction->paymentTypeCode == '05')DEBIT & CREDIT CARD
                    @elseif ($transaction->paymentTypeCode == '06')MOBILE MONEY
                    @elseif ($transaction->paymentTypeCode == '07')OTHER
                    @endif
                </span>
                <span>
                    @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                    {{ number_format($item->totalAmount, 2) }}
                </span>
            </div>

            <div class="total-line main">
                <span>ITEMS NUMBER</span>
                <span>{{ $transaction->items->count() }}</span>
            </div>
        </div>

        <!-- Insurance Information -->
        @if ($transaction->party?->patient_details != null)
            <div class="insurance-info">
                <div><strong>Insurance:</strong> {{ $transaction->party->patient_details->percentage }}%</div>
                <div><strong>Insurance Discount:</strong> {{ number_format(($transaction->totAmt * $transaction->party->patient_details->percentage) / 100, 2) }}</div>
                <div><strong>Amount to be paid:</strong> {{ number_format($transaction->totAmt - ($transaction->totAmt * $transaction->party->patient_details->percentage) / 100, 2) }}</div>
            </div>
        @endif

        <!-- Copy Notice -->
        @if ($copy)
            <div class="copy-notice">
                <strong>COPY</strong>
            </div>
        @endif

        <!-- Training Mode -->
        @if ($transaction->salesTypeCode == 'T')
            <div class="mode-notice">
                <strong>TRAINING MODE</strong>
                <div class="separator"></div>
            </div>
        @endif

        <!-- Proforma -->
        @if ($transaction->salesTypeCode == 'P')
            <div class="mode-notice">
                <strong>PROFORMA</strong>
                <div class="separator"></div>
            </div>
        @endif

        <!-- SDC Information -->
        @if ($transaction->signature != null && $transaction->signature !== '')
            <div class="sdc-info">
                <div class="sdc-title">SDC INFORMATION</div>
                
                <div class="sdc-line">
                    <span>Date: {{ date('d-m-Y', strtotime($transaction->signature?->vsdcReceiptPublishDate)) }}</span>
                    <span>Time: {{ date('h:i A', strtotime($transaction->signature?->vsdcReceiptPublishDate)) }}</span>
                </div>
                
                <div class="sdc-line">
                    <span>SDC ID:</span>
                    <span>{{ $transaction->signature?->sdcId }}</span>
                </div>
                
                <div class="sdc-line">
                    <span>RECEIPT NUMBER:</span>
                    <span>{{ $transaction->signature?->receiptNumber }}/{{ $transaction->signature?->totalReceiptNumber }} {{ $transaction->type }}</span>
                </div>

                @if ($transaction->signature?->internalData != null)
                    <div>
                        <div><strong>Internal Data:</strong></div>
                        <div class="signature-text">{{ $transaction->signature?->internalData }}</div>
                    </div>
                @endif

                @if ($transaction->signature?->receiptSignature != null)
                    <div>
                        <div><strong>Receipt Signature:</strong></div>
                        <div class="signature-text">{{ $transaction->signature?->receiptSignature }}</div>
                    </div>
                @endif

                @if ($transaction->salesTypeCode !== 'P' && $transaction->signature != null)
                    <div class="qr-container">
                        {{ QrCode::generate('Demo QR code data for EBM Invoice from HIQ Zata') }}
                    </div>
                @endif
            </div>
        @endif

        <!-- Receipt Footer Information -->
        @if ($transaction->signature != null)
            <div class="receipt-footer">
                <div class="sdc-line">
                    <span>RECEIPT NUMBER</span>
                    <span>{{ $transaction->signature?->invoiceNumber }}</span>
                </div>
                
                <div class="sdc-line">
                    <span>Date: {{ date('d-m-Y', strtotime($transaction->signature?->vsdcReceiptPublishDate)) }}</span>
                    <span>Time: {{ date('h:i A', strtotime($transaction->signature?->vsdcReceiptPublishDate)) }}</span>
                </div>
                
                <div class="sdc-line">
                    <span>MRC:</span>
                    <span>{{ $branch->mrc }}</span>
                </div>
            </div>
        @endif

        <div class="thank-you">{{ $branch->bottomMessage }}</div>
        <div class="version">Version V1.0.0 Powered by RRA_VSDC</div>
    </div>
</body>
</html>