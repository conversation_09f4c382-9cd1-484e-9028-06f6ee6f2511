<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from "@inertiajs/vue3";
import RadioOptionGroup from '@/Components/RadioOptionGroup.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import SelectOption from '@/Components/SelectOption.vue';
import FormSection from '@/Components/FormSection.vue';

const props = defineProps({
    'Roles': Array,
    'Branches': Array,
    'User': Object
});

const form = useForm({
    'email': props.User.email,
    'name': props.User.name,
    'permissionID': props.User.permissionID,
    'branchID': props.User.branchID,
    'isActive': props.User.isActive ? '1' : '0',
});

function inviteUser() {
    form.put(route('user.update.save', props.User.id), {

    });
}

const options = [
{ value: '1', label: 'Activated' },
    { value: '0', label: 'Deactivated' },
   
];

</script>

<template>
    <AppLayout title="Update ">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                 Update User
            </h2>
        </template>

        <FormSection>
            <template #title>
                Update User
            </template>

            <template #form>
                <form class="" @submit.prevent="inviteUser">
                    <div class="pb-4">
                        <InputLabel for="name" value="Name" />
                        <TextInput id="name" v-model="form.name" type="text" disabled class="bg-gray-200" />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>

                    <div class="pb-4">
                        <InputLabel for="email" value="Email" />
                        <TextInput id="email" v-model="form.email" type="email" disabled class="bg-gray-200" />
                        <InputError class="mt-2" :message="form.errors.email" />
                    </div>

                    <div class="grid grid-cols-6 gap-6">

                        <div class="col-span-6 sm:col-span-3">
                            <SelectOption id="permissionID" v-model="form.permissionID" :options="Roles" label="Role"
                                placeholder="Select a role" valueKey="id" labelKey="name" />
                            <InputError class="mt-2" :message="form.errors.permissionID" />
                        </div>

                        <div class="col-span-6 sm:col-span-3">
                            <SelectOption id="branchID" v-model="form.branchID" :options="Branches" label="Branch"
                                placeholder="Select a branch" valueKey="id" labelKey="name" />
                            <InputError class="mt-2" :message="form.errors.branchID" />
                        </div>
                    </div>

                    <div class="pb-4">
                        <RadioOptionGroup v-model="form.isActive" :options="options" name=""
                            label="Status" />
                        <InputError class="mt-2" :message="form.errors.isActive" />
                    </div>
                    <div class="flex justify-end mt-4">
                        <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                            Update
                        </PrimaryButton>
                    </div>
                </form>
            </template>
        </FormSection>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>