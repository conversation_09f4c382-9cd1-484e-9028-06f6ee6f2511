<?php

use App\Models\Company;
use App\Models\CompanyInsurance;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use App\Models\RolePermission;
use App\Models\Role;

beforeEach(function () {

    $this->user = User::factory()->create();

    $this->apiUrl = '/api/v1/company';

    $this->permission = RolePermission::factory()->create(
        [
            'name' => 'Admin'
        ]
    );
    Sanctum::actingAs($this->user);
});

it('can create company', function (array $payload) {

    $response = $this->postJson($this->apiUrl, $payload);

    $response->assertStatus(200)
        ->assertExactJsonStructure(['message'])
        ->assertJson([
            'message' => 'Company created successfully',
        ]);

    $this->assertDatabaseHas('Company', [
        'name' => $payload['name'],
        'phone' => $payload['phone'],
        'email' => $payload['email'],
        'address' => $payload['address'],
        'tin' => $payload['tin'],
        'user_id' => $this->user->id
    ]);
})->with([

    function () {
        return [
            'name' => 'hirwa',
            'phone' => '0780000000',
            'email' => '<EMAIL>',
            'address' => 'kigali',
            'tin' => '*********',
        ];
    }
]);

it('can update company', function (array $payload) {

    $company = Company::factory()->create([
        'user_id' => $this->user->id,
        'isEBM' => true
    ]);

    $role = Role::factory()->create([
        'user_id' => $this->user->id,
        'company_id' => $company->id,
        'role_permission_id' => $this->permission
    ]);

    $this->withHeader('companyId', $company->id);

    $response = $this->putJson($this->apiUrl, $payload);

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Company updated successfully',
        ]);

    $this->assertDatabaseHas('Company', [
        'name' => $payload['name'],
        'phone' => $payload['phone'],
        'email' => $payload['email'],
        'address' => $payload['address'],
        'tin' => $payload['tin'],
        'user_id' => $this->user->id
    ]);
})->with([

    function () {
        return [
            'name' => 'hirwa',
            'phone' => '0780000009',
            'email' => '<EMAIL>',
            'address' => 'kigali',
            'tin' => '*********',
        ];
    }
]);

it('can get company by id', function () {

    $company = Company::factory()->create([
        'user_id' => $this->user->id,
        'isEBM' => true,
        'address' => 'kigali'
    ]);

    CompanyInsurance::factory()->create([
        'company_id' => $company->id
    ]);

    Role::factory()->create([
        'user_id' => $this->user->id,
        'company_id' => $company->id,
        'role_permission_id' => $this->permission
    ]);

    $this->withHeader('companyId', $company->id);

    $response = $this->get("{$this->apiUrl}/{$company->id}");

    $response->assertStatus(200)
        ->assertExactJsonStructure([
            'id',
            'name',
            'address',
            'phone',
            'email',
            'tin',
            'updated_at',
            'insurance' => [
                '*' => [
                    "id",
                    'name',
                    'code',
                    'rate',
                ],
            ],
        ])
        ->assertJson([
            'id' => $company->id,
            'name' => $company->name,
            'address' => $company->address,
            'phone' => $company->phone,
            'email' => $company->email,
            'tin' => $company->tin
        ]);
});

it('can get all company', function () {

    Company::factory()->create([
        'user_id' => $this->user->id,
        'isEBM' => true
    ]);

    Company::factory()->create([
        'user_id' => $this->user->id,
        'isEBM' => true
    ]);

    Company::factory()->create([
        'user_id' => $this->user->id,
        'isEBM' => false
    ]);

    foreach (Company::where('user_id', $this->user->id)->get('id') as $company) {

        Role::factory()->create([
            'user_id' => $this->user->id,
            'company_id' => $company->id,
            'role_permission_id' => $this->permission
        ]);
    }

    $response = $this->get($this->apiUrl);

    $response->assertStatus(200)->assertExactJsonStructure([
        'data' => [
            '*' => [
                'id',
                'name',
                'address',
                'phone',
                'email',
                'tin',
                'branchesCount',
                'isDefault',
                'image',
            ]
        ],
        'currentPage',
        'lastPage',
        'itemsPerPage',
        'pageItems',
        'total',
        'timestamp'
    ]);

    expect($response['data'])->toHaveCount(2);
});

it('can throw error when creating company ', function (array $payload) {

    $response = $this->postJson($this->apiUrl, $payload['data']);

    $response->assertStatus($payload['code'])
        ->assertJson([
            'message' => $payload['message'],
        ]);
})->with([

    function () {

        $company = Company::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'hirwa',
        ]);

        Role::factory()->create([
            'user_id' => $this->user->id,
            'company_id' => $company->id,
            'role_permission_id' => $this->permission
        ]);

        return [
            'data' => [
                'name' => 'hirwa',
                'phone' => '0780000000',
                'email' => '<EMAIL>',
                'address' => 'kigali',
                'tin' => '*********',
            ],
            'message' => 'Company name already exists',
            'code' => 400
        ];
    },
    function () {

        $company = Company::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'hirwax',
            'tin' => '*********',
        ]);

        Role::factory()->create([
            'user_id' => $this->user->id,
            'company_id' => $company->id,
            'role_permission_id' => $this->permission
        ]);

        return [
            'data' => [
                'name' => 'hirwa',
                'phone' => '0780000000',
                'email' => '<EMAIL>',
                'address' => 'kigali',
                'tin' => '*********',
                'deviceSerial' => 'demo'
            ],
            'message' => 'Company tin already exists',
            'code' => 400
        ];
    }
]);

it('can throw error geting company by id ', function () {

    $company = Company::factory()->create([
        'user_id' => $this->user->id
    ]);

    $this->withHeader('companyId', 9898989);

    $response = $this->get("{$this->apiUrl}/{$company->id}");

    $response->assertStatus(404)
        ->assertJson([
            'message' => 'Company not found',
        ]);
});

it('can throw not found during  update company', function (array $payload) {

    $this->withHeader('companyId', 9030403434);

    $response = $this->putJson($this->apiUrl, $payload);

    $response->assertStatus(404)
        ->assertJson([
            'message' => 'Company not found',
        ]);
})->with([

    function () {
        return [
            'name' => 'hirwa',
            'phone' => '0780000000',
            'email' => '<EMAIL>',
            'address' => 'kigali',
            'tin' => '*********',
        ];
    }
]);

it('can throw errors when updating the  company', function (array $payload) {

    $company = Company::factory()->create([
        'user_id' => $this->user->id,
        'tin' => '*********',
        'isEBM' => true
    ]);

    Role::factory()->create([
        'user_id' => $this->user->id,
        'company_id' => $company->id,
        'role_permission_id' => $this->permission
    ]);

    $this->withHeader('companyId', $company->id);

    $response = $this->putJson($this->apiUrl, $payload['data']);

    $response->assertStatus($payload['code'])
        ->assertJson([
            'message' =>  $payload['message'],
        ]);
})->with([

    function () {

        $company = Company::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'hirwax',
            'tin' => '*********',
            'isEBM' => true
        ]);

        Role::factory()->create([
            'user_id' => $this->user->id,
            'company_id' => $company->id,
            'role_permission_id' => $this->permission
        ]);
        return [
            'data' => [
                'name' => 'hirwax',
                'phone' => '0780000000',
                'email' => '<EMAIL>',
                'address' => 'kigali',
                'tin' => '*********',
            ],
            'message' => 'Company name already exists',
            'code' => 400
        ];
    },
    function () {

        $company = Company::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'hirwax',
            'tin' => '*********',
            'isEBM' => true
        ]);

        Role::factory()->create([
            'user_id' => $this->user->id,
            'company_id' => $company->id,
            'role_permission_id' => $this->permission
        ]);
        return [
            'data' => [
                'name' => 'hirwa',
                'phone' => '0780000000',
                'email' => '<EMAIL>',
                'address' => 'kigali',
                'tin' => '*********',
            ],
            'message' => 'Company tin already exists',
            'code' => 400
        ];
    }
]);
