<?php

use App\Http\Controllers\CommonDataController;
use App\Http\Controllers\Api\CompanyBranchController;
use App\Http\Controllers\Api\CompanyController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\TransactionController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\TestController;


Route::get('test', [TestController::class, 'index']);
Route::get('test/pdf', [TestController::class, 'pdf']);


Route::middleware(['auth:sanctum'])->group(function () {

    Route::prefix('data')->group(function () {

        Route::get('product-type', [CommonDataController::class, 'producType']);
        Route::get('payment-mode', [CommonDataController::class, 'paymentMode']);
        Route::get('product-category', [CommonDataController::class, 'productCategory']);
        Route::get('product-class', [CommonDataController::class, 'productClass']);
        Route::get('product-quantity-unit', [CommonDataController::class, 'productQuantityUnit']);
        Route::get('product-packaging-unit', [CommonDataController::class, 'productPackagingUnit']);
        Route::get('product-tax', [CommonDataController::class, 'productTax']);
        Route::get('product-country-origin', [CommonDataController::class, 'productCountry']);
    });

    Route::prefix('company')->group(function () {

        Route::get('', [CompanyController::class, 'index']);
        Route::post('', [CompanyController::class, 'create']);
        Route::put('', [CompanyController::class, 'update']);
        Route::get('{companyId}', [CompanyController::class, 'show'])->whereNumber('companyId');

        Route::prefix('branch')->group(function () {

            Route::get('', [CompanyBranchController::class, 'index']);
            Route::post('', [CompanyBranchController::class, 'store']);
            Route::put('', [CompanyBranchController::class, 'update']);
        });
    });

    Route::prefix('product')->group(function () {

        Route::post('', [ProductController::class, 'create']);
        Route::put('{productId}', [ProductController::class, 'update'])->whereNumber('productid');
        Route::put('increase-quantity/{productId}', [ProductController::class, 'updateProductQuantity'])->whereNumber('productId');
        Route::put('reduce-quantity/{productId}', [ProductController::class, 'reduceProductQuantity'])->whereNumber('productId');
        Route::get('branch', [ProductController::class, 'getProduct']);
        Route::get('{productId}', [ProductController::class, 'getProductById'])->whereNumber(['branchId', 'productId']);
    });

    Route::prefix('transaction')->group(function () {

        Route::get('', [TransactionController::class, 'index']);
        Route::post('sale', [TransactionController::class, 'createSale']);
        Route::post('refund/{transactionId}', [TransactionController::class, 'createRefund'])->whereNumber('transactionId');
        Route::post('proforma', [TransactionController::class, 'createProforma']);
        Route::post('calculate', [TransactionController::class, 'calculate']);
        Route::get('{transactionId}', [TransactionController::class, 'getTransactionById'])->whereNumber('transactionId');
        Route::get('download-signature/{transactionId}', [TransactionController::class, 'downloadTransaction'])->whereNumber('transactionId');

    });
});

Route::get('transaction/download', [TransactionController::class, 'downloadPdf'])->name('download.signed.pdf')->middleware(['auth:sanctum', 'signed']);
