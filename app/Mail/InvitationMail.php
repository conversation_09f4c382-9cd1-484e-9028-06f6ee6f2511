<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class InvitationMail extends Mailable
{
    use Queueable, SerializesModels;

    public $inviterName;
    public $inviteeEmail;
    public $companyName;
    public $role;
    public $invitationLink;

    public function __construct($inviterName, $inviteeEmail, $companyName, $role, $invitationLink)
    {
        $this->inviterName = $inviterName;
        $this->inviteeEmail = $inviteeEmail;
        $this->companyName = $companyName;
        $this->role = $role;
        $this->invitationLink = $invitationLink;
    }

    public function build()
    {
        return $this->subject("You're invited to join {$this->companyName}")
            ->view('emails.invitation');
    }
}
