<?php

namespace App\Services;

use App\Models\Company;
use App\Models\CompanyBranch;
use App\Models\Order;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Exceptions\NotFound;
use App\Enums\TransactionTypeEnums;
use Illuminate\Pagination\LengthAwarePaginator;

class AdminService
{
    public function getAnalytics()
    {

        $now = Carbon::now('Africa/Johannesburg');

        $periods = [
            'last_30_days' => $now->copy()->subDays(30),
            'last_15_days' => $now->copy()->subDays(15),
            'last_7_days' => $now->copy()->subDays(7),
            'last_24_hours' => $now->copy()->subHours(24),
            'last_6_hours' => $now->copy()->subHours(6),
            'last_1_hour' => $now->copy()->subHour(),
            'last_15_minutes' => $now->copy()->subMinutes(15),
        ];

        $orders = Order::where('created_at', '>=', $periods['last_30_days'])->get();

        Log::info('FilteredOrders: ' . $orders->toJson());

        $analytics = [];

        foreach ($periods as $periodName => $date) {
            $count = $orders->filter(function ($order) use ($date) {
                return Carbon::parse($order->created_at)->greaterThanOrEqualTo($date);
            })->count();

            $analytics[$periodName] = $count;
        }

        $topCompanies = Company::withCount(['branches' => function ($query) {
            $query->whereHas('orders', function ($query) {
                $query->where('created_at', '>=', Carbon::now()->subDays(30));
            });
        }])->orderBy('branches_count', 'desc')->take(5)->get();


        // in top companies add top perfiming comoanies in last 30 days , top performing branches in last 30 days,
        // top performing branches in last 15 days, top performing branches in last 7 days, top performing branches in last 24 hours,

        $companyBranchPeriods = [
            'last_30_days' => $now->copy()->subDays(30),
            'last_15_days' => $now->copy()->subDays(15),
            'last_7_days' => $now->copy()->subDays(7),
            'last_24_hours' => $now->copy()->subHours(24),
            'last_6_hours' => $now->copy()->subHours(6),
            'last_1_hour' => $now->copy()->subHour(),
            'last_15_minutes' => $now->copy()->subMinutes(15),
        ];

        foreach ($topCompanies as $company) {
            $companyBranches = CompanyBranch::where('company_id', $company->id)
                ->select('id', 'name', 'phone', 'email', 'address', 'branchCode')
                ->withCount(['orders' => function ($query) use ($companyBranchPeriods) {
                    $query->where('created_at', '>=', $companyBranchPeriods['last_30_days']);
                }])->orderBy('orders_count', 'desc')->take(5)->get();

            $company->branches = $companyBranches;
            // with counted orders
            $company->branches_count = $companyBranches->sum('orders_count');
        }

        return [
            'analytics' => $analytics,
            'topCompanies' => $topCompanies,
        ];

        //eturn $analytics;
    }

    public function searchInvoice(int $perPage, int $page, ?int $companyId, ?string $invoiceType, ?string $fromDate, ?string $toDate, ?string $searchQuery): LengthAwarePaginator
    {


        $invoiceTypes = [
            TransactionTypeEnums::SALES->value,
            TransactionTypeEnums::SALES_RETURN->value,
            TransactionTypeEnums::PURCHASE->value,
            TransactionTypeEnums::PROFORMA->value,
            TransactionTypeEnums::DELIVERY_NOTE->value
        ];

        throw_if(!in_array($invoiceType, $invoiceTypes), NotFound::class, "Invalid invoice type, $invoiceType");

        if (is_null($searchQuery)) {
            return  Order::with('items')
                ->when($companyId, fn($query) => $query->whereHas('branch', function ($query) use ($companyId) {
                    $query->where('company_id', $companyId);
                }))
                ->when($invoiceType, fn($query) => $query->where('transactionType', $invoiceType))
                ->FilterByDate($fromDate, $toDate)
                ->orderBy('created_at', 'desc')
                ->paginate(perPage: $perPage, page: $page)
                ->through(fn($order) => [
                    'id' => $order->id,
                    'companyName' => $order->branch->company->name,
                    'branchName' => $order->branch->name,
                    'userName' => $order->user->name,
                    'invoiceNumber' => $order->invoiceNumber,
                    'clientName' => $order->clientName,
                    'salesDate' => $order->salesDate,
                    'createdAt' => $order->created_at,
                    'totalAmount' => $order->totAmt,
                    'itemsCount' =>   $order->items->count(),
                    'items' => $order->items->map(fn($item) => [
                        'productName' => $item->productName,
                        'units' => $item->quantity,
                        'unitPrice' => $item->price,
                        'discountRate' => $item->discount,
                        'taxAmount' => $item->taxAmount,
                        'taxRate' => $item->taxRate,
                        'taxName' => $item->taxName,
                        'totalAmount' => $item->totalAmount,
                        'totalDiscount' => $item->totalDiscount
                    ]),
                    'invoiceType' => $order->invoiceType,
                ]);
        }

        return  Order::search($searchQuery)

            ->query(function ($query) use ($companyId, $invoiceType, $fromDate, $toDate) {
                $query->when($companyId, fn($query) => $query->whereHas('branch', function ($query) use ($companyId) {
                    $query->where('company_id', $companyId);
                }))
                    ->when($invoiceType, fn($query) => $query->where('transactionType', $invoiceType))
                    ->FilterByDate($fromDate, $toDate);
            })
            ->paginate(perPage: $perPage, page: $page)
            ->through(fn($order) => [
                'id' => $order->id,
                'companyName' => $order->branch->company->name,
                'branchName' => $order->branch->name,
                'userName' => $order->user->name,
                'invoiceNumber' => $order->invoiceNumber,
                'clientName' => $order->clientName,
                'salesDate' => $order->salesDate,
                'createdAt' => $order->created_at,
                'totalAmount' => $order->totAmt,
                'itemsCount' =>   $order->items->count(),
                'items' => $order->items->map(fn($item) => [
                    'productName' => $item->productName,
                    'units' => $item->quantity,
                    'unitPrice' => $item->price,
                    'discountRate' => $item->discount,
                    'taxAmount' => $item->taxAmount,
                    'taxRate' => $item->taxRate,
                    'taxName' => $item->taxName,
                    'totalAmount' => $item->totalAmount,
                    'totalDiscount' => $item->totalDiscount
                ]),
                'invoiceType' => $order->invoiceType,
            ]);
    }
}
