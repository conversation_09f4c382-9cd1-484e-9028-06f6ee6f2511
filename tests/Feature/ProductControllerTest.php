<?php

use App\Enums\ProductStockStatusEnums;
use App\Models\BranchProductCategory;
use App\Models\Company;
use App\Models\CompanyBranch;
use App\Models\Country;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductClass;
use App\Models\ProductDetail;
use App\Models\ProductPackingUnit;
use App\Models\ProductQuantityUnit;
use App\Models\ProductType;
use App\Models\Tax;
use App\Models\User;
use Laravel\Sanctum\Sanctum;

use App\Models\Role;
use App\Models\RolePermission;

beforeEach(function () {

    $this->user = User::factory()->create();

    $this->company = Company::factory()->create(["user_id" =>  $this->user->id, "isEBM" => true]);
    $this->branch = CompanyBranch::factory()->create(["company_id" => $this->company->id, 'isEBM' => true]);

    $this->permission = RolePermission::factory()->create();
    $this->role = Role::factory()->create([
        'user_id' => $this->user->id,
        'company_id' => $this->company->id,
        'role_permission_id' => $this->permission
    ]);

    $this->apiUrl = '/api/v1/product';

    $tax = [['code' => 'A', 'rate' => 0], ['code' => 'B', 'rate' => 18], ['code' => 'C', 'rate' => 0], ['code' => 'D', 'rate' => 0]];

    foreach ($tax as $item) {
        Tax::factory()->create([
            'code' => $item['code'],
            'rate' => $item['rate']
        ]);
    }

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    Sanctum::actingAs($this->user);
});

it("can create new product", function () {

    $payload = [
        'name' => fake()->name,
        'quantityUnitID' => ProductQuantityUnit::factory()->create()->id,
        'packagingUnitID' => ProductPackingUnit::factory()->create()->id,
        'countryID' => Country::factory()->create()->id,
        'taxID' => Tax::factory()->create()->id,
        'branchProductCategoryID' => BranchProductCategory::factory()->create()->id,
        'hasStock' => 'yes'
    ];
    $response = $this->postJson($this->apiUrl, $payload);

    $response->assertStatus(200);

    $response->assertJson(['message' => 'Product created successfully'])
        ->assertExactJsonStructure(['message', 'productId']);

    $this->assertDatabaseHas('Product', [
        'name' => $payload['name'],
        'branch_id' => $this->branch->id,
        'quantity_unit_id' => $payload['quantityUnitID'],
        'packaging_unit_id' => $payload['packagingUnitID'],
        'country_id' => $payload['countryID'],
        'tax_id' => $payload['taxID'],
        'branch_product_category_id' => $payload['branchProductCategoryID'],

    ]);
});

it(" can throw validation error on create new product", function (array $payload) {

    $response = $this->postJson($this->apiUrl, $payload['payload']);

    $response->assertStatus(404);

    $response->assertJson(['message' => $payload['message']]);
})->with([

    function () {

        $data = [
            'name' => fake()->name,
            'categoryID' => ProductCategory::factory()->create()->id,
            'quantityUnitID' => 1,
            'packagingUnitID' => 1,
            'countryID' => 1,
            'taxID' => 1,
            'classID' => 1,
            'typeID' => 1,
            'branchProductCategoryID' => 1,
            'soldInSubUnit' => false,
            'hasStock' => 'yes'
        ];

        $message = 'Quantity unit not found';

        return [
            'payload' => $data,
            'message' => $message,
        ];
    },

    function () {

        $data = [
            'name' => fake()->name,
            'categoryID' => ProductCategory::factory()->create()->id,
            'quantityUnitID' =>  ProductQuantityUnit::factory()->create()->id,
            'packagingUnitID' => 1,
            'countryID' => 1,
            'taxID' => 1,
            'classID' => 1,
            'typeID' => 1,
            'branchProductCategoryID' => 1,
            'soldInSubUnit' => false,
            'hasStock' => 'yes'
        ];

        $message = 'Packing unit not found';

        return [
            'payload' => $data,
            'message' => $message,
        ];
    },

    function () {

        $data = [
            'name' => fake()->name,
            'categoryID' => ProductCategory::factory()->create()->id,
            'quantityUnitID' =>  ProductQuantityUnit::factory()->create()->id,
            'packagingUnitID' =>  ProductPackingUnit::factory()->create()->id,
            'countryID' => 1,
            'taxID' => 1,
            'classID' => 1,
            'typeID' => 1,
            'branchProductCategoryID' => 1,
            'soldInSubUnit' => false,
            'hasStock' => 'yes'
        ];

        $message = 'Country not found';

        return [
            'payload' => $data,
            'message' => $message,
        ];
    },

    function () {

        $data = [
            'name' => fake()->name,
            'categoryID' => ProductCategory::factory()->create()->id,
            'quantityUnitID' =>  ProductQuantityUnit::factory()->create()->id,
            'packagingUnitID' =>  ProductPackingUnit::factory()->create()->id,
            'countryID' => Country::factory()->create()->id,
            'taxID' => 1,
            'classID' => 1,
            'typeID' => 1,
            'branchProductCategoryID' => 1,
            'soldInSubUnit' => false,
            'hasStock' => 'yes'
        ];

        $message = 'Tax not found';

        return [
            'payload' => $data,
            'message' => $message,
        ];
    },

]);

it("can update product", function () {

    $batchNumber = fake()->word;
    $product = Product::factory()->create(
        [
            'branch_id' =>  $this->branch->id
        ]
    );
    ProductDetail::factory()->create([
        'product_id' => $product->id,
        'batchNumber' => $batchNumber,
        'discountRate' => 0
    ]);

    $payload = [
        'name' => fake()->name,
        'branchProductCategoryID' => BranchProductCategory::factory()->create()->id,
    ];

    $response = $this->putJson("{$this->apiUrl}/{$product->id}", $payload);

    $response->assertStatus(200);

    $response->assertJson(['message' => 'Product updated successfully']);

    $this->assertDatabaseHas('Product', [
        'name' => $payload['name'],
        'branch_product_category_id' => $payload['branchProductCategoryID'],
    ]);
});

it('can update product quantity ', function () {

    $product = Product::factory()->create(
        [
            'branch_id' =>  $this->branch->id,
            'description_three' => 'yes'
        ]
    );
    $productDetail =   ProductDetail::factory()->create([
        'product_id' => $product->id,
        'currentStock' => 0,
        'salesPrice' => 500,
        'batchNumber' => '12345',
        'discountRate' => 0,
    ]);

    $payload = [
        'description' => fake()->sentence,
        'quantity' => 232323,
        'salePrice' => 9000,
        'batchNumber' => '12345',
        'discountRate' => 0,
    ];

    $response = $this->putJson("{$this->apiUrl}/increase-quantity/{$product->id}", $payload);

    $response->assertStatus(200);

    $response->assertJson(['message' => 'Product quantity updated successfully']);

    $this->assertDatabaseHas('ProductDetails', [
        'currentStock' => $payload['quantity'] + $productDetail->currentStock,
        'product_id' => $product->id,
    ]);
});

it('can reduce product quantity ', function () {

    $product = Product::factory()->create(
        [
            'branch_id' =>  $this->branch->id,
        ]
    );
    $productDetail =   ProductDetail::factory()->create([
        'product_id' => $product->id,
        'currentStock' => 200,
        'batchNumber' => '12345',
    ]);

    $payload = [
        'quantity' => fake()->randomNumber(2),
        'batchNumber' => '12345',
        'description' => 'Expired',
    ];

    $response = $this->putJson("{$this->apiUrl}/reduce-quantity/{$product->id}", $payload);

    $response->assertStatus(200);

    $response->assertJson(['message' => 'Product quantity reduced successfully']);

    $this->assertDatabaseHas('ProductDetails', [
        'currentStock' => $productDetail->currentStock -  $payload['quantity'],
        'product_id' => $product->id,
    ]);
});

it('can throw error during stock reduction ', function (array $payload) {

    $response = $this->putJson("{$this->apiUrl}/reduce-quantity/{$payload['productId']}", $payload);

    $response->assertStatus($payload['code']);

    $response->assertJson(['message' => $payload['message']]);
})->with([
    function () {

        $product = Product::factory()->create(
            [
                'branch_id' =>  $this->branch->id
            ]
        );

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 200,
            'batchNumber' => '121212'
        ]);

        return [
            'productId' => $product->id,
            'quantity' => 300,
            'description' => 'Expired',
            'batchNumber' => '121212',
            'movingUnit' => 'sub',
            'message' => 'Quantity to reduce is greater than the current stock',
            'code' => 400
        ];
    },

    function () {

        return [
            'productId' => 111,
            'branchId'  =>  $this->branch->id,
            'quantity' => 300,
            'message' => 'Product not found',
            'code' => 404,
            'description' => 'Expired',
            'batchNumber' => '121212',
            'movingUnit' => 'sub',
        ];
    },
]);

it('can throw error during stock stock upgrade ', function (array $payload) {

    $response = $this->putJson("{$this->apiUrl}/increase-quantity/9000000000", $payload);

    $response->assertStatus($payload['code']);

    $response->assertJson(['message' => $payload['message']]);
})->with([

    function () {
        return [
            'quantity' => 300,
            'message' => 'Product not found',
            'code' => 404,
            'salePrice' => 9000,
            'batchNumber' => '12345',
            'discountRate' => 0,
        ];
    },
]);

it('can get product by id ', function () {

    $product = Product::factory()->create(
        [
            'branch_id' =>   $this->branch->id
        ]
    );
    ProductDetail::factory()->create(['product_id' => $product->id]);
    ProductDetail::factory()->create(['product_id' => $product->id]);
    ProductDetail::factory()->create(['product_id' => $product->id, 'currentStock' => 0]);
    $response = $this->get("{$this->apiUrl}/{$product->id}");

    $response->assertStatus(200);

    $response->assertExactJsonStructure([
        'id',
        'name',
        'itemCode',
        'category',
        'categoryID',
        'quantityUnit',
        'quantityUnitID',
        'packagingUnit',
        'packagingUnitID',
        'country',
        'countryID',
        'tax',
        'taxID',
        'class',
        'classID',
        'type',
        'typeID',
        'branchProductCategory',
        'branchProductCategoryID',
        'image',
        'totalStock',
        'soldInSubUnit',
        'conversionFactor',
        'hasStock',
        'productDetails' => [
            '*' => [
                'id',
                'currentStock',
                'purchasePrice',
                'salesPrice',
                'status',
                'expireDate',
                'batchNumber',
                'discountRate',
            ]
        ]
    ]);
});

it('can throw error, get product by id ', function (array $payload) {

    $response = $this->get("{$this->apiUrl}/{$payload['productId']}");

    $response->assertStatus(404);

    $response->assertJson(['message' => $payload['message']]);
})->with([
    function () {
        return  [
            'message' => 'Product not found',
            'productId' => 999999,
        ];
    },
]);

it('can get product', function () {

    $product = Product::factory()->create(
        [
            'branch_id' =>   $this->branch->id
        ]
    );

    ProductDetail::factory()->create(['product_id' => $product->id]);

    $response = $this->get("{$this->apiUrl}/branch");

    $response->assertStatus(200);

    $response->assertExactJsonStructure([
        'data' => [
            '*' => [
                'id',
                'name',
                'itemCode',
                'category',
                'quantityUnit',
                'packagingUnit',
                'tax',
                'class',
                'type',
                'totalStock',
                'image'
            ]
        ],
        'currentPage',
        'lastPage',
        'itemsPerPage',
        'pageItems',
        'total',
        'timestamp'
    ]);

    expect($response['data'])->toHaveCount(1);
});
