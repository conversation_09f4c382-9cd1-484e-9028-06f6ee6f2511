<script setup>
defineProps({
    type: {
        type: String,
        default: 'button',
    },
});
</script>

<template>
    <button :type="type" style="size:16px; " class="text-center px-4 py-2 bg-zata-primary-lighter border border-none rounded-md font-medium text-zata-secondary-dark  tracking-widest  hover:opacity-70 focus:outline-none focus:ring-2 focus:ring-none focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150 w-full">
        <slot />
    </button>
</template>
