<?php

use App\Enums\ProductStockStatusEnums;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ProductDetails', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->references('id')->on('Product')->onDelete('cascade')->onUpdate('restrict');
            $table->bigInteger('currentStock')->nullable()->default(0);
            $table->decimal('purchasePrice', 14, 2);
            $table->decimal('salesPrice', 14, 2);
            $table->decimal('discountRate')->default(0);
            $table->bigInteger('stockQuantityAlert')->nullable()->default(null);
            $table->bigInteger('openingStock')->nullable()->default(null);
            $table->string('status')->default(ProductStockStatusEnums::IN_STOCK->value);
            $table->bigInteger('expireDate')->nullable()->default(null);
            $table->string('batchNumber')->nullable()->default(null);
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();

            $table->unique(['product_id', 'batchNumber']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ProductDetails');
    }
};
