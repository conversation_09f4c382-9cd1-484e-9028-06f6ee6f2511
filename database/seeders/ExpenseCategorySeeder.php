<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ExpenseCategory;

class ExpenseCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        $categories = [
            [
                'name' => 'Food',
                'description' => 'Food expenses'
            ],
            [
                'name' => 'Transportation',
                'description' => 'Transportation expenses'
            ],
            [
                'name' => 'Entertainment',
                'description' => 'Entertainment expenses'
            ],
            [
                'name' => 'Utilities',
                'description' => 'Utilities expenses'
            ],
            [
                'name' => 'Healthcare',
                'description' => 'Healthcare expenses'
            ],
            [
                'name' => 'Other',
                'description' => 'Other expenses'
            ],
            [
                'name' => 'Savings',
                'description' => 'Savings expenses'
            ],
            [
                'name' => 'Electricity',
                'description' => 'Electricity expenses'
            ],
            [
                'name' => 'Other',  
                'description' => 'Other expenses'
            ]
        ];

        ExpenseCategory::insert($categories);
    }
}
