<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from "@inertiajs/vue3";
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import SelectOption from '@/Components/SelectOption.vue';
import FormSection from '@/Components/FormSection.vue';
import ActionMessage from '@/Components/ActionMessage.vue';

const props = defineProps({
    BranchProductCategories: Array,
    Product: Object,
});

const form = useForm({
    'name': props.Product.name,
    'branchProductCategoryID': props.Product.branchProductCategoryID,
});

function updateProduct() {
    form.put(route('product.update', props.Product.id), {
    });
}

</script>

<template>
    <AppLayout title="Edit Product">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Edit Product
            </h2>
        </template>

        <FormSection>
            <template #title>
                Product Information
            </template>

            <template #form>
                <form class="py-12 p-5 max-w-3xl m-auto" @submit.prevent="updateProduct">

                    <div class="pb-4">
                        <InputLabel for="name" value="Name" />
                        <TextInput id="name" v-model="form.name" type="text" />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>

                    <div class="pb-4">
                        <SelectOption id="branchProductCategoryID" v-model="form.branchProductCategoryID"
                            :options="BranchProductCategories" label="Product category" placeholder="Select a category"
                            valueKey="id" labelKey="name" />
                        <InputError class="mt-2" :message="form.errors.branchProductCategoryID" />
                    </div>
                    <div class="py-4">
                        <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                            Update
                        </PrimaryButton>
                    </div>

                    <ActionMessage :on="form.recentlySuccessful">
                            Successfully updated.
                        </ActionMessage>
                </form>
            </template>
        </FormSection>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>