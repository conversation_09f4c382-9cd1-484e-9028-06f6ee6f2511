<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Insurance extends Model
{
    /** @use HasFactory<\Database\Factories\InsuranceFactory> */
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'Insurance';

    protected $fillable = [
        'company_id',
        'name',
        'code',
        'rate',
        'isActive',
        'image',
    ];

    protected $casts = [
        'isActive' => 'boolean',
    ];
}
