<?php

namespace App\Http\Controllers;

use App\Data\CompanyBranchUpdateData;
use App\Data\NewCompanyBranchData;
use App\Enums\SubscriptionActionEnums;
use App\Http\Requests\CompanyBranchUpdateRequest;
use App\Http\Requests\NewCompanyBranchRequest;
use App\Services\AccountSubscriptionService;
use App\Services\CompanyBranchService;
use App\Services\CompanyService;
use Inertia\Inertia;
use App\Enums\AccountTypeEnums;

use Illuminate\Support\Facades\Auth;

class CompanyBranchController extends Controller
{

    protected $user;
    public function __construct(
        protected CompanyBranchService $service,
        protected CompanyService $companyService,
        protected AccountSubscriptionService $subscriptionService
    ) {
        $this->user = Auth::user();
    }

    public function new()
    {

        zataAccess([AccountTypeEnums::ADMIN->value]);
        $company = companyData();
        $this->subscriptionService->restrict($company->id, SubscriptionActionEnums::CREATE_BRANCH);
        return Inertia::render('Company/Branch/New', [
            'BranchCategories' => $this->service->getBranchCategories(),
        ]);
    }

    public function store(NewCompanyBranchRequest $request)
    {

        zataAccess([AccountTypeEnums::ADMIN->value]);
        $data = NewCompanyBranchData::from($request->validated());
        $company = companyData();
        $this->subscriptionService->restrict($company->id, SubscriptionActionEnums::CREATE_BRANCH);
        $this->service->storeBranch($data, $company);

        return redirect()->route('company.index')->banner('Branch Created Successfully');
    }

    public function edit(int $id)
    {

        zataAccess([AccountTypeEnums::ADMIN->value]);
        $company = companyData();
        $branch = $this->service->getBranch($id, $company);
        return Inertia::render('Company/Branch/Edit', [
            'Branch' => $branch,
            'companyId' => $company->id
        ]);
    }

    public function update(CompanyBranchUpdateRequest $request, int $id)
    {

        zataAccess([AccountTypeEnums::ADMIN->value]);
        $company = companyData();
        $data = CompanyBranchUpdateData::from($request->validated());
        $this->service->updateBranch($data, $id, $company);

        return redirect()->route('company.index')->banner('Branch Updated Successfully');
    }

    public function default(int $branchId)
    {
        $company = companyData();
        $this->service->makeDefaultBranch($branchId, $company);
        return redirect()->route('company.index');
    }
}
