<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or "CORS". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    */

    'paths' => ['api/*', 'sanctum/csrf-cookie', 'v1/', 'build/'],

    'allowed_methods' => ['*'],

    'allowed_origins' => ['*'],

    'allowed_origins_patterns' => [],

    'allowed_headers' => ['*'],

    'exposed_headers' => [],

    'max_age' => 0,

    'supports_credentials' => true,

];


/*
server {
    server_name  ebm.zata.rw;

    location / {
        proxy_pass http://localhost:4000;
    }




    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/ebm.zata.rw/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/ebm.zata.rw/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}
server {
    if ($host = ebm.zata.rw) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    server_name  ebm.zata.rw;
    listen 80;
    return 404; # managed by Certbot


}

*/