<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ProductQuantityUnit;

class ProductQuantityUnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $productQuantityUnit = [

            [
                'cd' => '4B',
                'cdNm' => 'Pair',
                'cdDesc' => 'Pair',
                'srtOrd' => 1,
            ],
            [
                'cd' => 'AV',
                'cdNm' => 'Cap',
                'cdDesc' => 'Cap',
                'srtOrd' => 2,
            ],
            [
                'cd' => 'BA',
                'cdNm' => 'Barrel',
                'cdDesc' => 'Barrel',
                'srtOrd' => 3,
            ],
            [
                'cd' => 'BE',
                'cdNm' => 'bundle',
                'cdDesc' => 'bundle',
                'srtOrd' => 4,
            ],
            [
                'cd' => 'BG',
                'cdNm' => 'bag',
                'cdDesc' => 'bag',
                'srtOrd' => 5,
            ],
            [
                'cd' => 'BL',
                'cdNm' => 'block',
                'cdDesc' => 'block',
                'srtOrd' => 6,
            ],
            [
                'cd' => 'BLL',
                'cdNm' => 'BLL Barrel (petroleum) (158,987 dm3)',
                'cdDesc' => 'BLL Barrel (petroleum) (158,987 dm3)',
                'srtOrd' => 7,
            ],
            [
                'cd' => 'BX',
                'cdNm' => 'box',
                'cdDesc' => 'box',
                'srtOrd' => 8,
            ],
            [
                'cd' => 'CA',
                'cdNm' => 'Can',
                'cdDesc' => 'Can',
                'srtOrd' => 9,
            ],
            [
                'cd' => 'CEL',
                'cdNm' => 'Cell',
                'cdDesc' => 'Cell',
                'srtOrd' => 10,
            ],
            [
                'cd' => 'CMT',
                'cdNm' => 'centimetre',
                'cdDesc' => 'centimetre',
                'srtOrd' => 11,
            ],
            [
                'cd' => 'CR',
                'cdNm' => 'CARAT',
                'cdDesc' => 'CAR',
            ],
            [
                'cd' => 'DR',
                'cdNm' => 'Drum',
                'cdDesc' => 'Drum',
                'srtOrd' => 13,
            ],
            [
                'cd' => 'DZ',
                'cdNm' => 'Dozen',
                'cdDesc' => 'Dozen',
                'srtOrd' => 14,
            ],
            [
                'cd' => 'GLL',
                'cdNm' => 'Gallon',
                'cdDesc' => 'Gallon',
                'srtOrd' => 15,
            ],
            [
                'cd' => 'GRM',
                'cdNm' => 'Gram',
                'cdDesc' => 'Gram',
                'srtOrd' => 16,
            ],
            [
                'cd' => 'GRO',
                'cdNm' => 'Gross',
                'cdDesc' => 'Gross',
                'srtOrd' => 17,
            ],
            [
                'cd' => 'KG',
                'cdNm' => 'Kilo-Gramme',
                'cdDesc' => 'Kilo-Gramme',
                'srtOrd' => 18,
            ],
            [
                'cd' => 'KTM',
                'cdNm' => 'kilometre',
                'cdDesc' => 'kilometre',
                'srtOrd' => 19,
            ],
            [
                'cd' => 'KWT',
                'cdNm' => 'kilowatt',
                'cdDesc' => 'kilowatt',
                'srtOrd' => 20,
            ],
            [
                'cd' => 'L',
                'cdNm' => 'Litre',
                'cdDesc' => 'Litre',
                'srtOrd' => 21,
            ],
            [
                'cd' => 'LBR',
                'cdNm' => 'pound',
                'cdDesc' => 'pound',
                'srtOrd' => 22,
            ],
            [
                'cd' => 'LK',
                'cdNm' => 'link',
                'cdDesc' => 'link',
                'srtOrd' => 23,
            ],
            [
                'cd' => 'LTR',
                'cdNm' => 'Litre',
                'cdDesc' => 'Litre',
                'srtOrd' => 24,
            ],
            [
                'cd' => 'M',
                'cdNm' => 'Metre',
                'cdDesc' => 'Metre',
                'srtOrd' => 25,
            ],
            [
                'cd' => 'M2',
                'cdNm' => 'Square Metre',
                'cdDesc' => 'Square Metre',
                'srtOrd' => 26,
            ],
            [
                'cd' => 'M3',
                'cdNm' => 'Cubic Metre',
                'cdDesc' => 'Cubic Metre',
                'srtOrd' => 27,
            ],
            [
                'cd' => 'MGM',
                'cdNm' => 'milligram',
                'cdDesc' => 'milligram',
                'srtOrd' => 28,
            ],
            [
                'cd' => 'MTR',
                'cdNm' => 'metre',
                'cdDesc' => 'metre',
                'srtOrd' => 29,
            ],
            [
                'cd' => 'MWT',
                'cdNm' => 'megawatt hour (1000 kW.h)',
                'cdDesc' => 'megawatt hour (1000 kW.h)',
                'srtOrd' => 30,
            ],
            [
                'cd' => 'NO',
                'cdNm' => 'Number',
                'cdDesc' => 'Number',
                'srtOrd' => 31,
            ],
            [
                'cd' => 'NX',
                'cdNm' => 'part per thousand',
                'cdDesc' => 'part per thousand',
                'srtOrd' => 32,
            ],
            [
                'cd' => 'PA',
                'cdNm' => 'packet',
                'cdDesc' => 'packet',
                'srtOrd' => 33,
            ],
            [
                'cd' => 'PG',
                'cdNm' => 'plate',
                'cdDesc' => 'plate',
                'srtOrd' => 34,
            ],
            [
                'cd' => 'PR',
                'cdNm' => 'pair',
                'cdDesc' => 'pair',
                'srtOrd' => 35,
            ],
            [
                'cd' => 'RL',
                'cdNm' => 'reel',
                'cdDesc' => 'reel',
                'srtOrd' => 36,
            ],
            [
                'cd' => 'RO',
                'cdNm' => 'roll',
                'cdDesc' => 'roll',
                'srtOrd' => 37,
            ],
            [
                'cd' => 'SET',
                'cdNm' => 'set',
                'cdDesc' => 'set',
                'srtOrd' => 38,
            ],
            [
                'cd' => 'ST',
                'cdNm' => 'sheet',
                'cdDesc' => 'sheet',
                'srtOrd' => 39,
            ],
            [
                'cd' => 'TNE',
                'cdNm' => 'tonne (metric ton)',
                'cdDesc' => 'tonne (metric ton)',
                'srtOrd' => 40,
            ],
            [
                'cd' => 'TU',
                'cdNm' => 'tube',
                'cdDesc' => 'tube',
                'srtOrd' => 41,
            ],
            [
                'cd' => 'U',
                'cdNm' => 'Pieces/item',
                'cdDesc' => 'Pieces/item',
                'srtOrd' => 42,
            ],
            [
                'cd' => 'YRD',
                'cdNm' => 'yard',
                'cdDesc' => 'yard',
                'srtOrd' => 43,
            ],
        ];


        foreach ($productQuantityUnit as $productQuantityUnit) {
            ProductQuantityUnit::create(
                [
                    'code' => $productQuantityUnit['cd'],
                    'name' => $productQuantityUnit['cdNm'],
                    'description' => $productQuantityUnit['cdDesc']
                ]
            );
        }


    }
}
