<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PaymentMode extends Model
{
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'PaymentMode';

    protected $fillable = [
        'name',
        'description',
        'code',
        'company_id',
        'forEbm',
    ];

    protected $casts = [
        'forEbm' => 'boolean',
    ];

    public function scopeWhereCompanyId($query, int $companyId)
    {
        return $query->where(function ($query) use ($companyId) {
            $query->where('company_id', $companyId)
                ->orWhereNull('company_id');
        });
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(TransactionAccount::class, 'payment_mode_id');
    }
}
