<?php

namespace App\Http\Controllers;

use App\Data\NewUserData;
use App\Data\NewUserInvitationData;
use App\Data\UpdateCompanyUserData;
use App\Http\Requests\NewUserInvitationRequest;
use App\Http\Requests\NewUserRequest;
use App\Http\Requests\UpdateCompanyUserRequest;
use App\Services\CompanyBranchService;
use App\Services\RoleService;
use App\Services\UserService;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Services\AccountSubscriptionService;
use App\Enums\SubscriptionActionEnums;
use App\Enums\AccountTypeEnums;

class UserController extends Controller
{

    protected $user;
    public function __construct(
        protected UserService $service,
        protected RoleService $roleService,
        protected CompanyBranchService $companyBranchService,
         protected AccountSubscriptionService $subscriptionService
    ) {
        $this->user = Auth::user();
    }
    public function signUp(NewUserRequest $request)
    {

        $data = NewUserData::from($request->validated());

        $this->service->storeUser($data);

        return redirect()->route('login')->banner('User created successfully');
    }

    public function companyUser()
    {

        zataAccess([AccountTypeEnums::ADMIN->value]);
        $defaultCompany = companyData();
        
        $users = $this->service->getCompanyUsers($defaultCompany->id);
        return Inertia::render('User/Index', [
            'Users' => $users,
        ]);
    }

    public function createInvitation()
    {

        zataAccess([AccountTypeEnums::ADMIN->value]);
        $defaultCompany = companyData();
        $roles = $this->roleService->getCompanyPermissions($defaultCompany->id);
        $companyBranches = $this->pagination($this->companyBranchService->getBranches($defaultCompany->id, perPage: 100, page: 1));
        return Inertia::render('User/CreateInvitation', [
            'Roles' => $roles,
            'Branches' => $companyBranches['data']->map(fn($branch) => [
                'id' => $branch['id'],
                'name' => $branch['name'],
            ]),
        ]);
    }

    public function storeInvitation(NewUserInvitationRequest $request)
    {

        zataAccess([AccountTypeEnums::ADMIN->value]);
        $defaultCompany = companyData();
        $this->subscriptionService->restrict($defaultCompany->id, SubscriptionActionEnums::CREATE_USER);
        $data = NewUserInvitationData::from($request->validated());
        $this->service->createInvitation($data, $defaultCompany, $this->user->name);

        return redirect()->route('user.company')->banner('Invitation Created Successfully');
    }

    public function acceptInvitation(int $invitationId)
    {

        $user = $this->user;
        $getInvitation = $this->service->getInvitation($invitationId, $user);
        return Inertia::render('User/AcceptInvitation', [
            'Invitation' => $getInvitation,
        ]);
    }

    public function confirmInvitation(int $invitationId)
    {
        $user = $this->user;
        $this->service->confirmInvitation($invitationId, $user);
        return redirect()->route('dashboard');
    }

    public function updateUser(int $userId)
    {

        zataAccess([AccountTypeEnums::ADMIN->value]);
        $defaultCompany = companyData();

        $companyUser = $this->service->getCompanyUser($userId, $defaultCompany->id);
        $roles = $this->roleService->getCompanyPermissions($defaultCompany->id);
        $companyBranches = $this->pagination($this->companyBranchService->getBranches($defaultCompany->id, perPage: 100, page: 1));
        return Inertia::render('User/Update', [
            'User' => $companyUser,
            'Roles' => $roles,
            'Branches' => $companyBranches['data']->map(fn($branch) => [
                'id' => $branch['id'],
                'name' => $branch['name'],
            ]),
        ]);
    }

    public function updateSave(int $userId, UpdateCompanyUserRequest $request){

        zataAccess([AccountTypeEnums::ADMIN->value]);
        $company = companyData();
        $data = UpdateCompanyUserData::from($request->validated());

        $this->service->updateCompanyUser($userId, $data, $company);

        return redirect()->route('user.company')->banner('User updated successfully');
    }
}
