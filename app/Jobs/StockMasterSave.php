<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Services\StockTrackingService;

class StockMasterSave implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $stockService;
    protected $data;
    protected $branch;

    public function __construct($data, $branch)
    {
        $this->data = $data;
        $this->branch = $branch;
        $this->stockService = app(StockTrackingService::class);
    }

    public function handle(): void
    {
        $this->stockService->stockMasterSave($this->data, $this->branch);
    }
}
