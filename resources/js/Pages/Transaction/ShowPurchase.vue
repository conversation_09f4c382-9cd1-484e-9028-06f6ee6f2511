<script setup>
import ButtonGroupOption from '@/Components/ButtonGroupOption.vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";
import { ref } from 'vue';

const props = defineProps({
    Transaction: Object,
    Company: Object,
    Branch: Object,
    Copy: Boolean,
});

const downloading = ref(false);
const errorMessage = ref('');


async function downloadFile(routeName, id, fileType) {
    downloading.value = true;
    errorMessage.value = '';

    try {
        const response = await fetch(route(routeName, id), {
            method: 'GET',
            headers: {
                'Accept': 'application/pdf',
            },
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Download failed');
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `zata-point-${fileType}-${new Date().toISOString().replace(/[:.]/g, '-')}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    } catch (error) {
        errorMessage.value = error.message;
    } finally {
        downloading.value = false;
    }
}

</script>

<template>
    <AppLayout title="Transaction">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Transaction Details
            </h2>
        </template>

        <div v-if="errorMessage" class="text-red-600 mb-4">
            {{ errorMessage }}
        </div>

        <ButtonGroupOption class="px-8">
            <button
                class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-full inline-flex items-center"
                :disabled="downloading" @click="downloadFile('transaction.purchase.download', Transaction.id, 'purchase')">
                {{ downloading ? 'Downloading...' : 'Download' }}
            </button>
        </ButtonGroupOption>

        <section id="print-section" class="flex justify-center p-4 print-only mx-auto bg-white ">
            <div class="w-full bg-white rounded mx-auto">
                <div class="border border-gray-400 m-5 p-5 rounded">
                    <section class="flex justify-between">
                        <!-- Invoice Header -->
                        <div class="py-2 border-gray-600 flex justify-start">
                            <div class="py-8">
                                <p class="font-bold text-lg text-left">
                                    {{ Company.name }}
                                </p>
                                <p class="text-left">
                                    <span class="font-bold"> {{ Company.address }} </span>
                                </p>
                                <p aria-colspan="2" class="text-left ">
                                    <span class="font-bold"> TEL: {{ Company.phone }} </span>
                                </p>
                                <p aria-colspan="2" class="text-left hidden ">
                                    <span class="font-bold"> EMAIL : {{ Company.email }} </span>
                                </p>
                                <p class="text-left ">
                                    <span class="font-bold"> TIN : {{ Company.tin }} </span>
                                </p>
                            </div>
                        </div>
                    </section>
                    <div class="">
                        <p class="font-semibold text-center">
                            {{ Branch.topMessage }}
                        </p>
                    </div>
                    <div>
                        <p>
                            <span class="font-bold">
                                Reference :
                            </span>
                            <span>
                                Served by {{ Transaction.user.name }}
                            </span>
                        </p>
                    </div>
                    <!-- Client Info -->
                    <div class="py-2 flex justify-between">
                        <div>
                            <p class="text-left py-3"> TO </p>
                            <div class="border border-1 border-gray-500 text-left px-3 py-4 w-80">
                                <p> TIN : {{ Transaction.clientTin }}</p>
                                <p> Name: {{ Transaction.clientName }} </p>
                                <p class="hidden"> Phone : {{ Transaction.clientPhoneNumber }} </p>
                            </div>

                        </div>
                        <div>
                            <p class="py-3 text-white">.</p>
                            <div class="border border-1 border-gray-500 text-left px-3 py-4 w-80">
                                <p> Purchase No : {{ Transaction.id }} </p>
                                <p> Date : {{ Transaction.salesDate }}</p>
                            </div>
                        </div>

                    </div>
                    <div class="w-full overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-300" style="min-height: 400px;">
                            <thead class="bg-gray-200">
                                <tr>
                                    <th class="text-left px-4 py-2 border border-gray-300">Item code</th>
                                    <th class="text-left px-4 py-2 border border-gray-300">Item Desc</th>
                                    <th class="text-left px-4 py-2 border border-gray-300">Qty</th>
                                    <th class="text-left px-4 py-2 border border-gray-300">Tax</th>
                                    <th class="text-left px-4 py-2 border border-gray-300">Unit Price</th>
                                    <th class="text-left px-4 py-2 border border-gray-300">Total Price</th>
                                </tr>
                            </thead>
                            <tbody class="h-full">
                                <tr v-for="(item, index) in Transaction.items" :key="index"
                                    :class="{ 'h-full': index === Transaction.items.length - 1 }" class="">
                                    <td class="text-left px-4 py-2 border border-gray-300 content-start">
                                        {{ item.productCode }}
                                    </td>
                                    <td class="text-left px-4 py-2 border border-gray-300 content-start">
                                        {{ item.productName }}
                                    </td>
                                    <td class="text-left px-4 py-2 border border-gray-300 content-start">
                                        {{ item.quantity }}
                                    </td>
                                    <td class="text-left px-4 py-2 border border-gray-300 content-start">
                                        {{ item.taxName }}
                                    </td>
                                    <td class="text-left px-4 py-2 border border-gray-300 content-start">
                                        <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span>
                                        {{ $zataCurrency(item.price) }}
                                    </td>
                                    <td class="text-left px-4 py-2 border border-gray-300 content-start">
                                        <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span>
                                        {{ $zataCurrency(item.totalAmount) }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="py-1">
                        <p class="text-center"> Version V1.0.0 Powered by RRA_VSDC </p>
                    </div>

                    <div class="font-semibold text-center max-w-lg mt-3 mx-auto">{{ Branch.bottomMessage }}</div>

                </div>
            </div>
        </section>
        <template #footer />
    </AppLayout>
</template>
