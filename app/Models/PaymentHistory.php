<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentHistory extends Model
{

    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'PaymentHistory';

    protected $fillable = [
        'user_id',
        'key',
        'amount',
        'currency',
        'gateway',
        'plan',
        'date',
        'status',
        'created_at',
        'updated_at',
    ];
}
