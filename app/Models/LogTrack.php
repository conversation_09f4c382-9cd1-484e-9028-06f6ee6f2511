<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Relations\HasOne;
use DateTimeInterface;

class LogTrack extends Model
{
    /** @use HasFactory<\Database\Factories\LogTrackFactory> */
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'LogTrack';

    protected $fillable = [
        'message',
        'action',
        'company_id',
        'branch_id',
        'user_id'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected function serializeDate(DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function getCreatedAtAttribute($value)
    {
        return \Carbon\Carbon::createFromTimestamp($value)->format('Y-m-d H:i:s');
    }

    public function user(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function branch(): HasOne
    {
        return $this->hasOne(CompanyBranch::class, 'id', 'branch_id');
    }

    public function company(): HasOne
    {
        return $this->hasOne(Company::class, 'id', 'company_id');
    }

    public function scopeFilterByDate($query, $startDate = null, $endDate = null)
    {
        if (is_null($startDate) && is_null($endDate)) {
            return $query;
        }

        try {
            $startTimestamp = null;
            if (!is_null($startDate) && !empty($startDate)) {
                $startTimestamp = \Carbon\Carbon::createFromFormat('Y-m-d', $startDate)
                    ->startOfDay()
                    ->getTimestamp(); // Convert to Unix timestamp
            }

            $endTimestamp = null;
            if (!is_null($endDate) && !empty($endDate)) {
                $endTimestamp = \Carbon\Carbon::createFromFormat('Y-m-d', $endDate)
                    ->endOfDay()
                    ->getTimestamp(); // Convert to Unix timestamp
            }

            if ($startTimestamp && $endTimestamp) {
                return $query->whereBetween('created_at', [$startTimestamp, $endTimestamp]);
            } elseif ($startTimestamp) {
                return $query->where('created_at', '>=', $startTimestamp);
            } elseif ($endTimestamp) {
                return $query->where('created_at', '<=', $endTimestamp);
            }

            return $query;
        } catch (\Exception $e) {
            Log::error('Date filter error: ' . $e->getMessage());
            return $query;
        }
    }
}
