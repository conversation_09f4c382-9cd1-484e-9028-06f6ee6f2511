<script setup>
import ButtonGroupOption from '@/Components/ButtonGroupOption.vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";
import QrcodeVue from 'qrcode.vue';
import SecondaryLink from '@/Components/SecondaryLink.vue';
import { ref } from 'vue';

const props = defineProps({
    Transaction: Object,
    Company: Object,
    Branch: Object,
    Copy: Boolean,
});

const downloading = ref(false);
const downloading2 = ref(false);
const errorMessage = ref('');

function formatSignature(signature) {
    if (!signature) return '';
    return signature.match(/.{1,4}/g).join('-');
}

async function downloadFile(routeName, id, fileType) {
    downloading.value = true;
    errorMessage.value = '';

    try {
        const response = await fetch(route(routeName, id), {
            method: 'GET',
            headers: {
                'Accept': 'application/pdf',
            },
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Download failed');
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `zata-point-${fileType}-${new Date().toISOString().replace(/[:.]/g, '-')}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    } catch (error) {
        errorMessage.value = error.message;
    } finally {
        downloading.value = false;
    }
}

async function downloadFile2(routeName, id, fileType) {
    downloading2.value = true;
    errorMessage.value = '';

    try {
        const response = await fetch(route(routeName, id), {
            method: 'GET',
            headers: {
                'Accept': 'application/pdf',
            },
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Download failed');
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `zata-point-${fileType}-${new Date().toISOString().replace(/[:.]/g, '-')}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    } catch (error) {
        errorMessage.value = error.message;
    } finally {
        downloading2.value = false;
    }
}

function truncateName(name, maxLength = 30) {
    if (name.length > maxLength) {
        return name.substring(0, maxLength) + ' ...';
    }
    return name;
}

function formatNumber(value) {
    if (value === null || value === undefined) return '0.00';
    return Number(value).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}
</script>

<template>
    <AppLayout title="Transaction">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Transaction Details
            </h2>
        </template>

        <div v-if="errorMessage" class="text-red-600 mb-4">
            {{ errorMessage }}
        </div>

        <ButtonGroupOption class="px-8">
            <button
                class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-full inline-flex items-center"
                :disabled="downloading" @click="downloadFile('transaction.download.small', Transaction.id, 'invoice')">
                {{ downloading ? 'Downloading...' : 'Download' }}
            </button>

            <SecondaryLink :href="route('transaction.refundSale', Transaction.id)"
                v-if="!Transaction.isRefunded && Transaction.transactionType == 'sales'">
                Refund
            </SecondaryLink>

            <button
                class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-full inline-flex items-center"
                :disabled="downloading2"
                @click="downloadFile2('transaction.download.deliveryNote', Transaction.id, 'delivery-note')"
                v-if="!Transaction.isRefunded && (Transaction.transactionType == 'sales' || Transaction.transactionType == 'proforma')">
                {{ downloading2 ? 'Downloading...' : 'Delivery note' }}
            </button>

            <SecondaryLink :href="route('transaction.proforma.convert', Transaction.id)"
                v-if="!Transaction.isRefunded && Transaction.type == 'PS'">
                Convert to sale
            </SecondaryLink>
        </ButtonGroupOption>

        <section id="print-section" class="flex justify-center p-4 print-only mx-auto">
            <div class="max-w-lg w-full bg-white rounded mx-auto">
                <div class="border border-black m-5 p-5 rounded">

                    <!-- Invoice Header -->
                    <div class="border-b border-dotted py-2 border-gray-600">
                        <div class="flex justify-between">
                            <img src="/ebm_logo_2.png" alt="logo" class="w-20">
                            <img src="/ebm_logo_1.png" alt="logo" class="w-20">
                        </div>
                        <div class="py-8">
                            <p class="font-bold text-lg text-left">
                                {{ Company.name }}
                            </p>
                            <p class="text-left">
                                <span class="font-bold"> {{ Company.address }} </span>
                            </p>
                            <p aria-colspan="2" class="text-left ">
                                <span class="font-bold"> TEL: {{ Company.phone }} </span>
                            </p>
                            <p aria-colspan="2" class="text-left hidden ">
                                <span class="font-bold"> EMAIL : {{ Company.email }} </span>
                            </p>
                            <p class="text-left ">
                                <span class="font-bold"> TIN : {{ Company.tin }} </span>
                            </p>
                            <p class="text-left py-2" v-if="Copy">
                                <span class="font-bold"> COPY </span>
                            </p>
                            <p class="text-left py-1">
                                <span v-if="Transaction.type == 'NS'" class="hidden">SALES</span>
                                <span v-if="Transaction.type == 'TS'">TRAINING SALES</span>
                                <span v-if="Transaction.type == 'TR'">TRAINING REFUND</span>
                                <span v-if="Transaction.type == 'PS'">PROFORMA</span>
                            </p>
                            <div class="text-left font-bold uppercase"
                                v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">
                                <span v-if="Transaction.type == 'NR'">REFUND</span> <br>
                                REF. NORMAL RECEIPT : {{ Transaction.originalInvoiceNumber }} <br>
                                <hr class="border-dotted border-black" />
                                <p>REFUND IS APPROVED ONLY FOR ORIGINAL SALES RECEIPT</p>
                            </div>
                        </div>
                    </div>

                    <div v-if="Transaction.party.patient_details != null">
                        <p v-if="Transaction.party.patient_details.hasAffiliation"> Affiliate No: {{
                            Transaction.party.patient_details.affiliationNumber }} </p>
                        <p v-if="Transaction.party.patient_details.hasAffiliation"> Affiliate Name: {{
                            Transaction.party.patient_details.affiliateFirstName }} {{
                                Transaction.party.patient_details.affiliateLastName }} </p>
                        <p v-if="Transaction.party.patient_details.hasAffiliation"> Relationship: {{
                            Transaction.party.patient_details.relationship }} </p>

                        <p> Beneficial No: {{ Transaction.party.patient_details.beneficiaryNumber }}</p>
                        <p> Beneficial Name: {{ Transaction.party.patient_details.beneficiaryFirstName }} {{
                            Transaction.party.patient_details.beneficiaryLastName }} </p>
                        <p> Date of birth: {{ Transaction.party.patient_details.dateOfBirth }} </p>
                        <p> Department: {{ Transaction.party.patient_details.department }} </p>
                        <p v-if="Transaction.prescriptionNumber != null"> Prescription No: {{
                            Transaction.prescriptionNumber }}
                        </p>
                        <p> Insurance: {{ Transaction.party.patient_details.insurance.name }} </p>
                        <p v-if="Transaction.insuranceTin != null"> Insurance TIN: {{ Transaction.insuranceTin }} </p>
                    </div>

                    <div class="py-3">
                        <p class="font-semibold text-center">
                            {{ Branch.topMessage }}
                        </p>
                    </div>

                    <div>
                        <p>
                            <span class="font-bold">
                                Reference :
                            </span>
                            <span>
                                Served by {{ Transaction.user.name }}
                            </span>
                        </p>
                    </div>

                    <!-- Client Info -->
                    <div class="py-2 border-b border-dotted border-black">
                        <p class="">
                        <p>Customer</p>
                        <p> <span class="font-bold"> TIN</span> : {{ Transaction.clientTin ?? ' - ' }}</p>
                        <p> <span class="font-bold"> Name:</span> {{ Transaction.clientName ?? ' - ' }} </p>
                        <p class=""> <span class="font-bold"> Phone : </span> {{ Transaction.clientPhoneNumber ?? ' - '
                            }} </p>

                        </p>
                    </div>
                    <!-- Items Info -->
                    <div v-for="(item, index) in Transaction.items" :key="index"
                        :class="{ 'h-full': index === Transaction.items.length - 1 }" class="py-2">
                        {{ truncateName(item.productName) }}
                        <div class="mb-2 hidden">
                            <div class="font-bold"> {{ formatNumber(item.price) }} </div>
                        </div>
                        <div class="flex justify-between mb-2">
                            <div class="text-gray-600">
                                {{ formatNumber(item.price) }} X
                            </div>
                            <div class="text-gray-600"> {{ item.quantity }} </div>
                            <div class="">
                                <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span>
                                <span v-if="item.discount > 0">
                                    {{ formatNumber(parseInt(item.totalAmount) + (parseInt(item.totalDiscount)))
                                    }}
                                </span>
                                <span v-else>
                                    {{ formatNumber(item.totalAmount) }}
                                </span>
                                &nbsp;
                                <span v-if="item.taxName == 'A'">A-EX</span>
                                <span v-if="item.taxName == 'B'">B</span>
                                <span v-if="item.taxName == 'C'">C</span>
                                <span v-if="item.taxName == 'D'">D</span>

                            </div>
                        </div>

                        <div class="flex justify-between mb-2" v-if="item.discount > 0">
                            <div class="text-gray-600">Discount &nbsp; {{ item.discount }} %</div>
                            <div class=""> <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span> {{
                                formatNumber(item.totalAmount) }} </div>
                        </div>
                    </div>

                    <hr class="border-dotted border-black" />
                    <div class="border-b border-dotted py-2 border-gray-600"
                        v-if="Transaction.type == 'PS' || Transaction.type == 'TS' || Transaction.type == 'TR' || Copy">
                        <p class="text-center font-black">
                            THIS IS NOT AN OFFICIAL RECEIPT
                        </p>
                    </div>

                    <!-- Totals -->
                    <div class="border-b border-dotted py-2 border-gray-600">
                        <div class="flex justify-between items-center">
                            <div class="font-bold">TOTAL</div>
                            <div class="font-semibold">
                                <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span> {{
                                formatNumber(Transaction.totAmt) }}
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-2" v-if="Transaction.taxblAmtA > 0">
                            <div class="">TOTAL A-EX</div>
                            <div class="text-gray-600">
                                <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span> {{
                                formatNumber(Transaction.taxblAmtA) }}
                            </div>
                        </div>

                        <div class="flex justify-between items-center mt-2" v-if="Transaction.taxAmtB > 0">
                            <div class="">TOTAL B-18%</div>
                            <div class="text-gray-600">
                                <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span> {{
                                formatNumber(Transaction.taxblAmtB) }}
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-2"
                            v-if="Transaction.taxAmtB > 0 || Transaction.taxAmtB < 0">
                            <div class="">TOTAL TAX B</div>
                            <div class="text-gray-600">
                                <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span> {{
                                formatNumber(Transaction.taxAmtB) }}
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-2" v-if="Transaction.taxblAmtC > 0">
                            <div class="">TOTAL C</div>
                            <div class="text-gray-600">
                                <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span> {{
                                formatNumber(Transaction.taxblAmtC) }}
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-2" v-if="Transaction.taxblAmtD > 0">
                            <div class="">TOTAL D</div>
                            <div class="text-gray-600">
                                <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span> {{
                                formatNumber(Transaction.taxblAmtD) }}
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-2">
                            <div class="">TOTAL TAX</div>
                            <div class="text-gray-600">
                                <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span> {{
                                formatNumber(Transaction.totTaxAmt) }}
                            </div>
                        </div>
                    </div>

                    <!-- Totals 2 -->
                    <div class="border-b border-dotted py-2 border-gray-600">
                        <div class="flex justify-between items-center">
                            <div class="font-bold">
                                <span v-if="Transaction.paymentTypeCode == '01'">CASH</span>
                                <span v-if="Transaction.paymentTypeCode == '02'">CREDIT</span>
                                <span v-if="Transaction.paymentTypeCode == '03'">CASH/CREDIT</span>
                                <span v-if="Transaction.paymentTypeCode == '04'">BANK CHECK</span>
                                <span v-if="Transaction.paymentTypeCode == '05'">DEBIT & CREDIT CARD</span>
                                <span v-if="Transaction.paymentTypeCode == '06'">MOBILE MONEY</span>
                                <span v-if="Transaction.paymentTypeCode == '07'">OTHER</span>
                            </div>
                            <div class="text-gray-600">
                                <span v-if="Transaction.type == 'NR' || Transaction.type == 'TR'">-</span> {{
                                formatNumber(Transaction.totAmt) }}
                            </div>
                        </div>

                        <div class="flex justify-between items-center mt-2">
                            <div class="font-bold">ITEMS NUMBER</div>
                            <div class="text-gray-600"> {{ Transaction.items.length }}</div>
                        </div>
                    </div>

                    <p class="text-center border-b border-dotted py-2 border-gray-600" v-if="Copy">
                        <span class="font-bold"> COPY </span>
                    </p>

                    <!-- Training Mode -->
                    <div class="text-center py-4" v-if="Transaction.salesTypeCode == 'T'">
                        <span class="font-bold"> TRAINING MODE </span>
                        <hr class="border-dotted border-black">
                    </div>

                    <!-- Proforma -->
                    <div class="text-center py-4" v-if="Transaction.salesTypeCode == 'P'">
                        <span class="font-bold"> PROFORMA </span>
                        <hr class="border-dotted border-black">
                    </div>

                    <!-- SDC Information -->
                    <div class="border-b border-dotted py-2 border-gray-600"
                        v-if="Transaction.signature != null && Transaction.signature !== ''">
                        <div class="font-bold text-center">SDC INFORMATION</div>
                        <div class="flex justify-between items-center mt-2">
                            <div class="text-gray-600">
                                Date: {{ new
                                    Date(Transaction.signature.vsdcReceiptPublishDate).toLocaleDateString() }}
                            </div>
                            <div class="text-gray-600">
                                Time: {{ new
                                    Date(Transaction.signature.vsdcReceiptPublishDate).toLocaleTimeString() }}
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-2">
                            <div class="font-semibold text-gray-600">SDC ID:</div>
                            <div class="text-gray-600">
                                {{ Transaction.signature.sdcId }}
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-2">
                            <div class="font-semibold text-gray-600">RECEIPT NUMBER:</div>
                            <div class="text-gray-600">
                                {{ Transaction.signature.receiptNumber }} / {{
                                    Transaction.signature.totalReceiptNumber
                                }} &nbsp;
                                &nbsp;
                                <!-- <span v-if="Copy">
                                    <span v-if="Transaction.type == 'NS'"> CS</span>
                                    <span v-if="Transaction.type == 'NR'"> CR</span>
                                </span>
                                <span v-else>
                                    {{ Transaction.type }}
                                </span> -->

                                <span>
                                    {{ Transaction.type }}
                                </span>
                            </div>
                        </div>

                        <div class="flex justify-between items-center mt-2">
                            <div class="font-semibold text-gray-600">Internal Data:</div>
                            <div class="text-gray-600 text-sm">
                                <span
                                    v-if="Transaction.signature.internalData && Transaction.signature.internalData != null">
                                    {{ formatSignature(Transaction.signature.internalData) }}
                                </span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-2">
                            <div class="font-semibold text-gray-600">Receipt Signature:</div>
                            <div class="text-gray-600 text-sm">
                                <span
                                    v-if="Transaction.signature.receiptSignature && Transaction.signature.receiptSignature != null">
                                    {{ formatSignature(Transaction.signature.receiptSignature) }}
                                </span>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-center p-1 mt-2"
                                v-if="Transaction.salesTypeCode !== 'P' && Transaction.signature != null">
                                <qrcode-vue
                                    v-if="Transaction.signature.internalData != null && Transaction.signature.receiptSignature != null"
                                    :value="`#${new Date(Transaction.signature.vsdcReceiptPublishDate).toLocaleDateString()} #${new Date(Transaction.signature.vsdcReceiptPublishDate).toLocaleTimeString()} #${Transaction.signature.sdcId} #${Transaction.signature.receiptNumber} / ${Transaction.signature.totalReceiptNumber} ${Transaction.type} #${formatSignature(Transaction.signature.internalData)} #${formatSignature(Transaction.signature.receiptSignature)}`"
                                    :size="130" level="H" />
                            </div>
                        </div>
                    </div>
                    <div class="border-b border-dotted py-2 border-gray-600" v-if="Transaction.ebm != null">
                        <div class="flex justify-between items-center mt-2">
                            <div class="text-gray-600">RECEIPT NUMBER</div>
                            <div class="text-gray-600">{{ Transaction.signature.invoiceNumber }}</div>
                        </div>
                        <div class="flex justify-between items-center mt-2">
                            <div class="text-gray-600">
                                Date: {{ new
                                    Date(Transaction.signature.vsdcReceiptPublishDate).toLocaleDateString() }}
                            </div>
                            <div class="text-gray-600">
                                Time: {{ new
                                    Date(Transaction.signature.vsdcReceiptPublishDate).toLocaleTimeString() }}
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-2">
                            <div class="text-gray-600">MRC:</div>
                            <div class="text-gray-600">
                                {{ Branch.mrc }}
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-2 hidden">
                            <div class="font-semibold">CIS Version :</div>
                            <div class="text-gray-600">V1.0.0 </div>
                        </div>
                    </div>

                    <!-- Thank You Message -->
                    <div class="py-1">
                        <div class="font-bold text-center">{{ Branch.bottomMessage }}</div>
                        <p class="text-center"> Version V1.0.0 Powered by RRA_VSDC </p>
                    </div>
                </div>
            </div>
        </section>
    </AppLayout>
</template>
