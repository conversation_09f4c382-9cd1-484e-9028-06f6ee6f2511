<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Lara<PERSON>\Jetstream\Jetstream;

class NewUserRequest extends FormRequest
{

    use PasswordValidationRules;
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => $this->passwordRules(),
            'terms' => Jetstream::hasTermsAndPrivacyPolicyFeature() ? ['accepted', 'required'] : '',
            'affiliateCode' => ['nullable', 'string', 'max:50', 'min:3'],
        ];
    }
}
