<?php

use App\Enums\PartyTypeEnums;
use App\Enums\TransactionTypeEnums;
use App\Models\Company;
use App\Models\CompanyBranch;
use App\Models\CompanyParty;
use App\Models\Order;
use App\Models\OrderItems;
use App\Models\PartyType;
use App\Models\PaymentMode;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Models\Tax;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use App\Models\Role;
use App\Models\RolePermission;
use Illuminate\Support\Facades\Http;

beforeEach(function () {

    $this->user = User::factory()->create([
        'whiteLabel' => 'admin',
    ]);

    $this->company = Company::factory()->create(["user_id" =>  $this->user->id, "isEBM" => true]);
    $this->branch = CompanyBranch::factory()->create(["company_id" => $this->company->id, 'isEBM' => true]);

    $this->permission = RolePermission::factory()->create();
    $this->role = Role::factory()->create([
        'user_id' => $this->user->id,
        'company_id' => $this->company->id,
        'role_permission_id' => $this->permission
    ]);

    $this->apiUrl = '/api/v1/transaction/sale';
    $this->mainUrl = '/api/v1/transaction';
    $this->adminUrl = '/admin';

    $tax = [['code' => 'A', 'rate' => 0], ['code' => 'B', 'rate' => 18], ['code' => 'C', 'rate' => 0], ['code' => 'D', 'rate' => 0]];

    foreach ($tax as $item) {
        Tax::factory()->create([
            'code' => $item['code'],
            'rate' => $item['rate']
        ]);
    }

    Sanctum::actingAs($this->user);
});

it("can create sale transaction ", function (array $payload) {

    // TODO, moving unit on sale transaction
    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $response = $this->postJson($this->apiUrl, $payload);

    $response->assertStatus(200)
        ->assertJson(['message' => 'Sale transaction created successfully'])
        ->assertExactJsonStructure(['message', 'invoiceID']);
})->with([
    function () {

        $product = Product::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 100,
            'batchNumber' => '*********'
        ]);

        $companyParty = CompanyParty::factory()->create([
            'company_id' => $this->company->id,
            'party_type_id' => PartyType::where('name', PartyTypeEnums::CUSTOMER->value)->first()->id,
        ]);

        return [
            'customerID' => $companyParty->id,
            'purchaseCode' => '*********',
            'paymentMethodID' => PaymentMode::factory()->create(['forEbm' => true])->id,
            'transactionDate' => now()->format('Y-m-d'),
            'note' => 'Note',
            'items' => [
                [
                    'productID' => $product->id,
                    'units' => 10,
                    'unitPrice' => 1000,
                    'discountRate' => 10,
                    'batchNumber' => '*********',
                    'movingUnit' => 'sub'
                ]
            ]
        ];
    }
]);

it("can throw errors during create sale transaction ", function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $ebmData = $payload['ebm'];
    $response = $this->postJson($this->apiUrl, $ebmData);

    $response->assertStatus(404)
        ->assertJson([
            'message' => $payload['message'],
        ]);
})->with([
    function () {

        $product = Product::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        $stock =    ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 100,
        ]);

        return [
            'ebm' => [
                'purchaseCode' => '*********',
                'paymentMethodID' => 90000,
                'transactionDate' => now()->format('Y-m-d'),
                'note' => 'Note',
                'items' => [
                    [
                        'productID' => $product->id,
                        'units' => 10,
                        'unitPrice' => 1000,
                        'discountRate' => 10,
                        'batchNumber' => $stock->batchNumber,
                        'movingUnit' => 'sub'
                    ]
                ]
            ],
            'message' => 'Payment method not found',
        ];
    },

    function () {

        $product = Product::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 100,
        ]);

        return [
            'ebm' => [
                'purchaseCode' => '*********',
                'paymentMethodID' => PaymentMode::factory()->create([
                    'forEbm' => true
                ])->id,
                'transactionDate' => now()->format('Y-m-d'),
                'note' => 'Note',
                'items' => [
                    [
                        'productID' => 1212,
                        'units' => 10,
                        'unitPrice' => 1000,
                        'discountRate' => 10,
                        'batchNumber' => '*********',
                        'movingUnit' => 'sub'
                    ]
                ]
            ],
            'message' => 'Product not found',

        ];
    },

]);

it("can create tax calculation", function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $response = $this->postJson('api/v1/transaction/calculate', $payload);

    $response->assertStatus(200)
        ->assertExactJsonStructure([
            '*' => [
                'id',
                'code',
                'name',
                'class',
                'units',
                'unitPrice',
                'batchNumber',
                'movingUnit',
                'packagingUnit',
                'quantityUnit',
                'discountAmount',
                'discountRate',
                'taxAmount',
                'taxRate',
                'taxableAmount',
                'taxName',
                'expireDate'
            ]
        ]);
})->with([
    function () {

        $product = Product::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 100,
            'batchNumber' => '*********',
        ]);

        return [
            'items' => [
                [
                    'productID' => $product->id,
                    'units' => 10,
                    'unitPrice' => 1000,
                    'discountRate' => 10,
                    'batchNumber' => '*********',
                ]
            ]

        ];
    }
]);

it('can get invoice by invoicenumber', function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $response = $this->get("api/v1/transaction/{$payload['invoiceNumber']}");

    $response->assertStatus(200)
        ->assertExactJsonStructure([
            'id',
            'invoiceNumber',
            'originalInvoiceNumber',
            'clientTin',
            'clientName',
            'clientPhoneNumber',
            'salesTypeCode',
            'receiptTypeCode',
            'paymentTypeCode',
            'salesStatusCode',
            'confirmationDate',
            'type',
            'isRefunded',
            'taxblAmtA',
            'taxblAmtB',
            'taxblAmtC',
            'taxblAmtD',
            'taxAmtA',
            'taxAmtB',
            'taxAmtC',
            'taxAmtD',
            'totTaxblAmt',
            'totTaxAmt',
            'totAmt',
            'salesDate',
            'note',
            'items' => [
                '*' => [
                    'productName',
                    'units',
                    'unitPrice',
                    'taxAmount',
                    'taxRate',
                    'taxName',
                    'totalAmount',
                    'totalDiscount',
                    'discountRate',
                ]
            ]
        ]);
})->with([
    function () {

        $product = Product::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 100,
        ]);

        $order = Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
        ]);

        OrderItems::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
        ]);

        return [
            'invoiceNumber' => $order->id
        ];
    }
]);

it('can get all invoices', function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $response = $this->get(
        "{$this->mainUrl}?invoiceType={$payload['invoiceType']}&perPage={$payload['perPage']}&page={$payload['page']}",
    );

    $response->assertStatus(200)
        ->assertExactJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'invoiceNumber',
                    'clientName',
                    'salesDate',
                    'totalAmount',
                    'synced',
                    'isRefunded',
                    'status',
                    'itemsCount',
                    'invoiceType',
                ]
            ],
            'currentPage',
            'lastPage',
            'itemsPerPage',
            'pageItems',
            'total',
        ]);

    expect($response->json('data'))->toHaveCount(2);
    expect($response->json('currentPage'))->toEqual($payload['page']);
    expect($response->json('lastPage'))->toEqual(1);
    expect($response->json('itemsPerPage'))->toEqual($payload['perPage']);
    expect($response->json('pageItems'))->toEqual(2);
    expect($response->json('total'))->toEqual(2);
})->with([

    function () {
        $order1 =   Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
            'transactionType' => TransactionTypeEnums::SALES->value
        ]);

        $order2 =   Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
            'transactionType' => TransactionTypeEnums::SALES->value
        ]);

        $order3 =   Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
            'transactionType' =>   TransactionTypeEnums::PROFORMA->value,
        ]);

        OrderItems::factory()->create([
            'order_id' => $order1->id,
            'product_id' => Product::factory()->create([
                'branch_id' => $this->branch->id,
            ])->id,
        ]);

        OrderItems::factory()->count(4)->create([
            'order_id' => $order2->id,
            'product_id' => Product::factory()->create([
                'branch_id' => $this->branch->id,
            ])->id,
        ]);

        OrderItems::factory()->count(4)->create([
            'order_id' => $order3->id,
            'product_id' => Product::factory()->create([
                'branch_id' => $this->branch->id,
            ])->id,
        ]);

        return [
            'invoiceType' => TransactionTypeEnums::SALES->value,
            'perPage' => 10,
            'page' => 1
        ];
    }
]);

it("can generate PDF url for transaction ", function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $response = $this->get("$this->mainUrl/download-signature/{$payload['invoiceNumber']}");

    $response->assertStatus(200)->assertJsonStructure(['url', 'expires_at']);

    $pdf = $response->json('url');
    $streamedPdf =   $this->get($pdf);

    $streamedPdf->assertHeader('Content-Type', 'application/pdf');
})->with([
    function () {
        $order = Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
            'transactionType' => TransactionTypeEnums::SALES->value
        ]);

        return [
            'invoiceNumber' => $order->id,
        ];
    }
]);



it('can admin search invoice', function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $response = $this->get(
        "{$this->adminUrl}/search-json?invoiceType={$payload['invoiceType']}&startDate={$payload['startDate']}&endDate={$payload['endDate']}",
    );

    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'invoiceNumber',
                    'clientName',
                    'salesDate',
                ]
            ],
            'currentPage',
            'lastPage',
            'itemsPerPage',
            'pageItems',
            'total',
        ]);
})->with([
    function () {

        $order1 =   Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
            'transactionType' => TransactionTypeEnums::SALES->value
        ]);

        $order2 =   Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
            'transactionType' => TransactionTypeEnums::SALES->value
        ]);

        $order3 =   Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
            'transactionType' =>   TransactionTypeEnums::PROFORMA->value,
        ]);

        OrderItems::factory()->create([
            'order_id' => $order1->id,
            'product_id' => Product::factory()->create([
                'branch_id' => $this->branch->id,
            ])->id,
        ]);

        OrderItems::factory()->count(4)->create([
            'order_id' => $order2->id,
            'product_id' => Product::factory()->create([
                'branch_id' => $this->branch->id,
            ])->id,
        ]);

        OrderItems::factory()->count(4)->create([
            'order_id' => $order3->id,
            'product_id' => Product::factory()->create([
                'branch_id' => $this->branch->id,
            ])->id,
        ]);

        $searchQuery = 'invoice';

        return [
            'perPage' => 10,
            'page' => 1,
            'searchQuery' => $searchQuery,
            'invoiceType' => TransactionTypeEnums::SALES->value,
            'startDate' => '2022-01-01',
            'endDate' => '2025-12-31',
        ];
    }
]);
