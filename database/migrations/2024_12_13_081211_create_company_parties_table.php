<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('CompanyParty', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->nullable()->references('id')->on('Company')->onDelete('restrict');
            $table->foreignId('party_type_id')->references('id')->on('PartyType')->onDelete('restrict');
            $table->foreignId('account_id')->nullable()->references('id')->on('users')->onDelete('restrict');
            $table->string('slug');
            $table->string('name');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('address')->nullable();
            $table->string('tin')->nullable();
            $table->boolean('isActive')->default(true);
             $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('CompanyParty');
    }
};
