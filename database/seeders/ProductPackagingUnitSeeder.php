<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ProductPackingUnit;

class ProductPackagingUnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {


        $productPackagingUnit = [
            [
                'cd' => 'BZ',
                'cdNm' => 'Bag',
                'cdDesc' => 'Bag',
                'srtOrd' => 13,
            ],
            [
                'cd' => 'AM',
                'cdNm' => 'Ampoule',
                'cdDesc' => 'Ampoule',
                'srtOrd' => 1,
            ],
            [
                'cd' => 'BA',
                'cdNm' => 'Barrel',
                'cdDesc' => 'Barrel',
                'srtOrd' => 2,
            ],
            [
                'cd' => 'BC',
                'cdNm' => 'Bottlecrate',
                'cdDesc' => 'Bottlecrate',
                'srtOrd' => 3,
            ],
            [
                'cd' => 'BE',
                'cdNm' => 'Bundle',
                'cdDesc' => 'Bundle',
                'srtOrd' => 4,
            ],
            [
                'cd' => 'BF',
                'cdNm' => 'Balloon, non-protected',
                'cdDesc' => 'Balloon, non-protected',
                'srtOrd' => 5,
            ],
            [
                'cd' => 'BG',
                'cdNm' => 'Bag',
                'cdDesc' => 'Bag',
                'srtOrd' => 6,
            ],
            [
                'cd' => 'BJ',
                'cdNm' => 'Bucket',
                'cdDesc' => 'Bucket',
                'srtOrd' => 7,
            ],
            [
                'cd' => 'BK',
                'cdNm' => 'Basket',
                'cdDesc' => 'Basket',
                'srtOrd' => 8,
            ],
            [
                'cd' => 'BL',
                'cdNm' => 'Bale',
                'cdDesc' => 'Bale',
                'srtOrd' => 9,
            ],
            [
                'cd' => 'BQ',
                'cdNm' => 'Bottle, protected cylindrical',
                'cdDesc' => 'Bottle, protected cylindrical',
                'srtOrd' => 10,
            ],
            [
                'cd' => 'BR',
                'cdNm' => 'Bar',
                'cdDesc' => 'Bar',
                'srtOrd' => 11,
            ],
            [
                'cd' => 'BV',
                'cdNm' => 'Bottle, bulbous',
                'cdDesc' => 'Bottle, bulbous',
                'srtOrd' => 12,
            ],

            [
                'cd' => 'CA',
                'cdNm' => 'Can',
                'cdDesc' => 'Can',
                'srtOrd' => 14,
            ],
            [
                'cd' => 'CH',
                'cdNm' => 'Chest',
                'cdDesc' => 'Chest',
                'srtOrd' => 15,
            ],
            [
                'cd' => 'CJ',
                'cdNm' => 'Coffin',
                'cdDesc' => 'Coffin',
                'srtOrd' => 16,
            ],
            [
                'cd' => 'CL',
                'cdNm' => 'Coil',
                'cdDesc' => 'Coil',
                'srtOrd' => 17,
            ],
            [
                'cd' => 'CR',
                'cdNm' => 'Wooden Box, Wooden Case',
                'cdDesc' => 'Wooden Box, Wooden Case',
                'srtOrd' => 18,
            ],
            [
                'cd' => 'CS',
                'cdNm' => 'Cassette',
                'cdDesc' => 'Cassette',
                'srtOrd' => 19,
            ],
            [
                'cd' => 'CT',
                'cdNm' => 'Carton',
                'cdDesc' => 'Carton',
                'srtOrd' => 20,
            ],
            [
                'cd' => 'CTN',
                'cdNm' => 'Container',
                'cdDesc' => 'Container',
                'srtOrd' => 21,
            ],
            [
                'cd' => 'CY',
                'cdNm' => 'Cylinder',
                'cdDesc' => 'Cylinder',
                'srtOrd' => 22,
            ],
            [
                'cd' => 'DR',
                'cdNm' => 'Drum',
                'cdDesc' => 'Drum',
                'srtOrd' => 23,
            ],
            [
                'cd' => 'GT',
                'cdNm' => 'Extra Countable Item',
                'cdDesc' => 'Extra Countable Item',
                'srtOrd' => 24,
            ],
            [
                'cd' => 'HH',
                'cdNm' => 'Hand Baggage',
                'cdDesc' => 'Hand Baggage',
                'srtOrd' => 25,
            ],
            [
                'cd' => 'IZ',
                'cdNm' => 'Ingots',
                'cdDesc' => 'Ingots',
                'srtOrd' => 26,
            ],
            [
                'cd' => 'JR',
                'cdNm' => 'Jar',
                'cdDesc' => 'Jar',
                'srtOrd' => 27,
            ],
            [
                'cd' => 'JU',
                'cdNm' => 'Jug',
                'cdDesc' => 'Jug',
                'srtOrd' => 28,
            ],
            [
                'cd' => 'JY',
                'cdNm' => 'Jerry CAN Cylindrical',
                'cdDesc' => 'Jerry CAN Cylindrical',
                'srtOrd' => 29,
            ],
            [
                'cd' => 'KZ',
                'cdNm' => 'Canester',
                'cdDesc' => 'Canester',
                'srtOrd' => 30,
            ],
            [
                'cd' => 'LZ',
                'cdNm' => 'Logs, in bundle/bunch/truss',
                'cdDesc' => 'Logs, in bundle/bunch/truss',
                'srtOrd' => 31,
            ],
            [
                'cd' => 'NT',
                'cdNm' => 'Net',
                'cdDesc' => 'Net',
                'srtOrd' => 32,
            ],
            [
                'cd' => 'OU',
                'cdNm' => 'Non-Exterior Packaging Unit',
                'cdDesc' => 'Non-Exterior Packaging Unit',
                'srtOrd' => 33,
            ],
            [
                'cd' => 'PD',
                'cdNm' => 'Poddon',
                'cdDesc' => 'Poddon',
                'srtOrd' => 34,
            ],
            [
                'cd' => 'PG',
                'cdNm' => 'Plate',
                'cdDesc' => 'Plate',
                'srtOrd' => 35,
            ],
            [
                'cd' => 'PI',
                'cdNm' => 'Pipe',
                'cdDesc' => 'Pipe',
                'srtOrd' => 36,
            ],
            [
                'cd' => 'PO',
                'cdNm' => 'Pilot',
                'cdDesc' => 'Pilot',
                'srtOrd' => 37,
            ],
            [
                'cd' => 'PU',
                'cdNm' => 'Traypack',
                'cdDesc' => 'Traypack',
                'srtOrd' => 38,
            ],
            [
                'cd' => 'RL',
                'cdNm' => 'Reel',
                'cdDesc' => 'Reel',
                'srtOrd' => 39,
            ],
            [
                'cd' => 'RO',
                'cdNm' => 'Roll',
                'cdDesc' => 'Roll',
                'srtOrd' => 40,
            ],
            [
                'cd' => 'RZ',
                'cdNm' => 'Rods, in bundle/bunch/truss',
                'cdDesc' => 'Rods, in bundle/bunch/truss',
                'srtOrd' => 41,
            ],
            [
                'cd' => 'SK',
                'cdNm' => 'Skeletoncase',
                'cdDesc' => 'Skeletoncase',
                'srtOrd' => 42,
            ],
            [
                'cd' => 'TY',
                'cdNm' => 'Tank, cylindrical',
                'cdDesc' => 'Tank, cylindrical',
                'srtOrd' => 43,
            ],
            [
                'cd' => 'VG',
                'cdNm' => 'Bulk,gas(at 1031 mbar 15 oC)',
                'cdDesc' => 'Bulk,gas(at 1031 mbar 15 oC)',
                'srtOrd' => 44,
            ],
            [
                'cd' => 'VL',
                'cdNm' => 'Bulk,liquid(at normal temperature/pressure)',
                'cdDesc' => 'Bulk,liquid(at normal temperature/pressure)',
                'srtOrd' => 45,
            ],
            [
                'cd' => 'VO',
                'cdNm' => 'Bulk, solid, large particles(nodules)',
                'cdDesc' => 'Bulk, solid, large particles("nodules")',
                'srtOrd' => 46,
            ],
            [
                'cd' => 'VQ',
                'cdNm' => 'Bulk, gas (liquefied at abnormal temperature/pressure)',
                'cdDesc' => 'Bulk, gas(liquefied at abnormal temperature/pressure)',
                'srtOrd' => 47,
            ],
            [
                'cd' => 'VR',
                'cdNm' => 'Bulk, solid, granular particles(grains)',
                'cdDesc' => 'Bulk, solid, granular particles("grains")',
                'srtOrd' => 48,
            ],
            [
                'cd' => 'VT',
                'cdNm' => 'Extra Bulk Item',
                'cdDesc' => 'Extra Bulk Item',
                'srtOrd' => 49,
            ],
            [
                'cd' => 'VY',
                'cdNm' => 'Bulk, fine particles(powder)',
                'cdDesc' => 'Bulk, fine particles("powder")',
                'srtOrd' => 50,
            ],
            [
                'cd' => 'ML',
                'cdNm' => 'Mills',
                'cdDesc' => 'cigarette Mills',
                'srtOrd' => 51,
            ],
            [
                'cd' => 'TN',
                'cdNm' => 'TAN',
                'cdDesc' => '1TAN REFER TO 20BAGS',
                'srtOrd' => 52,
            ],
        ];

        foreach ($productPackagingUnit as $key => $value) {
            ProductPackingUnit::factory()->create([
                'name' => $value['cdNm'],
                'description' => $value['cdDesc'],
                'code' => $value['cd'],
            ]);
        }
    }
}
