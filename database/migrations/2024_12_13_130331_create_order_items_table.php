<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::create('OrderItem', function (Blueprint $table) {
            $table->id();
            $table->foreignId("order_id")->references("id")->on("Orders")->onUpdate("cascade")->onDelete("restrict");
            $table->foreignId("product_id")->references("id")->on("Product")->onUpdate("cascade")->onDelete("restrict");
            $table->string("productName");
            $table->string("productCode");
            $table->string('batchNumber')->nullable();
            $table->string('expireDate')->nullable();
            $table->decimal("quantity", 14, 2);
            $table->decimal("remainingQuantity", 14, 2)->default(0);
            $table->decimal("price", 14, 2)->default(0);
            $table->decimal("discount", 14, 2)->default(0);
            $table->decimal("supplyAmount", 14, 2)->default(0);
            $table->decimal("taxAmount", 14, 2)->default(0);
            $table->decimal("taxRate", 14, 2)->default(0);
            $table->string("taxName")->nullable();
            $table->decimal("totalAmount", 14, 2)->default(0);
            $table->decimal("totalDiscount", 14, 2)->default(0);
             $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('OrderItem');
    }
};
