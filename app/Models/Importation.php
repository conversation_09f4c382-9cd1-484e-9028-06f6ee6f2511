<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Importation extends Model
{
    /** @use HasFactory<\Database\Factories\ImportationFactory> */
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'Importation';

    protected $fillable = [
        'taskCd',
        'dclDe',
        'itemSeq',
        'dclNo',
        'hsCd',
        'itemNm',
        'orgnNatCd',
        'exptNatCd',
        'pkg',
        'pkgUnitCd',
        'qty',
        'qtyUnitCd',
        'totWt',
        'netWt',
        'spplrNm',
        'agntNm',
        'invcFcurAmt',
        'invcFcurCd',
        'invcFcurExcrt',
        'status',
        'item_code',
        'branch_id',
        'created_at',
        'updated_at'
    ];
}
