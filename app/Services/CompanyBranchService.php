<?php

namespace App\Services;

use App\Data\CompanyBranchUpdateData;
use App\Data\NewCompanyBranchData;
use App\Exceptions\BadRequest;
use App\Exceptions\NotFound;
use App\Models\BranchCategory;
use App\Models\Company;
use App\Models\CompanyBranch;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;

class CompanyBranchService
{

    protected $user;
    public function __construct(protected RoleService $roleService) {
        $this->user = Auth::user();
    }

    public function getBranchCategories(): array
    {

        return BranchCategory::where('isActive', true)
            ->select('id', 'name', 'description')
            ->get()
            ->toArray();
    }

    public function storeBranch(NewCompanyBranchData $data, Company $companyData): void
    {

        $brancNameValidation = collect($companyData['branches'])->contains('name', $data->name);

        throw_if($brancNameValidation, BadRequest::class, 'Branch with this name already exists');

        $branchCategoryValidation = BranchCategory::where('id', $data->branchCategoryID)
            ->exists();

        throw_if(!$branchCategoryValidation, BadRequest::class, 'Branch category not found');

        CompanyBranch::create([
            'company_id' => $companyData->id,
            'name' => $data->name,
            'phone' => $data->phone,
            'email' => $data->email,
            'address' => $data->address,
            'isEBM' => $companyData->isEBM,
            'branch_category_id' => $data->branchCategoryID,
        ]);
    }

    public function storeDefaultBranch(Company $companyData): CompanyBranch
    {

       return  CompanyBranch::create([
            'company_id' => $companyData->id,
            'name' => 'Default Branch',
            'address' => 'Default Address',
            'isEBM' => $companyData->isEBM,
            'isPrimary' => true,
            'branch_category_id' => BranchCategory::where('name', 'General')->first()->id,
        ]);
    }

    public function getBranch(int $id, Company $companyData): CompanyBranch
    {

        $branch = CompanyBranch::where('id', $id)
            ->where('company_id', $companyData->id)
            ->select('id', 'name', 'phone', 'email', 'address', 'branchCode', 'mode', 'topMessage', 'bottomMessage', 'deviceSerial', 'isActive', 'isPrimary')
            ->first();

        throw_if(!$branch, NotFound::class, 'Branch not found');

       return $branch;
    }

    public function updateBranch(CompanyBranchUpdateData $data,int $branchId,Company $companyData): void
    {

        $branchNameValidation = collect($companyData['branches'])
            ->where('id', '!=', $branchId)
            ->contains('name', $data->name);

        throw_if($branchNameValidation, BadRequest::class, 'Branch with this name already exists');

        $branch = CompanyBranch::where('id', $branchId)
            ->where('company_id', $companyData->id)
            ->first();

        throw_if(!$branch, NotFound::class, 'Branch not found');

        $branch->update([
            'name' => $data->name,
            'phone' => $data->phone,
            'email' => $data->email,
            'address' => $data->address,
            'topMessage' => $data->topMessage,
            'bottomMessage' => $data->bottomMessage,
        ]);
    }

    public function makeDefaultBranch(int $branchId, Company $companyData): void
    {

        $branch = CompanyBranch::where('id', $branchId)
            ->where('company_id', $companyData->id)
            ->first();

        throw_if(!$branch, NotFound::class, 'Branch not found');

        $this->roleService->setDefaultBranch($this->user->id, $companyData->id, $branchId);
   
    }
    public function getBranches(int $companyId,int $perPage, int $page) : LengthAwarePaginator
    {
        return CompanyBranch::where('company_id', $companyId)
            ->select('id', 'name', 'phone', 'email', 'address', 'branchCode')
            ->paginate(perPage: $perPage, page: $page);
    }
}
