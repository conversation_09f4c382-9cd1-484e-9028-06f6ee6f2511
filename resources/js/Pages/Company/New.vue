<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from "@inertiajs/vue3";
import FormSection from '@/Components/FormSection.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import RadioOptionGroup from '@/Components/RadioOptionGroup.vue';
import { Button } from '@/components/ui/button';

const form = useForm({
    name: null,
    email: null,
    phone: null,
    address: null,
    tin: null,
    isEBM: 0,
});

function createNewCompany() {
    form.post(route('company.store'), {

    });
}

const options = [
    { value: 0, label: 'No' },
    { value: 1, label: 'Yes' },
];
</script>

<template>
    <AppLayout title="Create new Company ">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Create new Company
            </h2>
        </template>

        <FormSection>
            <template #title>
                Company Information
            </template>

            <template #form>
                <form class="" @submit.prevent="createNewCompany">
                    <div class="pb-4">
                        <InputLabel for="name" value="Name" />
                        <TextInput id="name" v-model="form.name" type="text" />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>
                    <div class="grid grid-cols-6 gap-6">
                        <div class="col-span-6 sm:col-span-3">
                            <InputError class="mt-2" :message="form.errors.email" />
                            <InputLabel for="email" value="Email" />
                            <TextInput id="email" v-model="form.email" type="email" />
                        </div>
                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="phone" value="Phone" />
                            <TextInput id="phone" v-model="form.phone" type="text" />
                            <InputError class="mt-2" :message="form.errors.phone" />
                        </div>

                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="tin" value="TIN" />
                            <TextInput id="tin" v-model="form.tin" type="text" />
                            <InputError class="mt-2" :message="form.errors.tin" />
                        </div>

                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="address" value="Address" />
                            <TextInput id="address" v-model="form.address" type="text" />
                            <InputError class="mt-2" :message="form.errors.address" />
                        </div>
                    </div>

                    <div class="py-4">
                        <RadioOptionGroup v-model="form.isEBM" :options="options" name="isEBM" label="Will you use EBM" />
                        <InputError class="mt-2" :message="form.errors.isEBM" />
                    </div>

                    <div class="flex justify-end mt-4">
                        <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                            Create 
                        </PrimaryButton>
                    </div>

                </form>
            </template>
        </FormSection>
      
        <template #footer />
    </AppLayout>
</template>