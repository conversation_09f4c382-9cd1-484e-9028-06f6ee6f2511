<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('StockTrack', function (Blueprint $table) {
            $table->id();
            $table->foreignId("branch_id")->references('id')->on('CompanyBranch')->onDelete('restrict');
            $table->bigInteger('trackingNumber');
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('StockTrack');
    }
};
