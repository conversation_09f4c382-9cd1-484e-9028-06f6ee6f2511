<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\PartyType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CompanyParty>
 */
class CompanyPartyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_id' => Company::factory(),
            'party_type_id' => PartyType::factory(),
            'account_id' => null,
            'slug' => $this->faker->slug,
            'name' => $this->faker->name,
            'email' => $this->faker->email,
            'phone' => $this->faker->phoneNumber,
            'address' => $this->faker->address,
            'tin' => $this->faker->randomNumber(9),            
        ];
    }
}
