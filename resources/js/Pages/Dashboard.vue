<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/vue3';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ref, onMounted } from 'vue';
import { differenceInMinutes, differenceInHours, differenceInDays, isToday, isYesterday, format } from 'date-fns';
import axios from 'axios';
import { useToast } from '@/components/ui/toast'; // Import Shadcn toast hook

const { toast } = useToast(); // Initialize toast

const analyticsData = ref([]);
const paymentDetails = ref([]);
const productDetails = ref([]);
const salesTransactions = ref([]);
const expiringProducts = ref([]);

const isAnalyticsLoading = ref(true);
const isExpiringLoading = ref(true);
const isPaymentLoading = ref(true);
const isProductLoading = ref(true);
const isSalesLoading = ref(true);

onMounted(async () => {
  try {
    const response = await axios.get(route('dashboard-json'));

    // Update reactive state with fetched data
    analyticsData.value = response.data.FirstRowData || [];
    paymentDetails.value = response.data.PaymentDetails || [];
    productDetails.value = response.data.TopSellingProduct || [];
    salesTransactions.value = response.data.SaleTransactions || [];
    expiringProducts.value = response.data.expiringProducts || [];
  } catch (error) {

    toast({
      title: 'Error',
      description: 'Failed to load dashboard data. Please try again later.',
      variant: 'destructive', // Red error style
      duration: 5000, // 5 seconds
    });
  } finally {
    console.log('Stopping loading states'); // Debug
    isAnalyticsLoading.value = false;
    isExpiringLoading.value = false;
    isPaymentLoading.value = false;
    isProductLoading.value = false;
    isSalesLoading.value = false;
  }
});

const skeletonRows = (count) => Array.from({ length: count }, (_, i) => i);

const formatSaleDate = (saleDate) => {
  if (!saleDate) return 'N/A';

  const today = new Date();
  const sale = new Date(saleDate);

  const minutesDiff = differenceInMinutes(today, sale);
  const hoursDiff = differenceInHours(today, sale);
  const daysDiff = differenceInDays(today, sale);

  if (minutesDiff <= 30) {
    return 'Last 30 min';
  } else if (hoursDiff <= 2) {
    if (isToday(sale)) return 'Today';
    if (isYesterday(sale)) return 'Yesterday';
  } else if (daysDiff >= 7) {
    return `Last ${daysDiff} day${daysDiff !== 1 ? 's' : ''}`;
  } else {
    return format(sale, 'MMMM d EEEE');
  }
};

const formatExpiryDateInWords = (expiryDate) => {
  if (!expiryDate) return 'N/A';
  return format(new Date(expiryDate), 'MMMM d, yyyy');
};

const formatPaymentDateInWords = (date) => {
  if (!date) return 'N/A';
  return format(new Date(date), 'MMMM d, yyyy');
};
</script>

<template>
  <AppLayout title="Dashboard">
    <template #header>
      <h2 class="font-semibold text-2xl text-zata-primary-dark leading-tight">
        Dashboard
      </h2>
    </template>

    <div class="mt-6 flex space-x-4">
      <Button as-child>
        <Link :href="route('pos')">POS</Link>
      </Button>
      <Button as-child variant="secondary">
        <Link :href="route('product.new')">Add Product</Link>
      </Button>
      <Button as-child variant="secondary">
        <Link :href="route('transaction.account')">Finance</Link>
      </Button>
    </div>

    <div class="mt-8">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        <Card v-if="isAnalyticsLoading" v-for="i in skeletonRows(4)" :key="i"
          class="bg-zata-background-dark border-zata-primary-dark border-l-4 shadow-md min-h-[120px]">
          <CardContent class="pt-6 pb-4">
            <div class="h-8 w-3/4 bg-gray-300 rounded animate-pulse mb-2"></div>
            <div class="h-4 w-1/2 bg-gray-300 rounded animate-pulse"></div>
          </CardContent>
        </Card>
        <Card v-else v-for="(data, index) in analyticsData" :key="index"
          class="  border transition-shadow min-h-[120px]">
          <CardContent class="pt-6 pb-4">
            <h3 class="text-3xl font-extrabold text-zata-primary-dark">{{ data.value }}</h3>
            <p class="text-sm  mt-2 uppercase tracking-wide">{{ data.label }}</p>
          </CardContent>
        </Card>
      </div>

      <Card class="mb-8 min-h-[300px] my-20">
        <CardHeader class="border-b border-gray-300">
          <CardTitle class="text-xl ">Expiring Products</CardTitle>
        </CardHeader>
        <CardContent>
          <Table v-if="isExpiringLoading || expiringProducts.length > 0">
            <TableHeader>
              <TableRow>
                <TableHead class="text-zata-primary-dark">Item Code</TableHead>
                <TableHead class="text-zata-primary-dark">Item Description</TableHead>
                <TableHead class="text-zata-primary-dark">Batch</TableHead>
                <TableHead class="text-zata-primary-dark">Expiry Date</TableHead>
                <TableHead class="text-zata-primary-dark">Qty</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="isExpiringLoading" v-for="i in skeletonRows(5)" :key="i">
                <TableCell>
                  <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
              </TableRow>
              <TableRow v-else v-for="(product, index) in expiringProducts" :key="index"
               
                class="hover:bg-zata-background-dark transition-colors cursor-pointer">
                <TableCell>{{ product.code }}</TableCell>
                <TableCell>{{ product.name }}</TableCell>
                <TableCell>{{ product.batchNumber }}</TableCell>
                <TableCell>{{ formatExpiryDateInWords(product.expireDate) }}</TableCell>
                <TableCell>{{ product.currentStock }}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
          <div v-else class="py-10 text-center">
            <p class="text-zata-secondary-dark">No expiring products</p>
          </div>
        </CardContent>
      </Card>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <Card class="min-h-[300px]">
          <CardHeader class="border-b border-zata-primary-lighter">
            <CardTitle class="text-xl text-zata-primary-dark">Payment Details</CardTitle>
          </CardHeader>
          <CardContent>
            <Table v-if="isPaymentLoading || paymentDetails.length > 0">
              <TableHeader>
                <TableRow>
                  <TableHead class="text-zata-primary-dark">Date</TableHead>
                  <TableHead class="text-zata-primary-dark">Name</TableHead>
                  <TableHead class="text-zata-primary-dark">Description</TableHead>
                  <TableHead class="text-zata-primary-dark text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-if="isPaymentLoading" v-for="i in skeletonRows(5)" :key="i">
                  <TableCell>
                    <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                  </TableCell>
                  <TableCell>
                    <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                  </TableCell>
                  <TableCell>
                    <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                  </TableCell>
                  <TableCell>
                    <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                  </TableCell>
                </TableRow>
                <TableRow v-else v-for="(payment, index) in paymentDetails" :key="index"
                  class="hover:bg-zata-background-dark transition-colors cursor-pointer">
                  <TableCell>{{ formatPaymentDateInWords(payment.date) }}</TableCell>
                  <TableCell>{{ payment.name }}</TableCell>
                  <TableCell>{{ payment.description }}</TableCell>
                  <TableCell class="text-right">{{ $zataCurrency(payment.amount) }}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
            <div v-else class="py-10 text-center">
              <p class="text-zata-secondary-dark">No payment details available</p>
            </div>
          </CardContent>
        </Card>

        <Card class="min-h-[300px]">
          <CardHeader class="border-b border-zata-primary-lighter">
            <CardTitle class="text-xl text-zata-primary-dark">Top Selling Product</CardTitle>
          </CardHeader>
          <CardContent>
            <Table v-if="isProductLoading || productDetails.length > 0">
              <TableHeader>
                <TableRow>
                  <TableHead class="text-zata-primary-dark">Name</TableHead>
                  <TableHead class="text-zata-primary-dark">Units Sold</TableHead>
                  <TableHead class="text-zata-primary-dark">Sales</TableHead>
                  <TableHead class="text-zata-primary-dark text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-if="isProductLoading" v-for="i in skeletonRows(5)" :key="i">
                  <TableCell>
                    <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                  </TableCell>
                  <TableCell>
                    <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                  </TableCell>
                  <TableCell>
                    <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                  </TableCell>
                  <TableCell>
                    <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                  </TableCell>
                </TableRow>
                <TableRow v-else v-for="(product, index) in productDetails" :key="index"
                  class="hover:bg-zata-background-dark transition-colors cursor-pointer">
                  <TableCell>{{ product.name }}</TableCell>
                  <TableCell>{{ product.unitsSold }}</TableCell>
                  <TableCell>{{ product.sales }}</TableCell>
                  <TableCell class="text-right">{{ $zataCurrency(product.amount) }}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
            <div v-else class="py-10 text-center">
              <p class="text-zata-secondary-dark">No product details available</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card class="min-h-[300px] mt-20">
        <CardHeader class="border-b border-gray-300">
          <CardTitle class="text-xl ">Sales Transactions</CardTitle>
        </CardHeader>
        <CardContent class="overflow-x-auto">
          <Table v-if="isSalesLoading || salesTransactions.length > 0">
            <TableHeader>
              <TableRow>
                <TableHead class="text-zata-primary-dark">Product Count</TableHead>
                <TableHead class="text-zata-primary-dark">Type</TableHead>
                <TableHead class="text-zata-primary-dark text-right">Total Amount</TableHead>
                <TableHead class="text-zata-primary-dark">Sale Date</TableHead>
                <TableHead class="text-zata-primary-dark">Username</TableHead>
                <TableHead class="text-zata-primary-dark">Client Name</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="isSalesLoading" v-for="i in skeletonRows(5)" :key="i">
                <TableCell>
                  <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
              </TableRow>
              <TableRow v-else v-for="(sale, index) in salesTransactions" :key="index"
                class="hover:bg-zata-background-dark transition-colors cursor-pointer">
                <TableCell>{{ sale.items.length }}</TableCell>
                <TableCell>{{ sale.transactionType }}</TableCell>
                <TableCell class="text-right">{{ $zataCurrency(sale.totalAmount) }}</TableCell>
                <TableCell>{{ formatSaleDate(sale.salesDate) }}</TableCell>
                <TableCell>{{ sale.user.name }}</TableCell>
                <TableCell>{{ sale.clientName }}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
          <div v-else class="py-10 text-center">
            <p class="text-zata-secondary-dark">No sales transactions available</p>
          </div>
        </CardContent>
      </Card>
    </div>

    <template #sidenav />
    <template #footer />
  </AppLayout>
</template>

<style scoped>
.animate-pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}
</style>