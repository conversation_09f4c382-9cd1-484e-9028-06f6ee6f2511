<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\ProductClass;
use Database\Factories\ProductClassFactory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductClassSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $productClasses = [
            [
                'name' => 'Food standards',
                'code' =>  '85151701'
            ],
            [
                'name' => 'Personal computer television PC TV tuners',
                'code' => '4320155400',
            ],
            [
                'name' => 'Fanta',
                'code' =>  '5020230601'
            ],
            [
                'name' => 'water',
                'code' =>  '5020230602'
            ],
            [
                'name' => 'Inyange',
                'code' =>  '5020230102'
            ],
            [
                'name' => 'Gypsum boad',
                'code' =>  '1112200101'
            ],
            [
                'name' => 'Gypsum board 1',
                'code' =>  '3011170102'
            ],
            [
                'name' => 'Miscellaneous animal food',
                'code' =>  '10122101'
            ],
            [
                'name' => 'Wine',
                'code' =>  '50202203'
            ],
            [
                'name' => 'Solar equipment systems',
                'code' =>  '26111612'
            ],
            [
                'name' => 'Paint mixers',
                'code' =>  '31211905'
            ],
            [
                'name' => 'Fungicides',
                'code' =>  '10171702'
            ],
            [
                'name' => 'Earthmoving machinery parts and accessories',
                'code' =>  '22101539'
            ],
            [
                'name' => 'Unmanned aerial vehicle',
                'code' =>  '25132100'
            ],
            [
                'name' => 'Agricultural drone',
                'code' =>  '25132101'
            ],
            [
                'name' => 'Military drone',
                'code' =>  '25132102'
            ],
            [
                'name' => 'Recreational drone',
                'code' =>  '25132103'
            ]

        ];

        foreach ($productClasses as $productClass) {
            ProductClass::factory()->create($productClass);
        }
    }
}
