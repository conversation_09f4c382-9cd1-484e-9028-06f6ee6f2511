<?php

namespace Database\Factories;

use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OrderReceiptSignature>
 */
class OrderReceiptSignatureFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'receiptNumber' => $this->faker->word,
            'internalData' => $this->faker->word,
            'receiptSignature' => $this->faker->word,
            'totalReceiptNumber' => $this->faker->word,
            'vsdcReceiptPublishDate' => $this->faker->word,
            'sdcId' => $this->faker->word,
            'mrcNumber' => $this->faker->word,
            'order_id' => Order::factory(),
            'invoiceNumber' => $this->faker->word,
        ];
    }
}
