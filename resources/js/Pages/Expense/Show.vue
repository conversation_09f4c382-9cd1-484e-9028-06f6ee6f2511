<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";
import ListOption from '@/Components/ListOption.vue';
import ProfileSection from '@/Components/ProfileSection.vue';
import ButtonGroupOption from '@/Components/ButtonGroupOption.vue';
import PreviewSection from '@/Components/PreviewSection.vue';
import PrimaryLink from '@/Components/PrimaryLink.vue';

const props = defineProps({
    Expense: Object | Array,
});

const details = [
    { title: 'Amount', value: props.Expense.amount },
    { title: 'Category', value: props.Expense.category.name },
    { title: 'Payment Mode', value: props.Expense.payment_mode.name },
    { title: 'Description', value: props.Expense.description },
    { title: 'Date', value: props.Expense.date },
    { title: 'Updated At', value: props.Expense.updated_at },
];
</script>

<template>
    <AppLayout title="Expense">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Dashboard
            </h2>
        </template>

        <PreviewSection>
            <template #content>

                <ButtonGroupOption>
                    <PrimaryLink :href="route('expense.edit', Expense.id)">Edit</PrimaryLink>
                </ButtonGroupOption>
                <ListOption :details="details" />
            </template>
        </PreviewSection>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>
