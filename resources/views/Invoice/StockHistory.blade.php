<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Zata Point Delivery Note</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <style>
        @media print {
            .no-print {
                display: none;
            }

            .print-only {
                display: block;
            }
        }
    </style>
</head>

<body class="font-sans antialiased">
    <section id="print-section" class="flex justify-center p-4 print-only mx-auto bg-white " style="font-size: 10px">
        <div class="w-full max-w-7xl bg-white rounded mx-auto">
            <div class="border border-gray-400 m-5 p-5 rounded">
                <!-- Centered Logo and Top Message -->
                <div class="text-center mb-6">
                    <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('zata_logo.png'))) }}"
                        alt="Zata Logo" class="w-44 mx-auto">
                    <p class="mt-4 text-gray-600"> Stock History</p>
                </div>
                <section class="flex justify-between mb-4">
                    <div class="flex flex-col">
                        <p class="font-bold " style="font-size: 10px">{{ $company->name }}</p>
                        <p><span class="font-bold" style="font-size: 10px">Address:</span> {{ $company->address }}</p>
                        <p><span class="font-bold" style="font-size: 10px">Tel:</span> {{ $company->phone }}</p>
                        <p class="hidden"><span class="font-bold">Email:</span> {{ $company->email }}</p>
                        <p><span class="font-bold" style="font-size: 10px">TIN:</span> {{ $company->tin }}</p>

                    </div>
                </section>

                <p style="font-size: 10px">
                Branch Name : {{ $branch->name }}
                </p>

                <div>
                    <p class="text-center mt-2" style="font-size: 10px">
                        {{ Date::now()->format('F d, Y') }}
                    </p>

                </div>

                <!-- Items Table -->
                <div class="w-full mb-6">
                    <table class="min-w-full border border-gray-300">
                        <thead class="bg-gray-200">
                            <tr>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/7 " style="font-size: 7px">Item</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/7 " style="font-size: 7px">Code</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/7 " style="font-size: 7px">Batch</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/7 " style="font-size: 7px">Exp Date</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-2/7 " style="font-size: 7px">User</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/7 " style="font-size: 7px">quantity</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/7 " style="font-size: 7px">stockType</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/7 " style="font-size: 7px">orderType</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/7 " style="font-size: 7px">Date</th>
                                <th class="text-left px-2 py-2 border border-gray-300 w-1/7 " style="font-size: 7px">Customer / Supplier</th>
                            </tr>
                        </thead>

                        <tbody>
                            @foreach ($items as $item)
                                <tr>
                                    <td class="px-2 py-2 border border-gray-300 " style="font-size: 7px">
                                        {{ Str::limit($item['product'], 20, '...') }}
                                    </td>
                                    <td class="px-2 py-2 border border-gray-300 truncate max-w-xs " style="font-size: 7px">
                                        {{ $item['productCode'] }}</td>
                                    <td class="px-2 py-2 border border-gray-300 " style="font-size: 7px">{{ $item['batchNumber'] }}</td>
                                    <td class="px-2 py-2 border border-gray-300 " style="font-size: 7px">
                                        {{ $item['expiryDate'] ? Date::parse($item['expiryDate'])->format('Y-m-d') : '-' }}
                                    </td>
                                      <td class="px-2 py-2 border border-gray-300 " style="font-size: 7px">{{ $item['user'] }}</td>
                                    <td class="px-2 py-2 border border-gray-300 " style="font-size: 7px">{{ $item['quantity'] }}</td>
                                    <td class="px-2 py-2 border border-gray-300 " style="font-size: 7px">{{ $item['stockType'] }}
                                    </td>
                                       <td class="px-2 py-2 border border-gray-300 " style="font-size: 7px">{{ $item['orderType'] }}
                                    </td>
                                    <td class="px-2 py-2 border border-gray-300" style="font-size: 7px">
                                        {{ Date::parse($item['created_at'])->format('Y-m-d') }}
                                    </td>
                                    <td class="px-2 py-2 border border-gray-300 " style="font-size: 7px">{{ $item['party'] }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Footer Section -->
                <div class="flex justify-center mt-8">
                    <div>
                        <p class="text-center mt-2">Version V1.0.0 Powered by RRA_VSDC</p>
                    </div>
                   
                </div>

            </div>
        </div>
    </section>
</body>

</html>
