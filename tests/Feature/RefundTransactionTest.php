<?php

use App\Models\Company;
use App\Models\CompanyBranch;
use App\Models\CompanyParty;
use App\Models\Country;
use App\Models\Order;
use App\Models\OrderItems;
use App\Models\PaymentMode;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductClass;
use App\Models\ProductDetail;
use App\Models\ProductPackingUnit;
use App\Models\ProductQuantityUnit;
use App\Models\ProductType;
use App\Models\Tax;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use App\Models\Role;
use App\Models\RolePermission;

beforeEach(function () {

    $this->user = User::factory()->create();

    $this->company = Company::factory()->create(["user_id" =>  $this->user->id, "isEBM" => true]);
    $this->branch = CompanyBranch::factory()->create(["company_id" => $this->company->id, 'isEBM' => true]);

    $this->permission = RolePermission::factory()->create();
    $this->role = Role::factory()->create([
        'user_id' => $this->user->id,
        'company_id' => $this->company->id,
        'role_permission_id' => $this->permission
    ]);

    $this->apiUrl = '/api/v1/transaction/refund';

    $tax = [['code' => 'A', 'rate' => 0], ['code' => 'B', 'rate' => 18], ['code' => 'C', 'rate' => 0], ['code' => 'D', 'rate' => 0]];

    foreach ($tax as $item) {
        Tax::factory()->create([
            'code' => $item['code'],
            'rate' => $item['rate']
        ]);
    }

    Sanctum::actingAs($this->user);
});

it("can create refund transaction ", function (array $payload) {
    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $response = $this->postJson("$this->apiUrl/{$payload['id']}", $payload);

    $response->assertStatus(200)->assertJson(['message' => 'Refund transaction created successfully'])
        ->assertExactJsonStructure(['message', 'invoiceID']);
})->with([
    function () {

        $product =  Product::factory()->create(['branch_id' => $this->branch->id]);

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'batchNumber' => '*********',
        ]);
        $order = Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create([
                'tin' => '*********',
                'company_id' => $this->company->id
            ])->id,
            'paymentTypeCode' => PaymentMode::factory()->create(
                [
                    'forEbm' => true
                ]
            )->code
        ]);

        OrderItems::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'batchNumber' => '*********',
        ]);

        return [
            "purchaseCode" => "45367897",
            "transactionDate" => "2021-10-10",
            "note" => "This is a test note",
            "id" => $order->id
        ];
    },
    function () {

        $product =  Product::factory()->create(['branch_id' => $this->branch->id]);

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'batchNumber' => '*********',
        ]);
        $order = Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create([
                'tin' => null,
                'company_id' => null
            ])->id,
            'paymentTypeCode' => PaymentMode::factory()->create(
                [
                    'forEbm' => true
                ]
            )->code
        ]);

        OrderItems::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'batchNumber' => '*********',
        ]);

        return [
            "transactionDate" => "2021-10-10",
            "note" => "This is a test note",
            "id" => $order->id
        ];
    },
    function () {

        $product =  Product::factory()->create(['branch_id' => $this->branch->id]);

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'batchNumber' => '*********',
        ]);
        $order = Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create([
                'tin' => null,
                'company_id' => $this->company->id
            ])->id,
            'paymentTypeCode' => PaymentMode::factory()->create(
                [
                    'forEbm' => true
                ]
            )->code
        ]);

        OrderItems::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'batchNumber' => '*********',
        ]);

        return [
            "transactionDate" => "2021-10-10",
            "note" => "This is a test note",
            "id" => $order->id
        ];
    },
]);

it("can throw error during create refund transaction ", function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
    ]);

    $ebmPayload = $payload['ebm'];
    $response = $this->postJson("$this->apiUrl/{$payload['id']}", $ebmPayload);

    $response->assertStatus($payload['code'])->assertJson([
        'message' =>  $payload['message']
    ]);
})->with([
    function () {

        return [
            'ebm' => [
                "purchaseCode" => "PC-123",
                "transactionDate" => "2021-10-10",
                "note" => "This is a test note",
            ],
            'message' => 'Branch not specified',
            'code' => 403,
            "id" => 9000000000
        ];
    },
    function () {

        $this->withHeaders([
            'branchId' =>  $this->branch->id
        ]);
        return [
            'ebm' => [
                "purchaseCode" => "PC-123",
                "transactionDate" => "2021-10-10",
                "note" => "This is a test note",
            ],
            'message' => 'Transaction not found',
            'code' => 404,
            'id' => 9000000000
        ];
    },
    function () {

        $this->withHeaders([
            'branchId' =>  $this->branch->id
        ]);

        $order = Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => true,
            'company_party_id' => CompanyParty::factory()->create([
                'tin' => '*********',
                'company_id' => $this->company->id
            ])->id,
        ]);

        return [
            'ebm' => [
                "purchaseCode" => "PC-123",
                "transactionDate" => "2021-10-10",
                "note" => "This is a test note",
            ],
            'message' => 'Transaction already refunded',
            'code' => 404,
            'id' => $order->id
        ];
    },
    function () {

        $this->withHeaders([
            'branchId' =>  $this->branch->id
        ]);

        $order = Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create([
                'tin' => '*********',
                'company_id' => $this->company->id
            ])->id,
        ]);

        return [
            'ebm' => [
                "purchaseCode" => null,
                "transactionDate" => "2021-10-10",
                "note" => "This is a test note",
            ],
            'message' => 'Purchase code is required',
            'code' => 404,
            'id' => $order->id
        ];
    }
]);
