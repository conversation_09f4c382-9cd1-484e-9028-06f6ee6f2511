<?php

namespace Database\Factories;

use App\Models\CompanyBranch;
use App\Models\ExpenseCategory;
use App\Models\PaymentMode;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Expense>
 */
class ExpenseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'category_id' => ExpenseCategory::factory(),
            'branch_id' => CompanyBranch::factory(),
            'payment_mode_id' => PaymentMode::factory(),
            'amount' => $this->faker->randomFloat(2, 1, 1000),
            'description' => $this->faker->sentence,
            'date' => $this->faker->date()
        ];
    }
}
