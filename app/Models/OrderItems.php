<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use DateTime;

class OrderItems extends Model
{
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'OrderItem';

    protected $fillable = [
        "order_id",
        "product_id",
        "productName",
        "productCode",
        "batchNumber",
        "expireDate",
        "quantity",
        "remainingQuantity",
        "price",
        "discount",
        "supplyAmount",
        "taxAmount",
        "taxRate",
        "taxName",
        "totalDiscount",
        "totalAmount"
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'expireDate' => 'datetime'
    ];

    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function getExpireDateAttribute($value)
    {

       return $value ? \Carbon\Carbon::createFromTimestamp($value)->format('Y-m-d') : null;
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function scopeFilterByDate($query, $startDate, $endDate)
    {
        $startDateTime = DateTime::createFromFormat('Y-m-d', $startDate);
        $isValidStartDate = $startDateTime && $startDateTime->format('Y-m-d') === $startDate;

        $endDateTime = DateTime::createFromFormat('Y-m-d', $endDate);
        if ($endDateTime) {
            $endDateTime->setTime(23, 59, 59);
        }
        $isValidEndDate = $endDateTime && $endDateTime->format('Y-m-d H:i:s') === $endDateTime->format('Y-m-d') . ' 23:59:59';

        if ($isValidStartDate && $isValidEndDate) {
            $query->where('created_at', '>=', $startDateTime->format('Y-m-d H:i:s'))
                ->where('created_at', '<=', $endDateTime->format('Y-m-d H:i:s'));
        } elseif ($isValidStartDate) {
            $query->where('created_at', '>=', $startDateTime->format('Y-m-d H:i:s'));
        } elseif ($isValidEndDate) {
            $query->where('created_at', '<=', $endDateTime->format('Y-m-d H:i:s'));
        }

        return $query;
    }

    public function scopeFilterByProductId($query, $productId)
    {

        if (!is_null($productId)) {

            $query->where('product_id', $productId);
        }

        return $query;
    }

    public function setCurrentStockAttribute($value)
    {
        $this->attributes['current_stock'] = max(0, $value);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }
}
