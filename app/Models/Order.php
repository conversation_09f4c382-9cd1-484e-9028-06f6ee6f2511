<?php

namespace App\Models;

use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Laravel\Scout\Searchable;

class Order extends Model
{
    use HasFactory, Searchable;

    protected $dateFormat = 'U';

    protected $table = 'Orders';

    protected $fillable = [
        "type",
        "clientTin",
        "clientName",
        "clientPhoneNumber",
        "originalInvoiceNumber",
        "invoiceNumber",
        "purchaseCode",
        "salesTypeCode",
        "receiptTypeCode",
        "paymentTypeCode",
        "salesStatusCode",
        "confirmationDate",
        "salesDate",
        "remarks",
        "synced",
        "taxblAmtA",
        "taxblAmtB",
        "taxblAmtC",
        "taxblAmtD",
        "taxAmtA",
        "taxAmtB",
        "taxAmtC",
        "taxAmtD",
        "totTaxblAmt",
        "totTaxAmt",
        "totAmt",
        "branch_id",
        "isRefunded",
        "demo",
        "transactionType",
        "prescriptionNumber",
        "insuranceTin",
        "user_id",
        "company_party_id",

        'deliveredBy',
        'deliveredTo',
        'confirmedBy',
        'status',
    ];

    public function toSearchableArray()
    {
        return [
            'clientTin' => $this->clientTin,
            'clientName' => $this->clientName,
            'remarks' => $this->remarks,
            'totAmt' => $this->totAmt,
            'transactionType' => $this->transactionType,
            'invoiceNumber' => $this->invoiceNumber,
        ];
    }

    protected $casts = [
        'synced' => 'boolean',
        'isRefunded' => 'boolean',
        'demo' => 'boolean',

        'salesDate' => 'datetime',
        'confirmationDate' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected function serializeDate(DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function getSalesDateAttribute($value)
    {
        return \Carbon\Carbon::createFromTimestamp($value)->format('Y-m-d H:i:s');
    }

    public function items(): HasMany
    {
        return $this->hasMany(OrderItems::class, 'order_id', 'id');
    }

    public function ebm(): HasOne
    {

        return $this->hasOne(OrderReceiptSignature::class, 'order_id', 'id');
    }

    public function scopeFilterByDate($query, $startDate = null, $endDate = null)
    {
        if (is_null($startDate) && is_null($endDate)) {
            return $query;
        }

        try {
            $startTimestamp = null;
            if (!is_null($startDate) && !empty($startDate)) {
                $startTimestamp = \Carbon\Carbon::createFromFormat('Y-m-d', $startDate)
                    ->startOfDay()
                    ->getTimestamp();
            }

            $endTimestamp = null;
            if (!is_null($endDate) && !empty($endDate)) {
                $endTimestamp = \Carbon\Carbon::createFromFormat('Y-m-d', $endDate)
                    ->endOfDay()
                    ->getTimestamp();
            }

            if ($startTimestamp && $endTimestamp) {
                return $query->whereBetween('salesDate', [$startTimestamp, $endTimestamp]);
            } elseif ($startTimestamp) {
                return $query->where('salesDate', '>=', $startTimestamp);
            } elseif ($endTimestamp) {
                return $query->where('salesDate', '<=', $endTimestamp);
            }

            return $query;
        } catch (\Exception $e) {
            Log::error('Date filter error: ' . $e->getMessage());
            return $query;
        }
    }


    public function signature(): HasOne
    {
        return $this->hasOne(OrderReceiptSignature::class, 'order_id', 'id');
    }

    public function user(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function party(): HasOne
    {
        return $this->hasOne(CompanyParty::class, 'id', 'company_party_id');
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(CompanyBranch::class, 'branch_id');
    }
}
