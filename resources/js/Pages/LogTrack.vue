<script setup>
import { ref, reactive, computed } from 'vue';
import { useForm } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Button } from '@/components/ui/button';

const props = defineProps({
  Logs: Object,
  currentPage: Number,
  lastPage: Number,
  itemsPerPage: Number,
  pageItems: Number,
  total: Number,
  searchQuery: String,
});

const tableHeaders = [
  'User',
  'Branch',
  'Message',
  'Action',
  'Date Created',
];

const tableRows = computed(() => {
  return props.Logs.map(log => ({
    user: log.user,
    branch: log.branch,
    message: truncateName(log.message),
    action: log.action,
    dateCreate: log.dateCreate.split(' ')[0],
  }));
});

const showFilters = ref(false);
const paginationState = reactive({
  currentPage: props.currentPage,
  itemsPerPage: props.itemsPerPage,
  totalPages: props.lastPage,
});

const paginationForm = useForm({
  page: paginationState.currentPage,
  perPage: paginationState.itemsPerPage,
  searchQuery: props.searchQuery || '',
  fromDate: '',
  toDate: '',
});

function toggleFilters() {
  showFilters.value = !showFilters.value;
}

function applyFilters() {
  paginationState.currentPage = 1;
  updatePagination();
}

function resetFilters() {
  paginationForm.reset();
  paginationForm.searchQuery = '';
  paginationForm.fromDate = '';
  paginationForm.toDate = '';
  paginationState.currentPage = 1;
  updatePagination();
}

function updatePagination() {
  paginationForm.page = paginationState.currentPage;
  paginationForm.perPage = paginationState.itemsPerPage;
  paginationForm.get(route('logTrack'), {
    preserveState: true,
    preserveScroll: true,
  });
}

function handleItemsPerPageChange() {
  paginationState.currentPage = 1;
  updatePagination();
}

function prevPage() {
  if (paginationState.currentPage > 1) {
    paginationState.currentPage--;
    updatePagination();
  }
}

function nextPage() {
  if (paginationState.currentPage < paginationState.totalPages) {
    paginationState.currentPage++;
    updatePagination();
  }
}

function truncateName(name, maxLength = 50) {
  if (name && name.length > maxLength) {
    return name.substring(0, maxLength) + '...';
  }
  return name || '-';
}
</script>

<template>
  <AppLayout title="Logs Tracking">
    <div class="py-6 px-4 sm:px-6 lg:px-8">
      <!-- Header Section -->
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h3 class="text-2xl font-bold text-gray-900">Logs Tracking</h3>
      </div>

      <!-- Filters Section -->
      <div class="mb-6">
        <Button
          @click="toggleFilters"
          variant="outline"
          class="inline-flex items-center"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
          </svg>
          {{ showFilters ? 'Hide Filters' : 'Show Filters' }}
        </Button>

        <!-- Filter Panel -->
        <transition name="slide-fade">
          <div v-if="showFilters" class="mt-4 p-6 bg-white rounded-xl shadow-sm border border-gray-100">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- Date Filters -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Date Range</label>
                <div class="flex gap-2">
                  <input
                    type="date"
                    v-model="paginationForm.fromDate"
                    class="w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                  />
                  <input
                    type="date"
                    v-model="paginationForm.toDate"
                    class="w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                  />
                </div>
              </div>

              <!-- Search -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Search</label>
                <input
                  type="text"
                  v-model="paginationForm.searchQuery"
                  placeholder="Search logs..."
                  class="w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                />
              </div>
            </div>

            <!-- Filter Actions -->
            <div class="mt-6 flex gap-4">
              <Button
                @click="applyFilters"
                variant="default"
              >
                Apply Filters
              </Button>
              <Button
                @click="resetFilters"
                variant="outline"
              >
                Reset Filters
              </Button>
            </div>
          </div>
        </transition>
      </div>

      <!-- Table -->
      <div class="bg-white rounded-xl shadow-sm overflow-hidden">
        <div v-if="props.Logs.length > 0" class="overflow-x-auto">
          <table class="min-w-full">
            <thead class="bg-gray-50">
              <tr>
                <th v-for="header in tableHeaders" :key="header" class="px-6 py-3 text-left text-sm font-semibold text-gray-900">
                  {{ header }}
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <tr v-for="row in tableRows" :key="row.dateCreate" class="hover:bg-gray-50 transition-colors duration-100">
                <td v-for="(cell, index) in row" :key="index" class="px-6 py-4 text-sm text-gray-700">
                  {{ cell }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else class="text-center py-12 text-gray-500">
          No logs found
        </div>
      </div>

      <!-- Pagination -->
      <div class="mt-6 flex flex-col sm:flex-row justify-between items-center gap-4">
        <div class="flex items-center gap-2">
          <label class="text-sm text-gray-700">Show</label>
          <select
            v-model="paginationState.itemsPerPage"
            @change="handleItemsPerPageChange"
            class="rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
          >
            <option :value="5">5</option>
            <option :value="10">10</option>
            <option :value="20">20</option>
            <option :value="50">50</option>
            <option :value="100">100</option>
          </select>
          <span class="text-sm text-gray-700">per page</span>
        </div>
        <div class="flex items-center gap-4">
          <Button
            @click="prevPage"
            :disabled="paginationState.currentPage === 1"
            variant="outline"
          >
            Previous
          </Button>
          <span class="text-sm text-gray-700">
            Page {{ paginationState.currentPage }} of {{ paginationState.totalPages }}
          </span>
          <Button
            @click="nextPage"
            :disabled="paginationState.currentPage === paginationState.totalPages"
            variant="outline"
          >
            Next
          </Button>
        </div>
      </div>
    </div>

    <template #sidenav />
    <template #footer />
  </AppLayout>
</template>

<style scoped>
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}
.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}
</style>