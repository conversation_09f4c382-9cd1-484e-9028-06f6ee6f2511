<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ProductType;

class ProductTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $productType = [
            [
                'cd' => '1',
                'cdNm' => 'Raw Material',
                'cdDesc' => 'Raw Material',
                'useYn' => 'Y',
                'srtOrd' => 1,
            ],
            [
                'cd' => '2',
                'cdNm' => 'Finished Product',
                'cdDesc' => 'Finished Product',
                'useYn' => 'Y',
                'srtOrd' => 2,
            ],
            [
                'cd' => '3',
                'cdNm' => 'Service',
                'cdDesc' => 'Service without stock',
                'useYn' => 'Y',
                'srtOrd' => 3,
            ],
            [
                'cd' => '4',
                'cdNm' => 'Assets',
                'cdDesc' => 'Assets',
                'useYn' => 'Y',
                'srtOrd' => 4,
            ],
            [
                'cd' => '5',
                'cdNm' => 'Expenses',
                'cdDesc' => 'Expenses',
                'useYn' => 'Y',
                'srtOrd' => 5,
            ],
        ];

        foreach ($productType as $key => $value) {
            ProductType::factory()->create([
                'name' => $value['cdNm'],
                'description' => $value['cdDesc'],
                'code' => $value['cd'],
            ]);
        }
    }
}
