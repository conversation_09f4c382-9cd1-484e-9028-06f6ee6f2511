<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('Purchases', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->references('id')->on('CompanyBranch')->onDelete('restrict');
            $table->string('spplrTin');
            $table->string('spplrNm');
            $table->string('spplrBhfId');
            $table->string('spplrInvcNo');
            $table->string('rcptTyCd');
            $table->string('pmtTyCd')->nullable();
            $table->string('cfmDt');
            $table->string('salesDt');
            $table->string('stockRlsDt')->nullable();
            $table->decimal('totItemCnt', 14)->default(0);
            $table->decimal('taxblAmtA', 14)->default(0);
            $table->decimal('taxblAmtB', 14)->default(0);
            $table->decimal('taxblAmtC', 14)->default(0);
            $table->decimal('taxblAmtD', 14)->default(0);
            $table->decimal('taxRtA', 14)->default(0);
            $table->decimal('taxRtB', 14)->default(0);
            $table->decimal('taxRtC', 14)->default(0);
            $table->decimal('taxRtD', 14)->default(0);
            $table->decimal('taxAmtA', 14)->default(0);
            $table->decimal('taxAmtB', 14)->default(0);
            $table->decimal('taxAmtC', 14)->default(0);
            $table->decimal('taxAmtD', 14)->default(0);
            $table->decimal('totTaxblAmt', 14)->default(0);
            $table->decimal('totTaxAmt', 14)->default(0);
            $table->decimal('totAmt', 14)->default(0);
            $table->boolean('accepted')->default(false);
            $table->string('status')->default('Pending');
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('Purchases');
    }
};
