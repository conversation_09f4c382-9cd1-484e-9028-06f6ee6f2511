<?php

namespace Database\Seeders;

use App\Models\BranchCategory;
use App\Models\CompanyBranch;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CompanyBranchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        CompanyBranch::create(
            [
                'company_id' => 1,
                'name' => 'Main Branch',
                'address' => 'Kigali',
                'branch_category_id' => BranchCategory::where('name', 'General')->first()->id,
            ]
        );
    }
}
