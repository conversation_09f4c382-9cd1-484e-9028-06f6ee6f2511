<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";

const props = defineProps({
    Companies: Array,
    Analytics: Object
});
</script>

<template>
    <AppLayout title="Manage Company">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Manage Company
            </h2>
        </template>

        <div class="p-6 sm:px-20 bg-gray-50 min-h-screen">
            <!-- Companies Section -->
            <div class="mb-8">
                <div class="text-2xl font-bold text-gray-900 mb-4">
                    Companies
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Name
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Email
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    TIN
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Address
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Branch
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    EBM
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="company in Companies" :key="company.id" class="hover:bg-gray-50 cursor-pointer"
                                @click="() => $inertia.get(route('admin.company', company.id))">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ company.name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ company.email }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ company.tin }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ company.address }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ company.branches.length }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ company.isEBM ? 'Yes' : 'No' }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Analytics Section -->
            <div>
                <div class="text-2xl font-bold text-gray-900 mb-4">
                    Analytics
                </div>
                <!-- Time-based Analytics Cards -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                        <h3 class="text-sm font-medium text-gray-500">Last 30 Days</h3>
                        <p class="text-2xl font-bold text-gray-900">{{ Analytics.analytics.last_30_days }}</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                        <h3 class="text-sm font-medium text-gray-500">Last 15 Days</h3>
                        <p class="text-2xl font-bold text-gray-900">{{ Analytics.analytics.last_15_days }}</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                        <h3 class="text-sm font-medium text-gray-500">Last 7 Days</h3>
                        <p class="text-2xl font-bold text-gray-900">{{ Analytics.analytics.last_7_days }}</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                        <h3 class="text-sm font-medium text-gray-500">Last 24 Hours</h3>
                        <p class="text-2xl font-bold text-gray-900">{{ Analytics.analytics.last_24_hours }}</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                        <h3 class="text-sm font-medium text-gray-500">Last 6 Hours</h3>
                        <p class="text-2xl font-bold text-gray-900">{{ Analytics.analytics.last_6_hours }}</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                        <h3 class="text-sm font-medium text-gray-500">Last 1 Hour</h3>
                        <p class="text-2xl font-bold text-gray-900">{{ Analytics.analytics.last_1_hour }}</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                        <h3 class="text-sm font-medium text-gray-500">Last 15 Minutes</h3>
                        <p class="text-2xl font-bold text-gray-900">{{ Analytics.analytics.last_15_minutes }}</p>
                    </div>
                </div>

                <!-- Top Companies Table -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-4">
                        <h3 class="text-lg font-semibold text-gray-900">Top Companies</h3>
                    </div>
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Name
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Email
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Address
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Total Invoices
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Invoices per Branch
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="company in Analytics.topCompanies" :key="company.id" class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ company.name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ company.email }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ company.address }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ company.branches_count }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div v-for="branch in company.branches" :key="branch.id" class="mb-1">
                                        {{ branch.name }}: {{ branch.orders_count }}
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>