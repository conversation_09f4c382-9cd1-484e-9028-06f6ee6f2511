<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class TransactionPosRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return
            [
                "prescriptionNumber" => "nullable|string|max:200",
                "insuranceTin" => "nullable|string|max:200",
                "purchaseCode" => "nullable|string|max:200",
                "customerID" => "nullable|numeric|min:1",
                'paymentMethodID' => 'required|numeric|min:1',
                "transactionDate" => "required|date_format:Y-m-d",
                "note" => "nullable|string|max:200",
                'items' => 'required|array',
                'items.*.productID' => 'required|numeric|min:1',
                'items.*.units' => 'required|numeric|min:1',
                'items.*.unitPrice' => 'required|numeric|min:1',
                "items.*.discountRate" => "nullable|min:0|max:100",
                "items.*.batchNumber" => 'required|string|max:100',
                "items.*.movingUnit" => 'nullable|max:100|in:main,sub',

                "customerTIN" => "nullable|string|min:9|max:9",
                "customerName" => "nullable|string|max:200",
                "customerPhone" => "nullable|string|min:10|max:12",

                "originalInvoiceNumber" => "nullable|string|max:200",
            ];
    }
}
