<script setup>
import TableSection from '@/Components/TableSection.vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";
import { ref, computed } from "vue";

const props = defineProps({
    Accounts: Object,
    currentPage: Number,
    lastPage: Number,
    itemsPerPage: Number,
    pageItems: Number,
    total: Number,
    searchQuery: String,
});

const tableHeaders = ["N0", "Payment Mode", "User Name", "Amount", "Transaction", "Type", "Date", "Description"];
const tableRows = computed(() => {
    return props.Accounts.map((Account) => ({
        id: Account.id,
        paymentMode: Account.paymentMode,
        userName: Account.userName,
        amount: Account.amount,
        sourceType: Account.sourceType,
        type: Account.type,
        date: Account.date,
        description: Account.description,
    }));
});
</script>

<template>
    <AppLayout title="Payments">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Payments
            </h2>
        </template>
        <TableSection :headers="tableHeaders" :rows="tableRows" :current-page="props.currentPage"
            :last-page="props.lastPage" :items-per-page="props.itemsPerPage" title="Payments"
            notFoundMessage="No payments found."
            :search-query="props.searchQuery"
            :route-name="'transaction.payment'"
            >
            >
        </TableSection>
        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>
