<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Purchase extends Model
{
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'Purchases';

    protected $fillable = [
        'spplrTin',
        'spplrNm',
        'spplrBhfId',
        'spplrInvcNo',
        'rcptTyCd',
        'pmtTyCd',
        'cfmDt',
        'salesDt',
        'stockRlsDt',
        'totItemCnt',
        'taxblAmtA',
        'taxblAmtB',
        'taxblAmtC',
        'taxblAmtD',
        'taxRtA',
        'taxRtB',
        'taxRtC',
        'taxRtD',
        'taxAmtA',
        'taxAmtB',
        'taxAmtC',
        'taxAmtD',
        'totTaxblAmt',
        'totTaxAmt',
        'totAmt',
        'accepted',
        'branch_id'
    ];


    protected $casts = [
        'accepted' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];


    public function items(): HasMany
    {
        return $this->hasMany(PurchaseItem::class, 'purchase_id', 'id');
    }
}
