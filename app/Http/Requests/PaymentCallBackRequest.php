<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PaymentCallBackRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        
        return [
            
            'event_id' => ['required', 'string'],
            'event_kind' => ['required', 'string'],
            'created_at' => ['required', 'string'],
            'data.ref' => ['required', 'string'],
            'data.kind' => ['required', 'string'],
            'data.fee' => ['required', 'numeric'],
            'data.merchant' => ['required', 'string'],
            'data.client' => ['required', 'string'],
            'data.amount' => ['required', 'numeric'],
            'data.provider' => ['required', 'string'],
            'data.status' => ['required', 'string'],
            'data.created_at' => ['required', 'string'],
            'data.processed_at' => ['required', 'string'],
        ];
    }
}
