<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from "@inertiajs/vue3";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const props = defineProps({
    Company: Object,
});

const form = useForm({
    name: props.Company.name,
    email: props.Company.email,
    phone: props.Company.phone,
    address: props.Company.address,
    tin: props.Company.tin,
    isActive: props.Company.isActive ? '1' : '0',
    isEBM: props.Company.isEBM ? '1' : '0',
});

function createNewCompany() {
    form.put(route('admin.company.update', props.Company.id));
}
</script>

<template>
    <AppLayout :title="'Update ' + Company.name">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Update {{ Company.name }}
            </h2>
        </template>

        <div class="py-12">
            <Card class="max-w-3xl mx-auto">
                <CardHeader>
                    <CardTitle class="text-lg">Company Information</CardTitle>
                </CardHeader>
                <CardContent>
                    <form @submit.prevent="createNewCompany" class="space-y-6">
                        <div v-if="$page.props.errors && Object.keys($page.props.errors).length > 0" class="space-y-2">
                            <p class="font-bold text-red-600">Please fix the following errors:</p>
                            <ul class="text-red-600 text-sm">
                                <li v-for="(error, key) in $page.props.errors" :key="key">
                                    {{ error }}
                                </li>
                            </ul>
                        </div>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <div class="space-y-2">
                                <Label for="name">Name</Label>
                                <Input id="name" v-model="form.name" type="text" />
                                <p v-if="form.errors.name" class="text-red-500 text-sm">
                                    {{ form.errors.name }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="email">Email</Label>
                                <Input id="email" v-model="form.email" type="email" />
                                <p v-if="form.errors.email" class="text-red-500 text-sm">
                                    {{ form.errors.email }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="phone">Phone</Label>
                                <Input id="phone" v-model="form.phone" type="text" />
                                <p v-if="form.errors.phone" class="text-red-500 text-sm">
                                    {{ form.errors.phone }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="tin">TIN</Label>
                                <Input id="tin" v-model="form.tin" type="text" />
                                <p v-if="form.errors.tin" class="text-red-500 text-sm">
                                    {{ form.errors.tin }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="address">Address</Label>
                                <Input id="address" v-model="form.address" type="text" />
                                <p v-if="form.errors.address" class="text-red-500 text-sm">
                                    {{ form.errors.address }}
                                </p>
                            </div>

                             <div class="space-y-2">
                                <Label for="isActive">Status</Label>
                                <Select v-model="form.isActive">
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="1">Active</SelectItem>
                                        <SelectItem value="0">Inactive</SelectItem>
                                    </SelectContent>
                                </Select>
                                <p v-if="form.errors.isActive" class="text-red-500 text-sm">
                                    {{ form.errors.isActive }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="isEBM">EBM</Label>
                                <Select v-model="form.isEBM">
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="1">Yes</SelectItem>
                                        <SelectItem value="0">No</SelectItem>
                                    </SelectContent>
                                </Select>
                                <p v-if="form.errors.isEBM" class="text-red-500 text-sm">
                                    {{ form.errors.isEBM }}
                                </p>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <Button 
                                type="submit" 
                                :disabled="form.processing"
                                :class="{ 'opacity-25': form.processing }"
                            >
                                Save
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>