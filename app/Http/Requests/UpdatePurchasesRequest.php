<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePurchasesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'purchaseId' => 'required|numeric|min:1',
            'status' => 'required|boolean',
            'products' => 'required|array',
            'products.*.productId' => 'required|numeric|min:1',
            'products.*.purchaseItemId' => 'required|numeric|min:1',
            'products.*.batchNumber' => 'required|string|max:100',
        ];
    }

    public function messages()
    {
        return [
            'products.*.productId.required' => 'Product ID is required',
            'products.*.purchaseItemId.required' => 'Purchase item ID is required',
            'products.*.batchNumber.required' => 'Batch number is required',
        ];
    }
}
