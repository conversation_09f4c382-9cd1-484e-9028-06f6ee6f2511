<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('CompanyBranch', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->references('id')->on('Company')->onDelete('restrict');
            $table->foreignId('branch_category_id')->references('id')->on('BranchCategory')->onDelete('restrict');
            $table->string('name');
            $table->string('address');
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->string('branchCode')->nullable()->default('00');
            $table->enum('mode', ['T', 'N'])->default('T');
            $table->string('mrc')->nullable()->default('hiqDemo');
            $table->string('topMessage')->nullable()->default('Welcome');
            $table->string('bottomMessage')->nullable()->default('Goodbye');
            $table->string('currency')->default('RWF');
            $table->boolean('isEBM')->default(false);
            $table->boolean('isActive')->default(true);
            $table->string('deviceSerial')->nullable('demo');
            $table->string('cluster')->default('demo');
            $table->string('clusterName')->default('demo');
            $table->boolean('isInitialised')->default(false);
            $table->boolean('isPrimary')->default(false);

            $table->string('purchase_last_request_date')->nullable();
            $table->string('importation_last_request_date')->nullable();


            $table->string('dvcId')->nullable();
            $table->string('lastPchsInvcNo')->nullable();
            $table->string('lastSaleInvcNo')->nullable();
            $table->string('vatTyCd')->nullable();

            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('CompanyBranch');
    }
};
