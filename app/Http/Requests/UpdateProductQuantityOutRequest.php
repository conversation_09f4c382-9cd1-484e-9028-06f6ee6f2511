<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProductQuantityOutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'quantity' => ['required', 'numeric', 'min:1'],
            'movingUnit' => 'nullable|string|max:100|in:main,sub',
            'batchNumber' => 'required|string|max:100',
            'description' => 'required|string|max:100|in:Expired,Damaged,Lost,Other',
        ];
    }

    public function messages()
    {
        return [
            'description.required' => 'Reason is required',
            'description.in' => 'Reason is invalid, must be in Expired,Damaged,Lost,Other',
        ];
    }
}
