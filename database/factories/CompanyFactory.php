<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Company>
 */
class CompanyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            //
            "user_id" => User::factory(),
            "name" => $this->faker->name(),
            "address" => $this->faker->address(),
            "tin" => $this->faker->numberBetween(100000, 999999),
            "phone" => $this->faker->phoneNumber(),
            "isActive" => true,
            "isEBM" => false,
        ];
    }
}
