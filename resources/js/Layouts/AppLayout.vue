<script setup>
import { ref, computed } from "vue";
import { Head, Link, router, usePage } from "@inertiajs/vue3";
import ApplicationMark from "@/Components/ApplicationMark.vue";
import Banner from "@/Components/Banner.vue";
import Dropdown from "@/Components/Dropdown.vue";
import DropdownLink from "@/Components/DropdownLink.vue";
import NavLink from "@/Components/NavLink.vue";
import ResponsiveNavLink from "@/Components/ResponsiveNavLink.vue";

import "@fortawesome/fontawesome-free/css/all.css";
import "@fortawesome/fontawesome-free/js/all.js";

defineProps({
    title: String,
    fullwidth: {
        type: Boolean,
        default: false,
        required: false,
    },
});

const showingNavigationDropdown = ref(false);
const showDropdown = ref(false);
const appHistory = ref([]);

router.on("before", (event) => {
    const currentUrl = usePage().url;
    const nextUrl = event.detail.visit.url.pathname;

    if (
        currentUrl &&
        currentUrl !== nextUrl &&
        appHistory.value[appHistory.value.length - 1] !== currentUrl
    ) {
        appHistory.value.push(currentUrl);
    }
});

const isDashboard = computed(() => route().current("dashboard"));
const showBackButton = computed(() => {
    return (
        !isDashboard.value &&
        (appHistory.value.length > 0 || window.history.length > 1)
    );
});

const goBack = () => {
    if (appHistory.value.length > 0) {
        const previousUrl = appHistory.value.pop();
        router.visit(previousUrl, {
            preserveState: true,
            preserveScroll: true,
            replace: false,
        });
    } else if (window.history.length > 1) {
        window.history.back();
    } else {
        router.visit(route("dashboard"), { preserveState: true });
    }
};

const logout = () => {
    router.post(route("logout"));
};

const toggleDropdown = () => {
    showDropdown.value = !showDropdown.value;
};
</script>

<template>
    <div>
        <Head :title="title" />
        <Banner />

        <div class="min-h-screen bg-white flex flex-col">
            <nav
                class="bg-white border-b border-gray-200 fixed w-full top-0 z-10"
            >
                <div class="mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex">
                            <div class="shrink-0 flex items-center">
                                <Link :href="route('dashboard')">
                                    <ApplicationMark class="block h-9 w-auto" />
                                </Link>
                            </div>
                        </div>

                        <div class="hidden sm:flex sm:items-center sm:ms-6">
                            <Dropdown align="right" width="48">
                                <template #trigger>
                                    <button
                                        v-if="
                                            $page.props.jetstream
                                                .managesProfilePhotos
                                        "
                                        class="flex text-sm hover:bg-zata-background focus:bg-gray-200 p-4"
                                    >
                                        <img
                                            class="h-10 w-10 rounded-full object-cover"
                                            :src="
                                                $page.props.auth.user
                                                    .profile_photo_url
                                            "
                                            :alt="$page.props.auth.user.name"
                                        />
                                        <div
                                            class="flex flex-col justify-left ml-3"
                                        >
                                            <p
                                                class="text-left text-md text-gray-700 font-bold"
                                            >
                                                {{ $page.props.auth.user.name }}
                                            </p>
                                            <p
                                                class="text-left text-xs text-gray-500"
                                            >
                                                {{
                                                    $page.props.auth.user.email
                                                }}
                                            </p>
                                        </div>
                                    </button>
                                    <span v-else class="inline-flex rounded-md">
                                        <button
                                            type="button"
                                            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none focus:bg-gray-50 active:bg-gray-50 transition"
                                        >
                                            {{ $page.props.auth.user.name }}
                                            <svg
                                                class="ms-2 -me-0.5 size-4"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M19.5 8.25l-7.5 7.5-7.5-7.5"
                                                />
                                            </svg>
                                        </button>
                                    </span>
                                </template>

                                <template #content>
                                    <div
                                        class="block px-4 py-2 text-xs text-gray-400"
                                    >
                                        Manage Account
                                    </div>
                                    <DropdownLink :href="route('profile.show')">
                                        Profile
                                    </DropdownLink>
                                    <DropdownLink
                                        :href="route('affiliate.index')"
                                    >
                                        Affiliate
                                    </DropdownLink>
                                    <DropdownLink
                                        :href="route('company.index')"
                                    >
                                        Company
                                    </DropdownLink>
                                    <DropdownLink
                                        :href="route('user.company')"
                                        v-if="$page.props.permission == 'Admin'"
                                    >
                                        Company Users
                                    </DropdownLink>
                                    <DropdownLink :href="route('company.list')">
                                        Switch Company
                                    </DropdownLink>
                                    <DropdownLink
                                        :href="route('subscription.index')"
                                    >
                                        Subscription
                                    </DropdownLink>
                                    <DropdownLink
                                        v-if="
                                            $page.props.jetstream
                                                .hasApiFeatures &&
                                            $page.props.permission == 'Admin'
                                        "
                                        :href="route('api-tokens.index')"
                                    >
                                        API Tokens
                                    </DropdownLink>
                                    <div
                                        class="block px-4 py-2 text-xs text-gray-400"
                                        v-if="
                                            $page.props.auth.user.whiteLabel ==
                                            'admin'
                                        "
                                    >
                                        Zata Administrator
                                    </div>
                                    <DropdownLink
                                        :href="route('admin.index')"
                                        v-if="
                                            $page.props.auth.user.whiteLabel ==
                                            'admin'
                                        "
                                    >
                                        Manage Companies
                                    </DropdownLink>
                                    <DropdownLink
                                        :href="route('account.index')"
                                        v-if="
                                            $page.props.auth.user.whiteLabel ==
                                            'admin'
                                        "
                                    >
                                        Manage Accounts
                                    </DropdownLink>
                                    <DropdownLink
                                        :href="route('admin.search')"
                                        v-if="
                                            $page.props.auth.user.whiteLabel ==
                                            'admin'
                                        "
                                    >
                                        Search Invocies
                                    </DropdownLink>
                                    <div class="border-t border-gray-200" />
                                    <form @submit.prevent="logout">
                                        <DropdownLink as="button">
                                            Log Out
                                        </DropdownLink>
                                    </form>
                                </template>
                            </Dropdown>
                        </div>

                        <div class="-me-2 flex items-center sm:hidden">
                            <button
                                @click="
                                    showingNavigationDropdown =
                                        !showingNavigationDropdown
                                "
                                class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 focus:text-gray-500 transition"
                            >
                                <svg
                                    class="size-6"
                                    stroke="currentColor"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        :class="{
                                            hidden: showingNavigationDropdown,
                                            'inline-flex':
                                                !showingNavigationDropdown,
                                        }"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M4 6h16M4 12h16M4 18h16"
                                    />
                                    <path
                                        :class="{
                                            hidden: !showingNavigationDropdown,
                                            'inline-flex':
                                                showingNavigationDropdown,
                                        }"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"
                                    />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div
                    :class="{
                        block: showingNavigationDropdown,
                        hidden: !showingNavigationDropdown,
                    }"
                    class="sm:hidden"
                >
                    <div class="pt-2 pb-3 space-y-1">
                        <ResponsiveNavLink
                            :href="route('pos')"
                            :active="route().current('pos')"
                        >
                            POS
                        </ResponsiveNavLink>
                        <ResponsiveNavLink
                            :href="route('customer.index')"
                            :active="route().current('customer.index')"
                        >
                            Customer
                        </ResponsiveNavLink>
                        <ResponsiveNavLink
                            :href="route('supplier.index')"
                            :active="route().current('supplier.index')"
                        >
                            Supplier
                        </ResponsiveNavLink>
                        <ResponsiveNavLink
                            :href="route('product.index')"
                            :active="route().current('product.index')"
                        >
                            Products
                        </ResponsiveNavLink>
                        <ResponsiveNavLink
                            :href="route('product.stockHistory')"
                            :active="route().current('product.stockHistory')"
                        >
                            Stock History
                        </ResponsiveNavLink>
                        <ResponsiveNavLink
                            :href="route('transaction.sale')"
                            :active="route().current('transaction.sale')"
                        >
                            Sales
                        </ResponsiveNavLink>
                        <ResponsiveNavLink
                            :href="route('transaction.refund')"
                            :active="route().current('transaction.refund')"
                        >
                            Refund
                        </ResponsiveNavLink>
                        <ResponsiveNavLink
                            :href="route('transaction.proforma')"
                            :active="route().current('transaction.proforma')"
                        >
                            Quotation
                        </ResponsiveNavLink>
                        <ResponsiveNavLink
                            :href="route('expense.index')"
                            :active="route().current('expense.index')"
                        >
                            Expense
                        </ResponsiveNavLink>
                        <ResponsiveNavLink
                            :href="route('transaction.account')"
                            :active="route().current('transaction.account')"
                            v-if="$page.props.permission == 'Admin'"
                        >
                            Finance
                        </ResponsiveNavLink>
                        <ResponsiveNavLink
                            :href="route('transaction.payment')"
                            :active="route().current('transaction.payment')"
                            v-if="$page.props.permission == 'Admin'"
                        >
                            Payments
                        </ResponsiveNavLink>
                    </div>

                    <div class="pt-4 pb-1 border-t border-gray-200">
                        <div class="flex items-center px-4">
                            <div
                                v-if="
                                    $page.props.jetstream.managesProfilePhotos
                                "
                                class="shrink-0 me-3"
                            >
                                <img
                                    class="size-10 rounded-full object-cover"
                                    :src="
                                        $page.props.auth.user.profile_photo_url
                                    "
                                    :alt="$page.props.auth.user.name"
                                />
                            </div>
                            <div>
                                <div
                                    class="font-medium text-base text-gray-800"
                                >
                                    {{ $page.props.auth.user.name }}
                                </div>
                                <div class="font-medium text-sm text-gray-500">
                                    {{ $page.props.auth.user.email }}
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 space-y-1">
                            <ResponsiveNavLink
                                :href="route('profile.show')"
                                :active="route().current('profile.show')"
                            >
                                Profile
                            </ResponsiveNavLink>
                            <ResponsiveNavLink
                                v-if="$page.props.jetstream.hasApiFeatures"
                                :href="route('api-tokens.index')"
                                :active="route().current('api-tokens.index')"
                            >
                                API Tokens
                            </ResponsiveNavLink>
                            <ResponsiveNavLink
                                :href="route('admin.index')"
                                v-if="
                                    $page.props.auth.user.whiteLabel == 'admin'
                                "
                            >
                                Administrator
                            </ResponsiveNavLink>
                            <ResponsiveNavLink :href="route('company.index')">
                                Company
                            </ResponsiveNavLink>
                            <ResponsiveNavLink
                                :href="route('user.company')"
                                v-if="$page.props.permission == 'Admin'"
                            >
                                Company Users
                            </ResponsiveNavLink>
                            <ResponsiveNavLink :href="route('company.list')">
                                Switch Company
                            </ResponsiveNavLink>
                            <form @submit.prevent="logout">
                                <ResponsiveNavLink as="button">
                                    Log Out
                                </ResponsiveNavLink>
                            </form>
                        </div>
                    </div>
                </div>
            </nav>

            <div class="flex w-full mx-auto mb-12 pt-16">
                <nav
                    class="w-2/12 px-4 flex-col space-y-4 py-2 h-screen fixed top-0 left-0 overflow-y-auto hidden xl:flex"
                    aria-label="Sidebar Navigation"
                    v-if="$slots.sidenav"
                >
                    <NavLink
                        :href="route('dashboard')"
                        :active="route().current('dashboard')"
                        class="mt-20"
                    >
                        <i class="fa-solid fa-house h-4 mr-2"></i>
                        Home
                    </NavLink>

                    <p class="font-bold">Create</p>
                    <NavLink
                        :href="route('pos')"
                        :active="route().current('pos')"
                    >
                        <i class="fa-solid fa-cart-arrow-down h-4 mr-2"></i>
                        Receipt (POS)
                    </NavLink>
                    <NavLink
                        :href="route('transaction.proforma.create')"
                        :active="route().current('transaction.proforma.create')"
                    >
                        <i class="fa-solid fa-cart-arrow-down h-4 mr-2"></i>
                        Quotation
                    </NavLink>
                    <NavLink
                        :href="route('transaction.purchase.create')"
                        :active="route().current('transaction.purchase.create')"
                    >
                        <i class="fa-solid fa-cart-arrow-down h-4 mr-2"></i>
                        Purchase order
                    </NavLink>
                    <p class="font-bold">Sales</p>
                    <NavLink
                        :href="route('transaction.sale')"
                        :active="route().current('transaction.sale')"
                    >
                        <i class="fa-solid fa-cart-plus h-4 mr-2"></i>
                        Transaction
                    </NavLink>
                    <NavLink
                        :href="route('transaction.refund')"
                        :active="route().current('transaction.refund')"
                    >
                        <i
                            class="fa-solid fa-arrow-down-wide-short h-4 mr-2"
                        ></i>
                        Refund transaction
                    </NavLink>
                    <NavLink
                        :href="route('transaction.proforma')"
                        :active="route().current('transaction.proforma')"
                    >
                        <i class="fa-solid fa-dolly h-4 mr-2"></i>
                        Quotation orders
                    </NavLink>
                    <NavLink
                        :href="route('transaction.purchase')"
                        :active="route().current('transaction.purchase')"
                    >
                        <i class="fa-solid fa-truck h-4 mr-2"></i>
                        Purchase orders
                    </NavLink>
                    <p class="font-bold">Clients</p>
                    <NavLink
                        :href="route('customer.index')"
                        :active="route().current('customer.index')"
                    >
                        <i class="fa-solid fa-address-card h-4 mr-2"></i>
                        Customer
                    </NavLink>
                    <NavLink
                        :href="route('supplier.index')"
                        :active="route().current('supplier.index')"
                    >
                        <i class="fa-solid fa-address-book h-4 mr-2"></i>
                        Supplier
                    </NavLink>

                    <p class="font-bold">Stock</p>
                    <NavLink
                        :href="route('product.index')"
                        :active="route().current('product.index')"
                    >
                        <i class="fa-solid fa-basket-shopping h-4 mr-2"></i>
                        Product
                    </NavLink>
                    <NavLink
                        class="hidden"
                        :href="route('product.sync')"
                        :active="route().current('product.sync')"
                        v-if="
                            ['Admin', 'Warehouse manager'].includes(
                                $page.props.permission
                            )
                        "
                    >
                        <i class="fa-solid fa-truck-arrow-right h-4 mr-2"></i>
                        Sync
                    </NavLink>
                    <NavLink
                        :href="route('product.stockHistory')"
                        :active="route().current('product.stockHistory')"
                    >
                        <i class="fa-solid fa-box-open h-4 mr-2"></i>
                        History
                    </NavLink>
                    <NavLink
                        :href="route('import.index')"
                        :active="route().current('import.index')"
                        v-if="
                            ['Admin', 'Warehouse manager'].includes(
                                $page.props.permission
                            )
                        "
                    >
                        <i class="fa-solid fa-truck-plane h-4 mr-2"></i>
                        Imports (EBM)
                    </NavLink>
                    <NavLink
                        :href="route('purchase.index')"
                        :active="route().current('purchase.index')"
                        v-if="
                            ['Admin', 'Warehouse manager'].includes(
                                $page.props.permission
                            )
                        "
                    >
                        <i
                            class="fa-solid fa-cart-flatbed-suitcase h-4 mr-2"
                        ></i>
                        Purhcase (EBM)
                    </NavLink>
                    <p class="font-bold">Account</p>
                    <NavLink
                        :href="route('profile.show')"
                        :active="route().current('profile.show')"
                    >
                        <i class="fa-solid fa-users h-4 mr-2"></i>
                        Overview
                    </NavLink>
                    <NavLink
                        :href="route('transaction.payment')"
                        :active="route().current('transaction.payment')"
                        v-if="$page.props.permission == 'Admin'"
                    >
                        <i class="fa-solid fa-money-bill-transfer h-4 mr-2"></i>
                        Payments
                    </NavLink>
                    <NavLink
                        :href="route('expense.index')"
                        :active="route().current('expense.index')"
                        v-if="
                            ['Admin', 'Warehouse manager'].includes(
                                $page.props.permission
                            )
                        "
                    >
                        <i
                            class="fa-solid fa-filter-circle-dollar h-4 mr-2"
                        ></i>
                        Expense
                    </NavLink>

                    <p class="font-bold">Profile</p>
                    <NavLink
                        :href="route('company.index')"
                        :active="route().current('company.index')"
                    >
                        <i class="fa-solid fa-users h-4 mr-2"></i>
                        Company
                    </NavLink>

                    <NavLink
                        :href="route('transaction.account')"
                        :active="route().current('transaction.account')"
                        v-if="$page.props.permission == 'Admin'"
                    >
                        <i
                            class="fa-solid fa-circle-dollar-to-slot h-4 mr-2"
                        ></i>
                        Finance
                    </NavLink>

                    <NavLink
                        class="hidden"
                        :href="route('insurance.index')"
                        :active="route().current('insurance.index')"
                    >
                        <i
                            class="fa-solid fa-hands-holding-circle h-4 mr-2"
                        ></i>
                        Insurance
                    </NavLink>
                    <NavLink
                        :href="route('logTrack')"
                        :active="route().current('logTrack')"
                    >
                        <i class="fa-solid fa-file-pen mr-2"></i>
                        Logs
                    </NavLink>
                </nav>

                <div
                    :class="{
                        'xl:w-10/12 xl:ml-[16.666667%]': $slots.sidenav,
                        'w-full': !$slots.sidenav,
                    }"
                    class="flex flex-col space-y-4 px-4 w-full"
                >
                    <div v-if="showBackButton" class="flex items-center mb-2">
                        <button
                            @click="goBack"
                            class="inline-flex items-center px-4 py-2 bg-zata-background border text-zata-primary-dark text-sm font-medium hover:bg-zata-background-dark focus:outline-none transition rounded mt-2"
                        >
                            <i class="fa-solid fa-arrow-left mr-2"></i>
                            Back
                        </button>
                    </div>

                    <main
                        class="mx-auto w-full"
                        :class="{ 'max-w-[1500px]': !fullwidth }"
                    >
                        <slot />
                    </main>
                </div>
            </div>

            <footer
                class="border-0 border-t border-gray-200 mx-auto mt-auto font-bold w-full"
                v-if="$slots.footer"
            >
                <div class="max-w-7xl px-2 py-6 mx-auto bg-white rounded my-4">
                    <div
                        class="flex flex-col-reverse justify-between pb-2 lg:flex-row"
                    >
                        <p class="text-sm text-gray-600 text-center">
                            © 2025 ZATA Global. All rights reserved.
                        </p>
                        <ul
                            class="flex flex-col mb-3 space-y-2 lg:mb-0 sm:space-y-0 sm:space-x-5 sm:flex-row"
                        >
                            <li class="text-center">
                                <a
                                    target="_blank"
                                    :href="route('policy.show')"
                                    class="text-sm text-linidea-10 transition-colors duration-300 hover:text-deep-purple-accent-400"
                                    >Privacy Policy</a
                                >
                            </li>
                            <li class="text-center">
                                <a
                                    target="_blank"
                                    :href="route('terms.show')"
                                    class="text-sm text-linidea-10 transition-colors duration-300 hover:text-deep-purple-accent-400"
                                    >Terms & Conditions</a
                                >
                            </li>
                        </ul>
                    </div>
                </div>
            </footer>
            <div class="fixed bottom-8 right-4">
                <button
                    @click="toggleDropdown"
                    class="rounded-full bg-zata-primary-dark border border-zata-primary-dark text-white w-10 h-10 flex items-center justify-center text-3xl font-bold transform transition-all hover:scale-110"
                >
                    +
                </button>
                <div
                    v-if="showDropdown"
                    class="absolute bottom-16 right-0 bg-white border border-gray-200 rounded-lg shadow-lg w-48"
                >
                    <ul class="py-2">
                        <li>
                            <Link
                                :href="route('customer.new')"
                                class="block px-4 py-2 hover:bg-zata-background-dark text-zata-primary-dark"
                            >
                                <i class="fa-solid fa-user-plus"></i>
                                <span class="ml-2">Add customer</span>
                            </Link>
                        </li>
                        <li>
                            <Link
                                :href="route('product.new')"
                                class="block px-4 py-2 hover:bg-zata-background-dark text-zata-primary-dark"
                            >
                                <i class="fa-solid fa-cart-plus"></i>
                                <span class="ml-2">Add product</span>
                            </Link>
                        </li>
                        <li>
                            <Link
                                :href="route('expense.new')"
                                class="block px-4 py-2 hover:bg-zata-background-dark text-zata-primary-dark"
                            >
                                <i class="fa-solid fa-hourglass-start"></i>
                                <span class="ml-3">Add expense</span>
                            </Link>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</template>
