<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm } from '@inertiajs/vue3';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const props = defineProps({
    Import: Object,
    Products: Array,
});

const form = useForm({
    productID: null,
    status: "1",
    salePrice: '',
    purchasePrice: props.Import.invcFcurAmt || '',
    batchNumber: '',
    expireDate: '',
    discountRate: '',
    name: props.Import.itemNm
});

function submit() {
    form.put(route('import.update', props.Import.id));
}
</script>

<template>
    <AppLayout title="Importation">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">Import Details</h2>
        </template>

        <div class="py-6  mx-auto space-y-6">
            <!-- Import Summary -->
            <Card>
                <CardContent class="p-4">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>DCL No</TableHead>
                                <TableHead>Task Code</TableHead>
                                <TableHead>Item Name</TableHead>
                                <TableHead>HS Code</TableHead>
                                <TableHead>Quantity</TableHead>
                                <TableHead>Status</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            <TableRow>
                                <TableCell>{{ props.Import.dclNo }}</TableCell>
                                <TableCell>{{ props.Import.taskCd }}</TableCell>
                                <TableCell>{{ props.Import.itemNm }}</TableCell>
                                <TableCell>{{ props.Import.hsCd }}</TableCell>
                                <TableCell>{{ props.Import.qty }}</TableCell>
                                <TableCell>{{ props.Import.status }}</TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            <div class="py-6 px-4 sm:px-6 lg:px-8">
                <Card class="shadow-lg border-none max-w-3xl mx-auto">
                    <CardHeader class="bg-gray-50 border-b">
                        <CardTitle class="text-xl font-semibold text-gray-800">
                           Product details 
                        </CardTitle>
                    </CardHeader>
                    <CardContent class="p-6">
                        <form @submit.prevent="submit" class="space-y-6">
                            <div class="space-y-2">
                                <Label for="name">Name</Label>
                                <Input id="name" v-model="form.name" type="text" disabled />
                                <p v-if="form.errors.name" class="text-red-500 text-sm mt-1">{{ form.errors.name }}</p>
                            </div>

                            <div class="space-y-2">

                                <label class="block text-sm font-medium mb-1 text-gray-700">Select Product</label>
                                <Select v-model="form.productID">
                                    <SelectTrigger>
                                        <SelectValue placeholder="Choose product" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem v-for="product in props.Products" :key="product.id"
                                            :value="product.id">
                                            {{ product.name }}
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                                <p v-if="form.errors.productID" class="text-red-500 text-sm mt-1">{{
                                    form.errors.productID
                                    }}</p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-2">
                                    <Label for="salePrice">Sale Price</Label>
                                    <input v-model="form.salePrice" type="number" min="1"
                                        class="w-full border border-gray-300 rounded-md shadow-sm px-3 py-2"
                                        placeholder="Enter sale price" />
                                    <p v-if="form.errors.salePrice" class="text-red-500 text-sm mt-1">{{
                                        form.errors.salePrice
                                        }}</p>
                                </div>
                                <div class="space-y-2">
                                    <Label for="purchasePrice">Purchase Price</Label>
                                    <input v-model="form.purchasePrice" type="number" min="1"
                                        class="w-full border border-gray-300 rounded-md shadow-sm px-3 py-2"
                                        placeholder="Enter purchase price" />
                                    <p v-if="form.errors.purchasePrice" class="text-red-500 text-sm mt-1">{{
                                        form.errors.purchasePrice }}</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium mb-1 text-gray-700">Batch Number</label>
                                    <input v-model="form.batchNumber" type="text" maxlength="100"
                                        class="w-full border border-gray-300 rounded-md shadow-sm px-3 py-2"
                                        placeholder="Enter batch number" />
                                    <p v-if="form.errors.batchNumber" class="text-red-500 text-sm mt-1">{{
                                        form.errors.batchNumber }}</p>
                                </div>
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium mb-1 text-gray-700">Expire Date</label>
                                    <input v-model="form.expireDate" type="date"
                                        class="w-full border border-gray-300 rounded-md shadow-sm px-3 py-2" />
                                    <p v-if="form.errors.expireDate" class="text-red-500 text-sm mt-1">{{
                                        form.errors.expireDate
                                        }}</p>
                                </div>
                            </div>

                            <div>
                                <Label for="status">Status</Label>
                                <Select v-model="form.status">
                                    <SelectTrigger>
                                        <SelectValue placeholder="Choose status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="1">Accept</SelectItem>
                                        <SelectItem value="0">Reject</SelectItem>
                                    </SelectContent>
                                </Select>
                                <p v-if="form.errors.status" class="text-red-500 text-sm mt-1">{{ form.errors.status
                                }}</p>
                            </div>
                            <!-- Submit Button -->
                            <div class="pt-4 flex justify-end">
                                <Button type="submit" :disabled="form.processing" class="w-full lg:w-auto px-6 py-2"
                                    :class="{ 'opacity-50': form.processing }">
                                    Link Product
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </div>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>
