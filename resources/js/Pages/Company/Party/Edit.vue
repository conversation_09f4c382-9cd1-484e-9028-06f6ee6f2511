<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from "@inertiajs/vue3";
import FormSection from '@/Components/FormSection.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import { Button } from '@/components/ui/button';

import "@fortawesome/fontawesome-free/css/all.css";
import "@fortawesome/fontawesome-free/js/all.js";
const props = defineProps({
    Party: Object,
});

const form = useForm({
    name: props.Party.name,
    email: props.Party.email,
    phone: props.Party.phone,
    address: props.Party.address,
    tin: props.Party.tin,
    partyTypeID: props.Party.party_type_id,

});

function updateParty() {
    form.put(route('party.update', props.Party.id));
}
</script>

<template>
    <AppLayout title="Edit Party">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Edit Party
            </h2>
        </template>

        <FormSection>
            <template #title>
                Supplier Information
            </template>

            <template #form>
                <form class="" @submit.prevent="updateParty">
                    <div class="pb-4">
                        <InputLabel for="name" value="Name" />
                        <TextInput id="name" v-model="form.name" type="text" />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>
                    <div class="pb-4">
                        <InputLabel for="email" value="Email" />
                        <TextInput id="email" v-model="form.email" type="email" />
                        <InputError class="mt-2" :message="form.errors.email" />
                    </div>

                    <div class="pb-4">
                        <InputLabel for="phone" value="Phone" />
                        <TextInput id="phone" v-model="form.phone" type="text" />
                        <InputError class="mt-2" :message="form.errors.phone" />
                    </div>

                    <div class="pb-4">
                        <InputLabel for="address" value="Address" />
                        <TextInput id="address" v-model="form.address" type="text" />
                        <InputError class="mt-2" :message="form.errors.address" />
                    </div>

                    <div class="pb-4">
                        <InputLabel for="tin" value="Tin" />
                        <TextInput id="tin" v-model="form.tin" type="text" />
                        <InputError class="mt-2" :message="form.errors.tin" />
                    </div>

                    <div class="flex justify-between mt-4">
                        <Link  :href="route('supplier.index')" class="p-2 rounded px-4 border border-gray-300">
                            <i class="fa-solid fa-chevron-left"></i>
                            Back

                        </Link>
                        <Button :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                            Update
                        </Button>
                    </div>
                </form>
            </template>
        </FormSection>
        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>
