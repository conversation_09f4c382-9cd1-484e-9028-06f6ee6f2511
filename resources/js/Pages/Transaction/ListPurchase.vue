<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { reactive, watch,getCurrentInstance } from 'vue';
import { useForm, Link } from "@inertiajs/vue3";
import TableSection from '@/Components/TableSection.vue';
import { computed } from 'vue';
import PrimaryLink from '@/Components/PrimaryLink.vue';


const props = defineProps({
    Transactions: Array,
    currentPage: Number,
    lastPage: Number,
    itemsPerPage: Number,
    pageItems: Number,
    total: Number,
});

const instance = getCurrentInstance();
const zataCurrency = instance.appContext.config.globalProperties.$zataCurrency;


const tableHeaders = ["NO", "Supplier Name", "Total Amount", "Items", "Date", "Status"];

const tableRows = computed(() => {
    return props.Transactions.map((transaction) => ({
        id: transaction.id,
        clientName: transaction.clientName,
        totalAmount: zataCurrency(transaction.totalAmount),
        itemsCount: transaction.itemsCount,
        salesDate: transaction.salesDate,
        isRefunded: transaction.status,
    }));
});
</script>

<template>
    <AppLayout title="Purchase order">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Purchase order
            </h2>
        </template>

        <div>
            <TableSection :headers="tableHeaders" :rows="tableRows" :current-page="props.currentPage"
                :last-page="props.lastPage" :items-per-page="props.itemsPerPage" title="Purchase order"
                notFoundMessage="No  transaction found." :is-dynamic="true" :dynamic-url="'transaction.purchase.show'">
                <template #actions>
                    <PrimaryLink :href="route('transaction.purchase.create')">Add new</PrimaryLink>
                </template>
            </TableSection>
        </div>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>