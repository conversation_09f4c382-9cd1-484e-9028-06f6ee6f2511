<?php

namespace App\Services;

use Barryvdh\Snappy\Facades\SnappyPdf;
use Illuminate\Support\Facades\Log;

class PdfService
{
    public function transactionInvoice($transaction, $company, $branch, $copy)
    {
        return $this->generatePdfStream(
            'Invoice.TransactionInvoice',
            compact('transaction', 'company', 'branch', 'copy')
        );
    }
    public function smallTransactionInvoice($transaction, $company, $branch, $copy)
    {
        return $this->generatePdfStream(
            'Invoice.SmallTransactionInvoice',
            compact('transaction', 'company', 'branch', 'copy')
        );
    }

    public function smallTransactionInvoice58($transaction, $company, $branch, $copy)
    {
        return $this->generatePdfStream(
            'Invoice.SmallTransactionInvoice58',
            compact('transaction', 'company', 'branch', 'copy')
        );
    }

    public function deliveryNote($transaction, $company, $branch, $copy = false)
    {
        return $this->generatePdfStream(
            'Invoice.DeliveryNote',
            compact('transaction', 'company', 'branch', 'copy')
        );
    }

    public function stockHistory($items, $company, $branch)
    {

        return $this->generatePdfStream2(
            'Invoice.StockHistory',
            compact('items', 'company', 'branch')
        );
    }

    private function generatePdfStream2(string $view, array $data)
    {
        Log::info('Generating PDF', ['view' => $view]);

        $html = view($view, $data)->render();
        $pdf = SnappyPdf::loadHTML($html);
        return $pdf->output();
    }

    private function generatePdfStream(string $view, array $data)
    {
        Log::info('Generating PDF', ['view' => $view]);
        $transaction = $this->convert($data['transaction'], 5); // Limit recursion depth
        $transaction->items = collect($transaction->items);
        $data['transaction'] = $transaction;

        $html = view($view, $data)->render();
        $pdf = SnappyPdf::loadHTML($html)
            // ->setOption('page-width', '80mm') // Set width to 80mm
            ->setOption('page-height', null)  // Let height be dynamic
            ->setOption('margin-top', 0)
            ->setOption('margin-bottom', 0)
            ->setOption('margin-left', 0)
            ->setOption('margin-right', 0)
            ->setOption('disable-smart-shrinking', true); // Prevent scaling issues

        return $pdf->output();
    }

    private function convert($data, $maxDepth = 5, $currentDepth = 0)
    {
        if ($currentDepth >= $maxDepth) {
            Log::warning('Max recursion depth reached', ['data' => $data]);
            return $data; // Stop recursion
        }

        if (is_array($data)) {
            $result = new \stdClass();
            foreach ($data as $key => $value) {
                $result->$key = $this->convert($value, $maxDepth, $currentDepth + 1);
            }
            return $result;
        }
        return $data;
    }

    public function purchaseInvoice($transaction, $company, $branch, $copy = false)
    {
        return $this->generatePdfStream(
            'Invoice.PurchaseInvoice',
            compact('transaction', 'company', 'branch', 'copy')
        );
    }
}
