<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from "@inertiajs/vue3";
import FormSection from '@/Components/FormSection.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';


const props = defineProps({

});

const form = useForm({
    name: '',
    email: '',
    phone: '',
    address: '',
    tin: '',
    partyTypeID: 2,

});

function createNewParty() {
    form.post(route('supplier.store'), {
    });
}
</script>

<template>
    <AppLayout title="New  Supplier">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                New Supplier
            </h2>
        </template>

        <FormSection>
            <template #title>
                Supplier Information
            </template>
            <template #form>
                <form class="" @submit.prevent="createNewParty">
                    <div class="pb-4">
                        <InputLabel for="name" value="Name" />
                        <TextInput id="name" v-model="form.name" type="text" />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>
                    <div class="grid grid-cols-6 gap-6">
                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="email" value="Email" />
                            <TextInput id="email" v-model="form.email" type="email" />
                            <InputError class="mt-2" :message="form.errors.email" />
                        </div>

                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="phone" value="Phone" />
                            <TextInput id="phone" v-model="form.phone" type="text" />
                            <InputError class="mt-2" :message="form.errors.phone" />
                        </div>
                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="address" value="Address" />
                            <TextInput id="address" v-model="form.address" type="text" />
                            <InputError class="mt-2" :message="form.errors.address" />
                        </div>

                        <div class="col-span-6 sm:col-span-3">
                            <InputLabel for="tin" value="Tin" />
                            <TextInput id="tin" v-model="form.tin" type="text" />
                            <InputError class="mt-2" :message="form.errors.tin" />
                        </div>
                    </div>

                    <div class="flex justify-end mt-4">
                        <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                            Save
                        </PrimaryButton>
                    </div>
                </form>


            </template>
        </FormSection>
        <template #sidenav />
      <template #footer />
    </AppLayout>
</template>
