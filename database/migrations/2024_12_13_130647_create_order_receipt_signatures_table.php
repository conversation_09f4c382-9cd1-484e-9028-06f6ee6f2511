<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('OrderReceiptSignature', function (Blueprint $table) {
            $table->id();
            $table->foreignId("order_id")->references("id")->on("Orders");
            $table->unsignedBigInteger('receiptNumber');
            $table->string('internalData')->nullable();
            $table->string('receiptSignature')->nullable();
            $table->unsignedBigInteger('totalReceiptNumber');
            $table->bigInteger('vsdcReceiptPublishDate');
            $table->string('sdcId');
            $table->string('mrcNumber');
            $table->unsignedBigInteger('invoiceNumber');
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('OrderReceiptSignature');
    }
};
