<?php

use App\Enums\StockTypeEnums;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\TransactionTypeEnums;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ProductStockHistory', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->references('id')->on('CompanyBranch')->onDelete('restrict');
            $table->foreignId('to_branch_id')->nullable()->references('id')->on('CompanyBranch')->onDelete('restrict');
            $table->foreignId('product_id')->references('id')->on('Product')->onDelete('restrict');
            $table->foreignId('user_id')->references('id')->on('users')->onDelete('restrict');
            $table->foreignId('party_id')->nullable()->references('id')->on('CompanyParty')->onDelete('restrict');
            $table->string('productCode')->nullable();
            $table->string('batchNumber')->nullable();
            $table->decimal('quantity', 11, 2);
            $table->decimal('oldQuantity', 11, 2);
            $table->string('orderType', 20)->nullable()->default(TransactionTypeEnums::SALES->value);
            $table->enum('stockType', ['In', 'Out', 'In From', 'Out To'])->default(StockTypeEnums::IN->value);
            $table->string('description')->nullable();
            $table->bigInteger("date")->nullable();
            $table->bigInteger("expiryDate")->nullable();
            $table->bigInteger("confirmationDate")->nullable();
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *  check workflows for the migration
     */
    public function down(): void
    {
        Schema::dropIfExists('ProductStockHistory');
    }
};
