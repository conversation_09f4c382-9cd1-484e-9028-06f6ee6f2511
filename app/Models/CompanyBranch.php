<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class CompanyBranch extends Model
{

    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'CompanyBranch';

    protected $fillable = [
        "company_id",
        "name",
        "phone",
        "email",
        "address",
        "branchCode",
        "mode",
        "mrc",
        "topMessage",
        "bottomMessage",
        "currency",
        "isPrimary",
        "isEBM",
        "isActive",
        "branch_category_id",
        "deviceSerial",
        "cluster",
        "clusterName",
        "isInitialised",
        'purchase_last_request_date',
        'importation_last_request_date',
        'dvcId',
        'lastPchsInvcNo',
        'lastSaleInvcNo',
        'vatTyCd',
    ];

    protected $casts = [
        'isInitialised' => 'boolean',
        'isPrimary' => 'boolean',
        'isEBM' => 'boolean',
        'isActive' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'branch_id', 'id');
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'branch_id', 'id');
    }

    public function invoiceNumber(): HasOne
    {
        return $this->hasOne(InvoiceSequences::class, 'branch_id', 'id');
    }
}
