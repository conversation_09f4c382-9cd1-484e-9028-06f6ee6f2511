<script setup>
import PrimaryLink from '@/Components/PrimaryLink.vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";
import { computed } from 'vue';
import { Button } from '@/components/ui/button';

const props = defineProps({
    Companies: Array | Object,
});

const sortedCompanies = computed(() => {
    return [...props.Companies].sort((a, b) => b.isDefault - a.isDefault);
});
</script>

<template>
    <AppLayout title="Company Branches">

        <div class="flex justify-start w-full py-8">
            <Button as-child class="text-center" type="button">
                <Link :href="route('company.new')">Add Company</Link>
            </Button>

        </div>
        <section class="text-gray-600 body-font" v-if="sortedCompanies.length > 0">
            <div class="container px-5 py-5 mx-auto">
                <div class="flex flex-wrap -m-2">
                    <div class="p-2 lg:w-1/3 md:w-1/2 w-full " v-for="company in sortedCompanies" :key="company.id"
                        :class="{ 'cursor-pointer': true }"
                        @click="() => $inertia.get(route('company.switch', company.id))">
                        <div
                            class="h-full flex items-center border-gray-300 border p-4 rounded-lg bg-zata-background hover:bg-zata-background-dark">
                            <img alt=""
                                class="w-16 h-16  object-cover object-center flex-shrink-0 rounded-full mr-4 border border-gray-300"
                                :src="company.image" />
                            <div class="flex-grow">
                                <h2 class="text-gray-900 title-font font-medium">{{ company.name }}</h2>
                                <p class="text-gray-500">{{ company.tin }}</p>
                                <span v-if="company.isDefault"
                                    class="text-xs text-white bg-blue-500 rounded-full px-2 py-1">Default</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <div v-else class="flex justify-center items-center h-64">
            <p>No companies found.</p>
        </div>
        <template #footer>
        </template>
    </AppLayout>
</template>