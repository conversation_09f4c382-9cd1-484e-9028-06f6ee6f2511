<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class DpoPaymentService
{
    private string $baseUrl;
    private string $companyToken;
    
    public function __construct()
    {
        $this->baseUrl = config('dpo.base_url', 'https://secure.3gdirectpay.com/API/v6/');
        $this->companyToken = config('dpo.company_token');
        
        if (!$this->companyToken) {
            throw new Exception('DPO Company Token is required');
        }
    }

    /**
     * Create a new payment token
     */
    public function createToken(array $transactionData, array $services, array $allocations = [], array $additional = [], array $travelers = []): array
    {
        $this->validateCreateTokenData($transactionData, $services);
        
        $xmlData = $this->buildCreateTokenXml($transactionData, $services, $allocations, $additional, $travelers);
        
        return $this->makeRequest($xmlData);
    }

    /**
     * Verify transaction token status
     */
    public function verifyToken(string $transactionToken = null, string $companyRef = null, bool $verifyTransaction = true, array $customerData = []): array
    {
        if (!$transactionToken && !$companyRef) {
            throw new Exception('Either TransactionToken or CompanyRef is required');
        }

        $xml = new \SimpleXMLElement('<API3G/>');
        $xml->addChild('CompanyToken', $this->companyToken);
        $xml->addChild('Request', 'verifyToken');
        
        if ($transactionToken) {
            $xml->addChild('TransactionToken', $transactionToken);
        }
        
        if ($companyRef) {
            $xml->addChild('CompanyRef', $companyRef);
        }
        
        $xml->addChild('VerifyTransaction', $verifyTransaction ? '1' : '0');
        
        // Add optional customer data
        foreach ($customerData as $key => $value) {
            if (in_array($key, ['ACCref', 'customerPhone', 'customerPhonePrefix', 'customerEmail'])) {
                $xml->addChild($key, $value);
            }
        }

        return $this->makeRequest($xml->asXML());
    }

    /**
     * Update existing token
     */
    public function updateToken(string $transactionToken, array $updateData): array
    {
        $this->validateUpdateTokenData($updateData);
        
        $xml = new \SimpleXMLElement('<API3G/>');
        $xml->addChild('CompanyToken', $this->companyToken);
        $xml->addChild('Request', 'updateToken');
        $xml->addChild('TransactionToken', $transactionToken);
        
        $allowedFields = [
            'PaymentAmount', 'CompanyRef', 'CustomerEmail', 'CustomerFirstName',
            'CustomerLastName', 'CustomerAddress', 'CustomerCity', 'CustomerCountry',
            'CustomerDialCode', 'CustomerPhone', 'CustomerZip', 'CompanyAccRef', 'UserToken'
        ];
        
        foreach ($updateData as $key => $value) {
            if (in_array($key, $allowedFields)) {
                $xml->addChild($key, $value);
            }
        }

        return $this->makeRequest($xml->asXML());
    }

    /**
     * Refund a paid transaction
     */
    public function refundToken(string $transactionToken, float $refundAmount, string $refundDetails, string $refundRef = null, bool $refundApproval = false): array
    {
        if ($refundAmount <= 0) {
            throw new Exception('Refund amount must be greater than 0');
        }
        
        if (empty($refundDetails)) {
            throw new Exception('Refund details are required');
        }

        $xml = new \SimpleXMLElement('<API3G/>');
        $xml->addChild('Request', 'refundToken');
        $xml->addChild('CompanyToken', $this->companyToken);
        $xml->addChild('TransactionToken', $transactionToken);
        $xml->addChild('refundAmount', (string)$refundAmount);
        $xml->addChild('refundDetails', $refundDetails);
        
        if ($refundRef) {
            $xml->addChild('refundRef', $refundRef);
        }
        
        if ($refundApproval) {
            $xml->addChild('refundApproval', '1');
        }

        return $this->makeRequest($xml->asXML());
    }

    /**
     * Cancel active transaction
     */
    public function cancelToken(string $transactionToken): array
    {
        $xml = new \SimpleXMLElement('<API3G/>');
        $xml->addChild('CompanyToken', $this->companyToken);
        $xml->addChild('Request', 'cancelToken');
        $xml->addChild('TransactionToken', $transactionToken);

        return $this->makeRequest($xml->asXML());
    }

    /**
     * Send email to customer for transaction
     */
    public function emailToToken(string $transactionToken): array
    {
        $xml = new \SimpleXMLElement('<API3G/>');
        $xml->addChild('CompanyToken', $this->companyToken);
        $xml->addChild('Request', 'emailToToken');
        $xml->addChild('TransactionToken', $transactionToken);

        return $this->makeRequest($xml->asXML());
    }

    /**
     * Charge mobile payment
     */
    public function chargeTokenMobile(string $transactionToken, string $phoneNumber, string $mno, string $mnoCountry): array
    {
        $this->validateMobilePaymentData($phoneNumber, $mno, $mnoCountry);

        $xml = new \SimpleXMLElement('<API3G/>');
        $xml->addChild('CompanyToken', $this->companyToken);
        $xml->addChild('Request', 'ChargeTokenMobile');
        $xml->addChild('TransactionToken', $transactionToken);
        $xml->addChild('PhoneNumber', $phoneNumber);
        $xml->addChild('MNO', $mno);
        $xml->addChild('MNOcountry', $mnoCountry);

        return $this->makeRequest($xml->asXML());
    }

    /**
     * Get mobile payment options for transaction
     */
    public function getMobilePaymentOptions(string $transactionToken): array
    {
        $xml = new \SimpleXMLElement('<API3G/>');
        $xml->addChild('CompanyToken', $this->companyToken);
        $xml->addChild('Request', 'GetMobilePaymentOptions');
        $xml->addChild('TransactionToken', $transactionToken);

        return $this->makeRequest($xml->asXML());
    }

    /**
     * Get bank transfer options
     */
    public function getBankTransferOptions(string $transactionToken): array
    {
        $xml = new \SimpleXMLElement('<API3G/>');
        $xml->addChild('CompanyToken', $this->companyToken);
        $xml->addChild('Request', 'GetBankTransferOptions');
        $xml->addChild('TransactionToken', $transactionToken);

        return $this->makeRequest($xml->asXML());
    }

    /**
     * Charge token with credit card
     */
    public function chargeTokenCreditCard(string $transactionToken, array $cardData): array
    {
        $this->validateCreditCardData($cardData);

        $xml = new \SimpleXMLElement('<API3G/>');
        $xml->addChild('CompanyToken', $this->companyToken);
        $xml->addChild('Request', 'chargeTokenCreditCard');
        $xml->addChild('TransactionToken', $transactionToken);
        $xml->addChild('CreditCardNumber', $cardData['number']);
        $xml->addChild('CreditCardExpiry', $cardData['expiry']);
        $xml->addChild('CreditCardCVV', $cardData['cvv']);
        $xml->addChild('CardHolderName', $cardData['holder_name']);
        
        if (isset($cardData['charge_type'])) {
            $xml->addChild('ChargeType', $cardData['charge_type']);
        }

        return $this->makeRequest($xml->asXML());
    }

    /**
     * Create customer account
     */
    public function createAccount(array $customerData): array
    {
        $this->validateCreateAccountData($customerData);

        $xml = new \SimpleXMLElement('<API3G/>');
        $xml->addChild('CompanyToken', $this->companyToken);
        $xml->addChild('Request', 'createAccount');
        
        $requiredFields = [
            'CustomerEmail', 'CustomerFirstName', 'CustomerLastName', 
            'CustomerPhone', 'CustomerDialCode'
        ];
        
        foreach ($requiredFields as $field) {
            $xml->addChild($field, $customerData[$field]);
        }
        
        // Optional fields
        $optionalFields = [
            'CustomerPassword', 'CustomerAddress', 'CustomerCity', 
            'CustomerCountry', 'CustomerZip', 'CustomerNoCard'
        ];
        
        foreach ($optionalFields as $field) {
            if (isset($customerData[$field])) {
                $xml->addChild($field, $customerData[$field]);
            }
        }
        
        // Credit card fields (if CustomerNoCard is not set to 1)
        if (!isset($customerData['CustomerNoCard']) || $customerData['CustomerNoCard'] !== 1) {
            $cardFields = ['CustomerCreditCard', 'CustomerExpiry', 'CustomerCvv', 'CustomerHolderName'];
            foreach ($cardFields as $field) {
                if (isset($customerData[$field])) {
                    $xml->addChild($field, $customerData[$field]);
                }
            }
        }

        return $this->makeRequest($xml->asXML());
    }

    /**
     * Get company balance
     */
    public function getBalance(string $currency): array
    {
        if (empty($currency)) {
            throw new Exception('Currency is required');
        }

        $xml = new \SimpleXMLElement('<API3G/>');
        $xml->addChild('Request', 'getBalance');
        $xml->addChild('CompanyToken', $this->companyToken);
        $xml->addChild('Currency', $currency);

        return $this->makeRequest($xml->asXML());
    }

    /**
     * Get company services
     */
    public function getServices(): array
    {
        $xml = new \SimpleXMLElement('<API3G/>');
        $xml->addChild('Request', 'getServices');
        $xml->addChild('CompanyToken', $this->companyToken);

        return $this->makeRequest($xml->asXML());
    }

    /**
     * Charge recurring payment
     */
    public function chargeTokenRecurrent(string $transactionToken, string $subscriptionToken): array
    {
        $xml = new \SimpleXMLElement('<API3G/>');
        $xml->addChild('CompanyToken', $this->companyToken);
        $xml->addChild('Request', 'chargeTokenRecurrent');
        $xml->addChild('TransactionToken', $transactionToken);
        $xml->addChild('subscriptionToken', $subscriptionToken);

        return $this->makeRequest($xml->asXML());
    }

    /**
     * Build XML for createToken request
     */
    private function buildCreateTokenXml(array $transaction, array $services, array $allocations, array $additional, array $travelers): string
    {
        $xml = new \SimpleXMLElement('<API3G/>');
        $xml->addChild('CompanyToken', $this->companyToken);
        $xml->addChild('Request', 'createToken');
        
        // Transaction level
        $transactionNode = $xml->addChild('Transaction');
        foreach ($transaction as $key => $value) {
            $transactionNode->addChild($key, $value);
        }
        
        // Services level
        $servicesNode = $xml->addChild('Services');
        foreach ($services as $service) {
            $serviceNode = $servicesNode->addChild('Service');
            foreach ($service as $key => $value) {
                $serviceNode->addChild($key, $value);
            }
        }
        
        // Allocations level (optional)
        if (!empty($allocations)) {
            $allocationsNode = $xml->addChild('Allocations');
            foreach ($allocations as $allocation) {
                $allocationNode = $allocationsNode->addChild('Allocation');
                foreach ($allocation as $key => $value) {
                    $allocationNode->addChild($key, $value);
                }
            }
        }
        
        // Additional level (optional)
        if (!empty($additional)) {
            $additionalNode = $xml->addChild('Additional');
            foreach ($additional as $key => $value) {
                $additionalNode->addChild($key, $value);
            }
        }
        
        // Travelers level (optional)
        if (!empty($travelers)) {
            $travelersNode = $xml->addChild('Travelers');
            foreach ($travelers as $traveler) {
                $travelerNode = $travelersNode->addChild('Traveler');
                foreach ($traveler as $key => $value) {
                    $travelerNode->addChild($key, $value);
                }
            }
        }
        
        return $xml->asXML();
    }

    /**
     * Make HTTP request to DPO API
     */
    private function makeRequest(string $xmlData): array
    {
        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/xml',
                'Accept' => 'application/xml'
            ])->withBody($xmlData, 'application/xml')
              ->post($this->baseUrl);

            if (!$response->successful()) {
                throw new Exception("HTTP Error: " . $response->status() . " - " . $response->body());
            }

            return $this->parseXmlResponse($response->body());
            
        } catch (Exception $e) {
            Log::error('DPO API Request Failed', [
                'error' => $e->getMessage(),
                'xml_data' => $xmlData
            ]);
            
            throw new Exception('Payment service unavailable: ' . $e->getMessage());
        }
    }

    /**
     * Parse XML response from DPO
     */
    private function parseXmlResponse(string $xmlString): array
    {
        $xml = simplexml_load_string($xmlString);
        
        if ($xml === false) {
            throw new Exception('Invalid XML response from DPO');
        }
        
        return json_decode(json_encode($xml), true);
    }

    /**
     * Validate create token data
     */
    private function validateCreateTokenData(array $transaction, array $services): void
    {
        // Required transaction fields
        $requiredTransactionFields = ['PaymentAmount', 'PaymentCurrency', 'CompanyRef'];
        
        foreach ($requiredTransactionFields as $field) {
            if (!isset($transaction[$field]) || empty($transaction[$field])) {
                throw new Exception("Required transaction field missing: {$field}");
            }
        }
        
        // Validate payment amount
        if (!is_numeric($transaction['PaymentAmount']) || $transaction['PaymentAmount'] <= 0) {
            throw new Exception('PaymentAmount must be a positive number');
        }
        
        // Validate services
        if (empty($services)) {
            throw new Exception('At least one service is required');
        }
        
        foreach ($services as $service) {
            if (!isset($service['ServiceType']) || !isset($service['ServiceDescription'])) {
                throw new Exception('Service must contain ServiceType and ServiceDescription');
            }
        }
    }

    /**
     * Validate update token data
     */
    private function validateUpdateTokenData(array $data): void
    {
        if (isset($data['PaymentAmount']) && (!is_numeric($data['PaymentAmount']) || $data['PaymentAmount'] <= 0)) {
            throw new Exception('PaymentAmount must be a positive number');
        }
        
        if (isset($data['CustomerEmail']) && !filter_var($data['CustomerEmail'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email format');
        }
        
        if (isset($data['CustomerCountry']) && strlen($data['CustomerCountry']) !== 2) {
            throw new Exception('CustomerCountry must be a 2-letter ISO country code');
        }
    }

    /**
     * Validate mobile payment data
     */
    private function validateMobilePaymentData(string $phoneNumber, string $mno, string $mnoCountry): void
    {
        if (empty($phoneNumber)) {
            throw new Exception('Phone number is required');
        }
        
        if (empty($mno)) {
            throw new Exception('Mobile Network Operator (MNO) is required');
        }
        
        if (empty($mnoCountry)) {
            throw new Exception('MNO Country is required');
        }
        
        // Basic phone number validation
        if (!preg_match('/^\d{10,15}$/', $phoneNumber)) {
            throw new Exception('Invalid phone number format');
        }
    }

    /**
     * Validate credit card data
     */
    private function validateCreditCardData(array $cardData): void
    {
        $requiredFields = ['number', 'expiry', 'cvv', 'holder_name'];
        
        foreach ($requiredFields as $field) {
            if (!isset($cardData[$field]) || empty($cardData[$field])) {
                throw new Exception("Required card field missing: {$field}");
            }
        }
        
        // Validate card number (basic)
        if (!preg_match('/^\d{13,19}$/', $cardData['number'])) {
            throw new Exception('Invalid credit card number format');
        }
        
        // Validate expiry (MMYY format)
        if (!preg_match('/^\d{4}$/', $cardData['expiry'])) {
            throw new Exception('Card expiry must be in MMYY format');
        }
        
        // Validate CVV
        if (!preg_match('/^\d{3,4}$/', $cardData['cvv'])) {
            throw new Exception('CVV must be 3 or 4 digits');
        }
    }

    /**
     * Validate create account data
     */
    private function validateCreateAccountData(array $customerData): void
    {
        $requiredFields = ['CustomerEmail', 'CustomerFirstName', 'CustomerLastName', 'CustomerPhone', 'CustomerDialCode'];
        
        foreach ($requiredFields as $field) {
            if (!isset($customerData[$field]) || empty($customerData[$field])) {
                throw new Exception("Required customer field missing: {$field}");
            }
        }
        
        if (!filter_var($customerData['CustomerEmail'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email format');
        }
        
        if (isset($customerData['CustomerPassword']) && strlen($customerData['CustomerPassword']) < 6) {
            throw new Exception('Password must be at least 6 characters');
        }
    }

    /**
     * Helper method to check if transaction was successful
     */
    public function isTransactionSuccessful(array $response): bool
    {
        return isset($response['Result']) && in_array($response['Result'], ['000', '001', '002']);
    }

    /**
     * Get human-readable error message
     */
    public function getErrorMessage(array $response): string
    {
        $errorCodes = [
            '801' => 'Request missing company token',
            '802' => 'Company token does not exist',
            '803' => 'No request or error in Request type name',
            '804' => 'Error in XML',
            '900' => 'Transaction not paid yet',
            '901' => 'Transaction declined',
            '902' => 'Data mismatch in one of the fields',
            '903' => 'The transaction passed the Payment Time Limit',
            '904' => 'Transaction cancelled',
            '905' => 'Transaction amount exceeded limit',
            '906' => 'Monthly transaction limit exceeded',
            '922' => 'Provider does not exist',
            '923' => 'Allocated money exceeds payment amount',
            '940' => 'CompanyREF already exists and paid',
            '950' => 'Request missing mandatory fields',
            '999' => 'Custom error message'
        ];
        
        $code = $response['Result'] ?? 'Unknown';
        $explanation = $response['ResultExplanation'] ?? 'No explanation provided';
        
        return isset($errorCodes[$code]) 
            ? $errorCodes[$code] . ': ' . $explanation 
            : "Error {$code}: {$explanation}";
    }

    /**
     * Utility method to format transaction data for createToken
     */
    public static function formatTransactionData(
        float $amount, 
        string $currency, 
        string $companyRef, 
        string $redirectUrl = null, 
        string $backUrl = null,
        int $ptl = 5
    ): array {
        $transaction = [
            'PaymentAmount' => number_format($amount, 2, '.', ''),
            'PaymentCurrency' => strtoupper($currency),
            'CompanyRef' => $companyRef,
            'PTL' => $ptl
        ];
        
        if ($redirectUrl) {
            $transaction['RedirectURL'] = $redirectUrl;
        }
        
        if ($backUrl) {
            $transaction['BackURL'] = $backUrl;
        }
        
        return $transaction;
    }

    /**
     * Utility method to format service data
     */
    public static function formatServiceData(int $serviceType, string $description, string $date = null): array
    {
        $service = [
            'ServiceType' => $serviceType,
            'ServiceDescription' => $description
        ];
        
        if ($date) {
            $service['ServiceDate'] = $date;
        }
        
        return $service;
    }
}