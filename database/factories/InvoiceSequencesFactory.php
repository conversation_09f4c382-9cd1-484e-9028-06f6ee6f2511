<?php

namespace Database\Factories;

use App\Models\CompanyBranch;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\InvoiceSequences>
 */
class InvoiceSequencesFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            //
            'branch_id' => CompanyBranch::factory(),
            'last_number' => $this->faker->randomNumber(),
        ];
    }
}
