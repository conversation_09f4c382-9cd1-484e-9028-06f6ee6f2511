<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('Orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId("branch_id")->references('id')->on('CompanyBranch')->onDelete('restrict');
            $table->foreignId("user_id")->references('id')->on('users')->onDelete('restrict');
            $table->foreignId('confirmed_by')->nullable()->references('id')->on('users')->onDelete('restrict');
            $table->foreignId("company_party_id")->references('id')->on('CompanyParty')->onDelete('restrict');
            $table->unsignedBigInteger("invoiceNumber")->nullable();
            $table->unsignedBigInteger("originalInvoiceNumber")->nullable();
            $table->string("clientTin")->nullable();
            $table->string("clientName")->nullable();
            $table->string("clientPhoneNumber")->nullable();
            $table->string("salesTypeCode")->nullable();
            $table->string("receiptTypeCode")->nullable();
            $table->string("paymentTypeCode")->nullable();
            $table->string("salesStatusCode")->nullable();
            $table->bigInteger("confirmationDate")->nullable();

            $table->string("prescriptionNumber")->nullable();
            $table->string("insuranceTin")->nullable();
            $table->string("type");
            $table->bigInteger("salesDate")->nullable();
            $table->text("remarks")->nullable();
            $table->boolean("synced")->default(false);
            $table->boolean("isRefunded")->default(false);
            $table->boolean("demo")->default(false);
            $table->enum("status", ["Pending", "Confirmed", "Delivered", "Cancelled"])->default("Pending");
            $table->string("transactionType");

            $table->string("deliveredBy")->nullable();

            $table->decimal("taxblAmtA", 14)->nullable()->default(0);
            $table->decimal("taxblAmtB", 14)->nullable()->default(0);
            $table->decimal("taxblAmtC", 14)->nullable()->default(0);
            $table->decimal("taxblAmtD", 14)->nullable()->default(0);

            $table->decimal("taxAmtA", 14)->nullable()->default(0);
            $table->decimal("taxAmtB", 14)->nullable()->default(0);
            $table->decimal("taxAmtC", 14)->nullable()->default(0);
            $table->decimal("taxAmtD", 14)->nullable()->default(0);

            $table->decimal("totTaxblAmt", 14)->nullable()->default(0);
            $table->decimal("totTaxAmt", 14)->nullable()->default(0);
            $table->decimal("totAmt", 14)->nullable()->default(0);
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('Order');
    }
};
