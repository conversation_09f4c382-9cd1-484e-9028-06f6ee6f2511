<?php

use App\Exceptions\CompanyNotFound;
use App\Models\Company;
use App\Models\InvoiceSequences;
use App\Exceptions\NotFound;
use App\Exceptions\Forbidden;
use App\Models\CompanyBranch;
use Illuminate\Support\Facades\Auth;
use App\Services\RoleService;
use App\Enums\AccountTypeEnums;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Enums\SubscriptionTypesEnums;

function isApi(): bool
{

    if (config('app.env') === 'testing') {
        return true;
    }

    return request()->is('api/*');
}

function companyData(): Company
{

    if (isApi()) {

        $companyId = request()->header('companyId');

        throw_if(is_null($companyId), Forbidden::class, __("Company not specified"));

        $roleService = resolve(RoleService::class);

        $userRole = $roleService->userRoleByCompanyId(Auth::id(), $companyId);

        throw_if(is_null($userRole), NotFound::class, 'Company not found');

        $company =  Company::where('id', $userRole->company_id)
            ->with('branches')
            ->first();

        throw_if(!$company->isEBM, Forbidden::class, 'Invalid EBM Company');

        // if app is in production 
        if (config('app.env') === 'production') {
            $subscription = $company->user->subscription;

            if (!$subscription || !$subscription->isActive) {
                throw new Forbidden('No active subscription found. Please subscribe to continue.');
            }

            if ($subscription->subscriptionType !== SubscriptionTypesEnums::ENTERPRISE->value) {
                throw new Forbidden('Invalid subscription plan configuration.');
            }
        }

        return $company;
    }

    $user = Auth::user();

    $roleService = resolve(RoleService::class);

    $userRole = $roleService->userDefaultRole($user->id);

    throw_if(is_null($userRole), CompanyNotFound::class, 'You are not assigned to any company');

    $company =   Company::where('id', $userRole->company_id)
        ->with('branches')
        ->first();

    throw_if(is_null($company), NotFound::class, 'Company not found');

    return $company;
}

function branchData(): CompanyBranch
{

    if (isApi()) {

        $branchId = request()->header('branchId');

        throw_if(is_null($branchId), Forbidden::class, __("Branch not specified"));

        $branch = CompanyBranch::where('company_id', companyData()->id)
            ->where('id', $branchId)
            ->first();

        throw_if(is_null($branch), NotFound::class, 'Branch not found');

        throw_if(!$branch->isEBM, Forbidden::class, "Invalid EBM Company");

        return $branch;
    }

    $user = Auth::user();

    $roleService = resolve(RoleService::class);

    $userRole = $roleService->userDefaultRole($user->id);

    throw_if(is_null($userRole), CompanyNotFound::class, 'You are not assigned to any company');

    $branch =  CompanyBranch::where('company_id', $userRole->company_id)
        ->where('id', $userRole->default_branch_id)
        ->first();

    throw_if(is_null($branch), NotFound::class, 'Branch not found');

    return $branch;
}

function nextInvoiceNo(int $branchId): int
{

    $invoiceSequence = InvoiceSequences::where('branch_id', $branchId)->first();

    if (!$invoiceSequence) {
        InvoiceSequences::create([
            'branch_id' => $branchId,
            'last_number' => 1
        ]);
        return 1;
    }

    return $invoiceSequence->last_number + 1;
}

function saveNextInvoiceNo(int $branchId): void
{
    $invoiceSequencex = InvoiceSequences::where('branch_id', $branchId)->first();
    $invoiceSequencex->last_number = $invoiceSequencex->last_number + 1;
    $invoiceSequencex->save();
}

function trimNumber($number): float
{
    return round($number, 2);
}

function zataAccess(array $permissions)
{
    $user = Auth::user()?->userDefaultPermission?->permission?->name;

    throw_if(!$user, Forbidden::class, 'Unauthorized');

    foreach ($permissions as $permission) {
        if ($permission === AccountTypeEnums::ADMIN->value) {
            throw_if($user !== AccountTypeEnums::ADMIN->value, Forbidden::class, 'Unauthorized');
            return;
        } elseif ($permission === AccountTypeEnums::WAREHOUSE_MANAGER->value) {
            if (in_array($user, [AccountTypeEnums::ADMIN->value, AccountTypeEnums::WAREHOUSE_MANAGER->value])) {
                return;
            }
        } elseif ($permission === AccountTypeEnums::SALES_MANAGER->value) {
            return;
        }
    }

    throw new Forbidden('Unauthorized');
}
