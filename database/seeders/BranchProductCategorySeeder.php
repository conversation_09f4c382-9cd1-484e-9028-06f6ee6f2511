<?php

namespace Database\Seeders;

use App\Models\BranchProductCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use App\Enums\PharmacyProductCategoriesEnums;
use App\Enums\ElectronicsProductCategoriesEnums;
use App\Enums\SupermarketProductCategoriesEnums;
use App\Enums\GeneralProductCategoriesEnums;
use App\Enums\RestaurantProductCategoriesEnums;

class BranchProductCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        
        $general = GeneralProductCategoriesEnums::cases();
        $pharmacy = PharmacyProductCategoriesEnums::cases();
        $electronics = ElectronicsProductCategoriesEnums::cases();
        $supermarket = SupermarketProductCategoriesEnums::cases();
        $restaurant = RestaurantProductCategoriesEnums::cases();

        foreach ($general as $value) {
            $slug = str_replace(' ', '-', $value->value);
            BranchProductCategory::create([
                'name' => $value->value,
                'slug' => $slug,
                'branch_category_id' => 1,
                'isCommon' => 1,
            ]);
        }

        foreach ($pharmacy as $value) {
            $slug = str_replace(' ', '-', $value->value);
            BranchProductCategory::create([
                'name' => $value->value,
                'slug' => $slug,
                'branch_category_id' => 2,
                'isCommon' => 1,
            ]);
        }
        
        foreach ($supermarket as $value) {
            $slug = str_replace(' ', '-', $value->value);
            BranchProductCategory::create([
                'name' => $value->value,
                'slug' => $slug,
                'branch_category_id' => 3,
                'isCommon' => 1,
            ]);
        }

        foreach ($restaurant as $value) {
            $slug = str_replace(' ', '-', $value->value);
            BranchProductCategory::create([
                'name' => $value->value,
                'slug' => $slug,
                'branch_category_id' => 4,
                'isCommon' => 1,
            ]);
        }

        foreach ($electronics as $value) {
            $slug = str_replace(' ', '-', $value->value);
            BranchProductCategory::create([
                'name' => $value->value,
                'slug' => $slug,
                'branch_category_id' => 5,
                'isCommon' => 1,
            ]);
        }
    }
}
