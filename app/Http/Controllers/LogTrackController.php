<?php

namespace App\Http\Controllers;

use App\Services\LogTrackService;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;

class LogTrackController extends Controller
{

    protected $user;
    public function __construct(protected LogTrackService $service) {
        $this->user = Auth::user();
    }

    public function index()
    {

        $company = companyData();
        $branch = branchData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $fromDate = \request()->query('fromDate', null);
        $toDate = \request()->query('toDate', null);

        $logs = $this->service->getLogs($company->id,$branch->id,$this->user->id,$page,$perPage, $fromDate,$toDate);
        $response =  $this->pagination($logs);
        return Inertia::render('LogTrack', [
            'Logs' => $response['data'],
            'currentPage' => $response['currentPage'],
            'lastPage' => $response['lastPage'],
            'itemsPerPage' => $response['itemsPerPage'],
            'pageItems' => $response['pageItems'],
            'total' => $response['total'],
        ]);
    }
}
