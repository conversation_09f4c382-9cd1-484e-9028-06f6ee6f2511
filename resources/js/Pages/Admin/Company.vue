<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";
import { Button } from '@/components/ui/button';

const props = defineProps({
    Company: Object,
});


</script>

<template>
    <AppLayout title="Company Branches">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Company Details
            </h2>
        </template>

        <div class="py-12">
            <div class="">

                <div class="mt-8 mb-3 flex justify-end space-x-4">
                  
                    <Button as-child>
                        <Link :href="route('admin.company.edit', Company.id)">
                         Edit company details
                        </Link>
                    </Button>
                </div>

                <div class="bg-white  overflow-hidden sm:rounded-lg border">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Company Information</h3>
                    </div>
                    <div class="">
                        <dl>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Name</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ Company.name }}</dd>
                            </div>
                            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">TIN</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ Company.tin }}</dd>
                            </div>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Phone</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ Company.phone }}</dd>
                            </div>
                            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Email</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ Company.email }}</dd>
                            </div>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Address</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ Company.address }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <div class="mt-12">
                    <h2 class="text-lg font-semibold mb-2">
                        Branches
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                        v-if="Company.branches.length > 0">
                        <div v-for="branch in Company.branches" :key="branch.id"
                            class="bg-white  rounded border p-6">
                            <h3 class="text-lg font-semibold mb-2">{{ branch.name }}</h3>

                            <p class="text-gray-500 mb-2">{{ branch.email ?? 'N/A' }}</p>
                            <p class="text-gray-500 mb-2">{{ branch.address }}</p>
                            <div class="flex justify-between items-center">

                                <Link :href="route('admin.branch.edit', [Company.id, branch.id])"
                                    class="text-green-600 hover:text-green-900 underline">Edit</Link>
                            </div>
                        </div>
                    </div>

                    <div v-else class="flex justify-center items-center h-64">
                        <p>No branches found.</p>
                    </div>
                </div>
            </div>
        </div>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>