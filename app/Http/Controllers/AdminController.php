<?php

namespace App\Http\Controllers;
use App\Enums\TransactionTypeEnums;
use App\Data\AdminUpdateCompanyBranchData;
use App\Data\AdminUpdateCompanyData;
use App\Data\UpdateAccountData;
use App\Http\Requests\AdminUpdateCompanyBranchRequest;
use App\Http\Requests\AdminUpdateCompanyRequest;
use App\Services\Admin\CompanyService;
use App\Services\EbmService;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\UpdateAccountRequests;
use App\Models\Company;
use App\Services\Admin\AccountService;
use App\Services\AdminService;

class AdminController extends Controller
{

    public function __construct(
        protected AdminService $service,
        protected CompanyService $companyService,
        protected EbmService $ebmService,
        protected AccountService $accountService
    ) {}

    public function index()
    {
        if (!$this->isAdmin()) {
            return Inertia::render('Error/404');
        }

        $company = $this->companyService->company();
        $analytics = $this->service->getAnalytics();
        return Inertia::render('Admin/Index', [
            'Companies' => $company,
            'Analytics' => $analytics
        ]);
    }

    public function search()
    {
        if (!$this->isAdmin()) {
            return Inertia::render('Error/404');
        }

        return Inertia::render('Admin/Search', [
            'companies' => Company::select('id', 'name', 'tin', 'created_at')->get(),
            'invoiceTypes' => [
            TransactionTypeEnums::SALES->value,
            TransactionTypeEnums::SALES_RETURN->value,
            TransactionTypeEnums::PURCHASE->value,
            TransactionTypeEnums::PROFORMA->value,
            TransactionTypeEnums::DELIVERY_NOTE->value
        ]
        ]
        );
    }

    public function searchJson()
    {
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $fromDate = \request()->query('fromDate', null);
        $toDate = \request()->query('toDate', null);
        $companyId = \request()->query('companyId', null);
        $invoiceType = \request()->query('invoiceType', TransactionTypeEnums::SALES->value);
        $searchQuery = \request()->query('searchQuery', null);

        if (!$this->isAdmin()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

       return  $this->pagination($this->service->searchInvoice($perPage, $page, $companyId, $invoiceType, $fromDate, $toDate, $searchQuery));
    }
    public function company(int $companyId)
    {
        if (!$this->isAdmin()) {
            return Inertia::render('Error/404');
        }

        $company = $this->companyService->companyById($companyId);
        return Inertia::render('Admin/Company', [
            'Company' => $company,
        ]);
    }

    public function editCompany(int $companyid)
    {
        if (!$this->isAdmin()) {
            return Inertia::render('Error/404');
        }

        $company = $this->companyService->companyById($companyid);
        return Inertia::render('Admin/EditCompany', [
            'Company' => $company,
        ]);
    }

    public function updateCompany(AdminUpdateCompanyRequest $request, int $companyid)
    {
        if (!$this->isAdmin()) {
            return Inertia::render('Error/404');
        }

        $data = AdminUpdateCompanyData::from($request->validated());
        $this->companyService->updateCompany($data, $companyid);
        return redirect()->route('admin.company', ['companyId' => $companyid])->banner('Company Updated Successfully');
    }

    public function editBranch(int $companyId, int $branchId)
    {
        if (!$this->isAdmin()) {
            return Inertia::render('Error/404');
        }

        $branch = $this->companyService->branchById($companyId, $branchId);
        return Inertia::render('Admin/EditBranch', [
            'Branch' => $branch,
            'CompanyId' => $companyId,
        ]);
    }

    public function updateBranch(AdminUpdateCompanyBranchRequest $request, int $companyId, int $branchId)
    {
        if (!$this->isAdmin()) {
            return Inertia::render('Error/404');
        }

        $data = AdminUpdateCompanyBranchData::from($request->validated());
        $this->companyService->updateBranch($data, $companyId, $branchId);
        //  return redirect()->route('admin.company', ['companyId' => $companyId])->banner('Branch Updated Successfully');
        // redirect back 
        return redirect()->back()->banner('Branch Updated Successfully');
    }

    public function initializeBranch(int $branchId)
    {
        if (!$this->isAdmin()) {
            return Inertia::render('Error/404');
        }

        $this->ebmService->initializeBranch($branchId);

        return redirect()->back()->banner('Branch initialized');
    }

    public function autoInitializeBranch(int $branchId)
    {
        if (!$this->isAdmin()) {
            return Inertia::render('Error/404');
        }

        $this->ebmService->autoInitializeBranch($branchId);

        return redirect()->back()->banner('Branch initialized');
    }

    private function isAdmin(): bool
    {
        return Auth::user()->whiteLabel === 'admin';
    }

    // User Account Management
    public function accountIndex()
    {

        if (!$this->isAdmin()) {
            return Inertia::render('Error/404');
        }

        $accounts = $this->accountService->getAccounts();
        return Inertia::render('Admin/Account', [
            'Accounts' => $accounts
        ]);
    }

    public  function accountShow(int $accountId)
    {

        if (!$this->isAdmin()) {
            return Inertia::render('Error/404');
        }

        $account = $this->accountService->getAccountById($accountId);
        return Inertia::render('Admin/AccountShow', [
            'Account' => $account,
        ]);
    }

    public function accountUpdate(UpdateAccountRequests $request, int $accountId)
    {

        if (!$this->isAdmin()) {
            return Inertia::render('Error/404');
        }

        $data = UpdateAccountData::from($request->validated());
        $this->accountService->updateAccount($data, $accountId);
        return redirect()->route('account.index')->banner('Account Updated Successfully');
    }
}
