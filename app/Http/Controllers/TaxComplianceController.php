<?php

namespace App\Http\Controllers;

use App\Http\Requests\TaxComplianceRequest;
use Inertia\Inertia;
use Illuminate\Http\Request;

class TaxComplianceController extends Controller
{

    public function index()
    {
        return Inertia::render('tax/Index', [
            'result' => null,
        ]);
    }

    public function calculate(TaxComplianceRequest $request)
    {
        $data = $request->validated();
        $userType = $data['userType'];
        $taxType = $data['taxType'];

        $totalSales = 0;
        $totalExpenses = 0;
        $totalLosses = 0;
        $complianceReport = [];
        $breakdown = [];

        if ($userType === 'company') {
            $totalSales = array_sum(array_column($data['salesData'], 'amount'));
            $totalExpenses = $data['companyExpenses'] ?? 0;
            $totalLosses = $data['companyLosses'] ?? 0;
        } else {
            $totalSales = $data['totalSales'] ?? 0;
            $totalExpenses = $data['expenses'] ?? 0;
            $totalLosses = $data['losses'] ?? 0;
        }

        $this->runComplianceChecks($data, $totalSales, $totalLosses, $complianceReport);


        $taxableAmount = 0;
        $taxAmount = 0;

        if ($taxType === 'VAT') {
            $taxableSupplies = $data['taxableSupplies'] ?? 0;
            $inputVat = $data['inputVat'] ?? 0;

            if ($taxableSupplies < $totalSales) {
                $complianceReport[] = ['level' => 'warning', 'message' => 'Taxable supplies are less than total sales. This may indicate underreporting of taxable transactions, a key focus area for RRA.'];
            }


            if ($inputVat > ($taxableSupplies * 0.18)) {
                $complianceReport[] = ['level' => 'critical', 'message' => 'Input VAT seems high compared to taxable supplies. Ensure all input VAT is claimed in the appropriate period and supported by valid EBM invoices to avoid penalties.'];
            }

            $outputVat = $taxableSupplies * 0.18;
            $taxAmount = $outputVat - $inputVat;

            $breakdown = [
                ['label' => 'Output VAT (18%)', 'value' => $outputVat],
                ['label' => 'Input VAT (Deducted)', 'value' => -$inputVat],
            ];

            if ($taxAmount < 0) {
                $complianceReport[] = ['level' => 'info', 'message' => 'You have excess input VAT. This credit can be carried forward to the next tax period as per RRA guidelines.'];
                $taxAmount = 0;
            }
        } else {
            $taxableIncome = $totalSales - $totalExpenses - $totalLosses;
            $taxableAmount = max(0, $taxableIncome);
            $taxAmount = $taxableAmount * 0.30;

            $breakdown = [
                ['label' => 'Total Sales', 'value' => $totalSales],
                ['label' => 'Total Expenses', 'value' => -$totalExpenses],
                ['label' => 'Reported Losses', 'value' => -$totalLosses],
            ];

            if ($taxableIncome < 0) {
                $complianceReport[] = ['level' => 'info', 'message' => 'A net loss was calculated. Ensure this loss is correctly documented and carried forward according to RRA regulations.'];
            }

            if ($userType === 'company' && !empty($data['employees'])) {
                $payeBreakdown = [];
                $totalPaye = 0;
                foreach ($data['employees'] as $employee) {
                    $salary = $employee['salary'] ?? 0;
                    $employeePaye = $salary > 60000 ? ($salary - 60000) * 0.15 : 0;
                    $totalPaye += $employeePaye;
                    $payeBreakdown[] = ['label' => $employee['name'], 'value' => $employeePaye];
                }
                $taxAmount += $totalPaye;
                $breakdown[] = ['label' => 'Total PAYE', 'value' => $totalPaye];
                $complianceReport[] = ['level' => 'info', 'message' => "Calculated PAYE is an estimate. Ensure benefits in kind are included and correct tax brackets are used. "];
            }
        }

        $result = [
            'summary' => [
                'taxType' => $taxType,
                'taxAmount' => $taxAmount,
                'taxableAmount' => $taxableAmount,
            ],
            'breakdown' => $breakdown,
            'complianceReport' => $complianceReport,
            'chartsData' => [
                'labels' => ['Jan', 'Feb', 'Mar'],
                'datasets' => [[
                    'label' => 'Estimated Tax Over Time',
                    'data' => [rand(90, 120) / 100 * $taxAmount, rand(90, 110) / 100 * $taxAmount, $taxAmount],
                    'backgroundColor' => '#34d399',
                ]],
            ]
        ];

        return Inertia::render('tax/Index', [
            'result' => $result,
        ]);
    }


    private function runComplianceChecks(array $data, float $totalSales, float $totalLosses, array &$complianceReport): void
    {
        if ($totalSales > 0 && ($totalLosses / $totalSales) > 0.5) {
            $complianceReport[] = ['level' => 'warning', 'message' => 'The ratio of losses to sales is high. The RRA flags continual losses as a risk factor requiring scrutiny. '];
        }

        if ($data['taxType'] === 'VAT' || $totalSales > 0) {
            $complianceReport[] = ['level' => 'info', 'message' => 'Reminder: Inappropriate use of EBM (e.g., non-issuance or understatement) is a major compliance risk. Ensure all sales are captured accurately. '];
        }

        if (($data['expenses'] ?? $data['companyExpenses'] ?? 0) > 0) {
            $complianceReport[] = ['level' => 'info', 'message' => "Ensure expenses don't include personal items, penalties, or items without supporting documents, as these are non-deductible. "];
        }
    }
}
