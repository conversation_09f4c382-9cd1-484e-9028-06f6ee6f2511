<?php

namespace App\Http\Controllers;

use App\Services\DashboardService;
use Inertia\Inertia;

class HomeController extends Controller
{
    public function __construct(protected DashboardService $service) {}
    public function index()
    {
        return Inertia::render('Welcome', []);
    }

    public function dashbaord()
    {

        branchData();
        return Inertia::render('Dashboard');
    }

    public function dashboardJson()
    {
        $branch = branchData();
        return $this->service->dashboardData($branch->id);
    }
}
