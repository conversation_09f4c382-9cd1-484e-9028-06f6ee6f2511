<?php

namespace Database\Seeders;

use App\Models\BranchCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BranchCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        
        $data = [
            [
                'name' => 'General',
                'description' => 'General',
                'isActive' => true
            ],
            [
                'name' => 'Pharmacy',
                'description' => 'Pharmaceuticals and Medical Supplies',
                'isActive' => true
            ],
            [
                'name' => 'Supermarket and Grocery',
                'description' => 'Supermarket and Grocery Items',
                'isActive' => true
            ],
            [
                'name' => 'Restaurant and Bar',
                'description' => 'Restaurant and Bar Items',
                'isActive' => true
            ],
            [
                'name' => 'Electronics and Appliances',
                'description' => 'Electronics and Appliances Items',
                'isActive' => true
            ],
        ];

        BranchCategory::insert($data);
    }
}
