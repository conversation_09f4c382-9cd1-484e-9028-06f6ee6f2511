<script setup>
import { ref, watchEffect } from 'vue';
import { usePage } from '@inertiajs/vue3';
import { onUnmounted } from 'vue';

const page = usePage();
const show = ref(false);
const style = ref('success');
const message = ref('');

const timeoutId = ref(null);

watchEffect(() => {
  const flash = page.props.jetstream.flash;
  if (flash?.banner) {
    style.value = flash.bannerStyle || 'success';
    message.value = flash.banner;
    show.value = true;
    
    if (timeoutId.value) clearTimeout(timeoutId.value);
    timeoutId.value = setTimeout(() => {
      show.value = false;
    }, 5000);
  }
});

onUnmounted(() => {
  if (timeoutId.value) clearTimeout(timeoutId.value);
});
</script>

<template>
  <div>
    <Transition 
      enter-active-class="transition ease-out duration-300"
      enter-from-class="transform -translate-y-full"
      enter-to-class="transform translate-y-0"
      leave-active-class="transition ease-in duration-200"
      leave-from-class="transform translate-y-0"
      leave-to-class="transform -translate-y-full"
    >
      <div 
        v-if="show && message" 
        class="fixed top-8 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md"
      >
        <div 
          role="alert"
          class="relative py-4 px-6 rounded-lg shadow-lg bg-white border-2"
          :class="{
            'border-green-500 bg-green-50': style === 'success',
            'border-red-500 bg-red-50': style === 'danger'
          }"
        >
          <div class="flex items-center space-x-4">
            <div class="flex-shrink-0">
              <svg v-if="style === 'success'" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <svg v-if="style === 'danger'" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p class="flex-1 text-base font-medium text-gray-900 leading-relaxed">
              {{ message }}
            </p>
            <button
              @click="show = false"
              class="flex-shrink-0 p-2 rounded-full hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2"
              :class="{
                'focus:ring-green-500': style === 'success',
                'focus:ring-red-500': style === 'danger'
              }"
              aria-label="Close notification"
            >
              <svg class="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>