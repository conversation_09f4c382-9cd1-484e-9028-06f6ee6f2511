<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";

const props = defineProps({
    Invitation: Object,
});
</script>

<template>
    <AppLayout title="Accept Invitation">
        <template #header>
            <h2 class="font-medium text-2xl text-zata-secondary-dark leading-tight tracking-tight">
                Accept Invitation
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-2xl mx-auto px-6 py-8 ">
                <h3 class="text-xl font-semibold text-zata-secondary-dark mb-6">
                    Invitation Details
                </h3>
                <div v-if="Invitation">
                    <div class="p-6 bg-zata-background-dark border border-zata-primary-lighter rounded-md text-center">
                        <p class="text-lg font-medium text-zata-secondary-dark">
                            You have been invited to
                            <span class="text-zata-primary-dark font-semibold">{{ Invitation.company }}</span>.
                        </p>
                        <div class="mt-4 space-y-2 text-zata-secondary-dark">
                            <div class="flex justify-center">
                                <p>
                                    <span class="font-bold w-24">As </span>
                                    <span> {{ Invitation.permission }} </span>
                                </p>

                            </div>
                            <div class="flex  justify-center">
                                <p> <span class="font-bold w-24">in Branch </span>
                                    <span>{{ Invitation.branch }}</span>
                                </p>

                            </div>
                            <div class="flex justify-center">
                                <p>
                                    <span class="font-bold w-24">Expires:</span>
                                    <span>{{ Invitation.expireAt }}</span>
                                </p>

                            </div>
                        </div>
                        <div class="mt-6 flex justify-center">
                            <Link :href="route('user.confirmInvitation', Invitation.id)"
                                class="inline-block px-6 py-2.5 text-sm font-medium text-white bg-zata-primary-dark hover:bg-zata-primary-light rounded-md shadow-sm transition-colors duration-200">
                            Accept Invitation
                            </Link>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <p class="text-zata-secondary-dark opacity-70 italic">No invitation available at this time.</p>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style scoped></style>