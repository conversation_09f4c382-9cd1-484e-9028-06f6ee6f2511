<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Laravel\Scout\Searchable;

class Product extends Model
{
    use HasFactory, Searchable;

    protected $dateFormat = 'U';

    protected $table = 'Product';

    protected $fillable = [
        'branch_id',
        'category_id',
        'quantity_unit_id',
        'packaging_unit_id',
        'type_id',
        'tax_id',
        'class_id',
        'country_id',
        'branch_product_category_id',
        'itemCode',
        'name',
        'slug',
        'discountRate',
        'soldInSubUnit',
        'conversionFactor',
        'image',
        'description',
        'description_two',
        'description_three',
    ];

    public function toSearchableArray()
    {
        return [
            'name' => $this->name,
            'itemCode' => $this->itemCode,
        ];
    }

    public function productDetails(): HasMany
    {
        return $this->hasMany(ProductDetail::class, 'product_id', 'id');
    }

    public function productPackingUnit(): HasOne
    {
        return $this->hasOne(ProductPackingUnit::class, 'id','packaging_unit_id');
    }

    public function productQuantityUnit(): HasOne
    {
        return $this->hasOne(ProductQuantityUnit::class, 'id', 'quantity_unit_id');
    }

    public function productCategory(): HasOne
    {
        return $this->hasOne(ProductCategory::class, 'id', 'category_id');
    }

    public function productType(): HasOne
    {
        return $this->hasOne(ProductType::class, 'id', 'type_id');
    }

    public function productClass(): BelongsTo
    {
        return $this->belongsTo(ProductClass::class, 'class_id');
    }

    public function productBranch(): BelongsTo
    {
        return $this->belongsTo(CompanyBranch::class, 'branch_id');
    }

    public function productCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    public function productTax(): BelongsTo
    {
        return $this->belongsTo(Tax::class, 'tax_id');
    }

    public function  productBranchCetegory(): BelongsTo
    {
        return $this->belongsTo(BranchProductCategory::class, 'branch_product_category_id');
    }

    public function scopeWhereProductBranchCategoryId($query, int|null $productBranchCategoryID)
    {
        if (!is_null($productBranchCategoryID)) {
            return $query->where('branch_product_category_id', $productBranchCategoryID);
        }
        return $query;
    }

    public function scopeWhereCompanyId($query, int $companyId) 
    {
        return $query->where(function ($query) use ($companyId) {
            $query->where('company_id', $companyId)
                ->orWhereNull('company_id');
        });
    }

    public function scopeWhereInStock($query)
    {
        return $query->whereHas('productDetails', function ($q) {
            $q->where('currentStock', '>', 0);
        });
    }
    
}
