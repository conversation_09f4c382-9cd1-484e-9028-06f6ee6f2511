<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('CompanyInsurance', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->references('id')->on('Company')->onDelete('restrict');
            $table->foreignId('insurance_id')->references('id')->on('Insurance')->onDelete('restrict');
            $table->string('code')->nullable();
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
            $table->unique(['company_id', 'insurance_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('CompanyInsurance');
    }
};
