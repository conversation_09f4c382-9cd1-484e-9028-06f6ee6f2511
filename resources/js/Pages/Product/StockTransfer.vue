<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from "@inertiajs/vue3";
import FormSection from '@/Components/FormSection.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import SelectOption from '@/Components/SelectOption.vue';
import RadioOptionGroup from '@/Components/RadioOptionGroup.vue';
import { computed } from 'vue';

const props = defineProps({
    Product: Object,
    Branches: Array
});

const form = useForm({
    quantity: null,
    branchID: null,
    batchNumber: null,
    wholeStock: 0,
    note: 'Stock Transfer',
    movingUnit: 'sub',
    soldInSubUnit: props.Product.soldInSubUnit
});

function createNewParty() {
    form.put(route('product.stockTransferStore', props.Product.id), {
    });
}

const batchOptions = computed(() =>
    props.Product.productDetails.map(detail => ({
        value: detail.batchNumber,
        label: `Batch : ${detail.batchNumber} | Expire: ${detail.expireDate} | Quantity: ${detail.currentStock}`,
    }))
);

const branchOption = props.Branches.map(branch => ({
    value: branch.id,
    label: branch.name
}));

const options = [
    { value: 0, label: 'No' },
    { value: 1, label: 'Yes' },
];

const optionsUnit = computed(() => [
    { value: 'sub', label: `Sub Units / ${props.Product.quantityUnit}` },
    { value: 'main', label: `Main Units / ${props.Product.packagingUnit}` },
]);
</script>

<template>
    <AppLayout title="Stock Transfer">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Stock Transfer
            </h2>
        </template>

        <FormSection>
            <template #title>
                Stock Transfer - <span class="font-bold">{{ Product.name }}</span>
            </template>
            <template #form>
                <form class="" @submit.prevent="createNewParty">
                    <div class="py-4">
                        <SelectOption id="batchno" v-model="form.batchNumber" :options="batchOptions"
                            label="Batch NO" placeholder="" valueKey="value" labelKey="label" />
                        <InputError class="mt-2" :message="form.errors.batchNumber" />
                    </div>

                    <div class="py-4">
                        <p v-if="Branches.length == 0" class="text-red-500 text-center">Add transfer branch</p>
                        <SelectOption id="branchID" v-model="form.branchID" :options="branchOption" label="Branch"
                            placeholder="" valueKey="value" labelKey="label" />
                        <InputError class="mt-2" :message="form.errors.branchID" />
                    </div>

                    <div v-if="form.soldInSubUnit" class="pb-4">
                        <RadioOptionGroup v-model="form.movingUnit" :options="optionsUnit" name="movingUnit"
                            label="Load unit" />
                    </div>

                    <div class="py-1">
                        <RadioOptionGroup v-model="form.wholeStock" :options="options" name="wholeStock"
                            label="Transfer whole stock" />
                        <InputError class="mt-2" :message="form.errors.wholeStock" />
                    </div>
                    <div v-if="form.wholeStock == 0" class="py-4">
                        <InputLabel for="quantity" value="Quantity" />
                        <TextInput id="quantity" v-model="form.quantity" type="number" />
                        <InputError class="mt-2" :message="form.errors.quantity" />
                    </div>
                    <div class="py-4">
                        <InputLabel for="note" value="Reason" />
                        <TextInput id="note" v-model="form.note" type="text" />
                        <InputError class="mt-2" :message="form.errors.note" />
                    </div>

                    <div class="flex justify-end mt-4">
                        <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                            Comfirm
                        </PrimaryButton>
                    </div>
                </form>
            </template>
        </FormSection>
        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>
