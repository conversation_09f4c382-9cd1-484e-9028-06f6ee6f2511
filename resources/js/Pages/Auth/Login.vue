<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticationCard from '@/Components/AuthenticationCard.vue';
import AuthenticationCardLogo from '@/Components/AuthenticationCardLogo.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import { Button } from '@/components/ui/button';
import Banner from '@/Components/Banner.vue';

defineProps({
    canResetPassword: Boolean,
    status: String,
});

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

const submit = () => {
    form.transform(data => ({
        ...data,
        remember: form.remember ? 'on' : '',
    })).post(route('login'), {
        onFinish: () => form.reset('password'),
    });
};
</script>

<template>

    <Head title="Log in" />
    <Banner />
    <AuthenticationCard>
        <template #logo>
            <AuthenticationCardLogo />
        </template>

        <div v-if="status" class="mb-4 font-medium text-sm text-green-600">
            {{ status }}
        </div>

        <template #formtitle>

        </template>


        <form @submit.prevent="submit">
            <div>
                <InputLabel for="email" value="Your email address" />
                <TextInput id="email" v-model="form.email" type="email" class="mt-1 block w-full" required autofocus
                    autocomplete="username" />
                <InputError class="mt-2" :message="form.errors.email" />
            </div>

            <div class="mt-7">
                <InputLabel for="password" value="Your password" />
                <TextInput id="password" v-model="form.password" type="password" class="mt-1 block w-full" required
                    autocomplete="current-password" />
                <InputError class="mt-2" :message="form.errors.password" />
            </div>
            <div class="mt-7 flex justify-center ">
                <Button class="w-full" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    Log in
                </Button>
            </div>
            <div class="mt-6 lg:flex justify-between">

                <div>
                    <Link v-if="canResetPassword" :href="route('password.request')"
                        class="underline font-medium text-sm text-zata-primary-dark hover:text-zata-primary-light">
                    Forgot your password?
                    </Link>
                </div>

                <div>
                    New to Zata?
                    <Link :href="route('register')"
                        class="underline font-medium text-zata-primary-dark hover:text-zata-primary-light">
                    Sign up
                    </Link>
                </div>
            </div>

            <div class="mx-auto mt-12">
                <Link :href="route('google-auth')"
                    class="flex justify-center  items-center p-3  border rounded-md bg-gray-50 hover:bg-zata-background">
                <div class="flex items-center justify-center ">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" viewBox="0 0 48 48">
                        <defs>
                            <path id="a"
                                d="M44.5 20H24v8.5h11.8C34.7 33.9 30.1 37 24 37c-7.2 0-13-5.8-13-13s5.8-13 13-13c3.1 0 5.9 1.1 8.1 2.9l6.4-6.4C34.6 4.1 29.6 2 24 2 11.8 2 2 11.8 2 24s9.8 22 22 22c11 0 21-8 21-22 0-1.3-.2-2.7-.5-4z">
                            </path>
                        </defs>
                        <clipPath id="b">
                            <use xlink:href="#a" overflow="visible"></use>
                        </clipPath>
                        <path clip-path="url(#b)" fill="#FBBC05" d="M0 37V11l17 13z"></path>
                        <path clip-path="url(#b)" fill="#EA4335" d="M0 11l17 13 7-6.1L48 14V0H0z"></path>
                        <path clip-path="url(#b)" fill="#34A853" d="M0 37l30-23 7.9 1L48 0v48H0z"></path>
                        <path clip-path="url(#b)" fill="#4285F4" d="M48 48L17 24l-4-3 35-10z"></path>
                    </svg>
                    <span class=" ml-3 font-bold">Login with Google</span>
                </div>
                </Link>
            </div>
        </form>
    </AuthenticationCard>
</template>
