<?php

use App\Models\BranchCategory;
use App\Models\Company;
use App\Models\CompanyBranch;
use App\Models\PaymentMode;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Models\Role;
use App\Models\RolePermission;
use App\Models\Tax;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use Illuminate\Support\Facades\Http;

beforeEach(function () {

    $this->user = User::factory()->create([
        'whiteLabel' => 'admin',
    ]);

    $this->company = Company::factory()->create(["user_id" =>  $this->user->id, "isEBM" => true]);

    $this->permission = RolePermission::factory()->create();
    $this->role = Role::factory()->create([
        'user_id' => $this->user->id,
        'company_id' => $this->company->id,
        'role_permission_id' => $this->permission
    ]);

    $this->apiUrl = '/api/v1/company/branch';

    Sanctum::actingAs($this->user);
});

it("can create company branch ", function (array $payload) {

    $this->withHeader('companyId', $this->company->id);

    $response = $this->postJson($this->apiUrl, $payload);

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Branch created successfully',
        ]);

    $this->assertDatabaseHas('CompanyBranch', [
        'name' => $payload['name'],
        'phone' => $payload['phone'],
        'email' => $payload['email'],
        'address' => $payload['address'],
    ]);
})->with([
    function () {

        return [
            'name'  => 'hirwa',
            'phone' => "250788888888",
            'email' => '<EMAIL>',
            'address' => 'demo address',
            'branchCategoryID' => BranchCategory::where('name', 'General')->first()->id,
        ];
    }
]);

it("can throw error during create company branch ", function (array $payload) {

    $this->withHeader('companyId', $this->company->id);

    $response = $this->postJson($this->apiUrl, $payload['data']);

    $response->assertStatus($payload['code'])
        ->assertJson([
            'message' => $payload['message'],
        ]);
})->with([
    function () {

        CompanyBranch::factory()->create([
            'company_id' => $this->company->id,
            'name' => 'hirwa',
        ]);

        return [
            'data'  => [
                'name'  => 'hirwa',
                'address' => 'jsdk kjsdksd',
                'branchCategoryID' => BranchCategory::where('name', 'General')->first()->id,
            ],
            'message' => 'Branch with this name already exists',
            'code' => 400
        ];
    },
    function () {

        CompanyBranch::factory()->create([
            'company_id' => $this->company->id,
            'branchCode' => '00',
        ]);

        return [
            'data'  => [
                'name'  => 'hirwa',
                'address' => 'jsdk kjsdksd',
                'branchCategoryID' => 900000,
            ],
            'message' => 'Branch category not found',
            'code' => 400
        ];
    }
]);

it("can update company branch ", function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $payload['branchId']
    ]);
    $response = $this->putJson($this->apiUrl, $payload);

    $response->assertStatus(200)
        ->assertExactJsonStructure(['message'])
        ->assertJson(['message' => 'Branch updated successfully']);

    $this->assertDatabaseHas('CompanyBranch', [
        'name' => $payload['name'],
        'address' => $payload['address'],
        'phone' => $payload['phone'],
        'email' => $payload['email'],
        'address' => $payload['address']
    ]);
})->with([
    function () {

        $companyBranch = CompanyBranch::factory()->create([
            'company_id' => $this->company->id,
            'isEBM' => true
        ]);

        return [
            'branchId' => $companyBranch->id,
            'name'  => 'hirwa',
            'phone' => '0781234567',
            'email' => '<EMAIL>',
            'address' => 'jsdk kjsdksd',
        ];
    }
]);

it("can throw error during update company branch ", function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $payload['data']['branchId']
    ]);

    $response = $this->putJson($this->apiUrl, $payload['data']);

    $response->assertStatus($payload['code'])
        ->assertJson([
            'message' => $payload['message'],
        ]);
})->with([
    function () {

        CompanyBranch::factory()->create([
            'company_id' => $this->company->id,
            'name' => 'hirwa',
        ]);

        $companyBranch = CompanyBranch::factory()->create([
            'company_id' => $this->company->id,
            'isEBM' => true
        ]);

        return [
            'data' => [
                'branchId' => $companyBranch->id,
                'name'  => 'hirwa',
                'phone' => '0781234567',
                'email' => '<EMAIL>',
                'address' => 'jsdk kjsdksd',
                'branchCode' => '00',
                'topMessage' => 'top message',
                'bottomMessage' => 'bottom message',
            ],
            'message' => 'Branch with this name already exists',
            'code' => 400
        ];
    },

    function () {

        return [
            'data' => [
                'branchId' => 23232323,
                'name'  => 'hirwa',
                'phone' => '0781234567',
                'email' => '<EMAIL>',
                'address' => 'jsdk kjsdksd',
                'branchCode' => '00',
                'topMessage' => 'top message',
                'bottomMessage' => 'bottom message',
            ],
            'message' => 'Branch not found',
            'code' => 404
        ];
    }
]);


it('can get company branches ', function (array $payload) {

    $this->withHeader('companyId', $this->company->id);

    $response = $this->get($this->apiUrl);

    $response->assertStatus(200)
        ->assertExactJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'phone',
                    'email',
                    'address',
                    'branchCode',
                ]
            ],
            'currentPage',
            'lastPage',
            'itemsPerPage',
            'pageItems',
            'total',
            'timestamp'
        ]);

    expect($response->json('data'))->toHaveCount($payload['count']);

    $response->assertStatus(200);
})->with([
    function () {

        $count = fake()->numberBetween(1, 20);
        CompanyBranch::factory()->count($count)->create([
            'company_id' => $this->company->id,
        ]);

        return [
            'count' => $count
        ];
    }
]);

it('can initialize branch ', function (array $payload) {

    $branch = CompanyBranch::factory()->create([
        'company_id' => $this->company->id,
        'isEBM' => true,
        'cluster' => 'https://server.zata.rw',
        'clusterName' => 'zata'
    ]);

    $url = 'admin/branch/initialize/' . $branch->id;

    Http::fake([
        'https://server.zata.rw/*' => Http::response($payload),
    ]);

    $response = $this->get($url);

    $this->assertDatabaseHas('CompanyBranch', [
        'id' => $branch->id,
        'isInitialised' => 1,
    ]);

    if ($payload['resultCd'] === '000') {
        $this->assertDatabaseHas('CompanyBranch', [
            'id' => $branch->id,
            'isInitialised' => 1,
            'dvcId' => $payload['data']['info']['dvcId'],
            'lastPchsInvcNo' => $payload['data']['info']['lastPchsInvcNo'],
            'lastSaleInvcNo' => $payload['data']['info']['lastSaleInvcNo'],
            'vatTyCd' => $payload['data']['info']['vatTyCd'],
        ]);
    }
})->with([
    function () {

        return  [
            'resultCd' => '000',
            'resultMsg' => 'It is succeeded',
            'resultDt' => '20250106215703',
            'data' => [
                'info' => [
                    'tin' => '*********',
                    'taxprNm' => 'HIQ AFRICA Ltd',
                    'bsnsActv' => null,
                    'bhfId' => '01',
                    'bhfNm' => 'HQ',
                    'bhfOpenDt' => '20241230',
                    'prvncNm' => 'KIGALI CITY',
                    'dstrtNm' => 'NYARUGENGE',
                    'sctrNm' => 'NYARUGENGE',
                    'locDesc' => 'NYARUGENGE',
                    'hqYn' => 'Y',
                    'mgrNm' => 'HIQ AFRICA Ltd',
                    'mgrTelNo' => '0781521343',
                    'mgrEmail' => '<EMAIL>',
                    'sdcId' => null,
                    'mrcNo' => null,
                    'dvcId' => '*********0100001',
                    'intrlKey' => null,
                    'signKey' => null,
                    'cmcKey' => null,
                    'lastPchsInvcNo' => 0,
                    'lastSaleRcptNo' => null,
                    'lastInvcNo' => null,
                    'lastSaleInvcNo' => 0,
                    'lastTrainInvcNo' => null,
                    'lastProfrmInvcNo' => null,
                    'lastCopyInvcNo' => null,
                    'vatTyCd' => 2
                ]
            ]
        ];
    },

    function () {

        return [

            'resultCd' => '902',
            'resultMsg' => 'This device is installed',
            'resultDt' => '20250519090412',
            'data' => null
        ];
    }
]);
