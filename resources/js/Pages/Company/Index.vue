<script setup>
import ButtonGroupOption from '@/Components/ButtonGroupOption.vue';
import ListOption from '@/Components/ListOption.vue';
import PreviewSection from '@/Components/PreviewSection.vue';
import PrimaryLink from '@/Components/PrimaryLink.vue';
import ProfileSection from '@/Components/ProfileSection.vue';
import SecondaryLink from '@/Components/SecondaryLink.vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from "@inertiajs/vue3";
import { computed } from 'vue';


const props = defineProps({
    Company: Object | Array,
    Branches: Array | Object,
    defaultBranchId: Number,
});


const sortedBranches = computed(() => {
    return [...props.Branches].sort((a, b) => b.id === props.defaultBranchId ? 1 : -1);
});

const details = [
    { title: 'TIN', value: props.Company.tin },
    { title: 'Phone', value: props.Company.phone },
    { title: 'Email', value: props.Company.email },
    { title: 'Address', value: props.Company.address },
];
</script>

<template>
    <AppLayout title="Company Branches">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Company Details
            </h2>
        </template>

        <PreviewSection>
            <template #content>
                <ProfileSection :image="Company.image" :title="Company.name" :description="Company.email" />
                <ButtonGroupOption>
                    <PrimaryLink :href="route('company.edit', Company.id)">
                        Edit
                    </PrimaryLink>
                    <SecondaryLink :href="route('branch.new')">
                        Add Branch
                    </SecondaryLink>
                </ButtonGroupOption>
                <ListOption :details="details" />
            </template>

            <template #deatils>
                <div class="">
                    <p class="text-lg font-semibold mb-2">
                        All Branches
                    </p>
                    <section class="text-gray-600 body-font" v-if="sortedBranches.length > 0">
                        <div class="container py-2 mx-auto">
                            <div class="flex flex-wrap -m-2">
                                <div class="p-2 lg:w-1/3 md:w-1/2 w-full" v-for="branch in sortedBranches"
                                    :key="branch.id">
                                    <div
                                        class="h-full flex items-center border-gray-300 border p-4 rounded-lg bg-zata-background">
                                        <img alt=""
                                            class="w-16 h-16  object-cover object-center flex-shrink-0 rounded-full mr-4 border border-gray-300"
                                            :src="branch.image">
                                        <div class="flex-grow">
                                            <h2 class="text-gray-900 title-font font-medium">{{ branch.name }}</h2>
                                            <p class="text-gray-500">{{ branch.email }}</p>
                                            <div v-if="branch.id === defaultBranchId"
                                                class="flex justify-between items-center pt-2">
                                                <span
                                                    class="text-xs text-white bg-blue-500 rounded-full px-2 py-1">Default</span>
                                                <Link :href="route('branch.edit', branch.id)"
                                                    class="text-green-600 hover:text-green-900 underline">Edit
                                                </Link>
                                            </div>

                                            <div class="flex justify-between items-center pt-2"
                                                v-if="branch.id !== defaultBranchId">
                                                <button v-if="branch.id !== defaultBranchId"
                                                    @click="() => $inertia.get(route('branch.setDefault', branch.id))"
                                                    class="text-green-600 hover:text-green-900 underline">
                                                    Make Default
                                                </button>
                                                <Link :href="route('branch.edit', branch.id)"
                                                    class="text-green-600 hover:text-green-900 underline">Edit
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <div v-else class="flex justify-center items-center h-64">
                        <p>No branches found.</p>
                    </div>
                </div>
            </template>
        </PreviewSection>
        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>