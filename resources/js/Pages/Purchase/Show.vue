<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm } from '@inertiajs/vue3';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import {
    Card,
    CardHeader,
    CardTitle,
    CardContent,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';

const props = defineProps({
    Purchase: Object,
    Products: Array,
});

// Initialize form with purchase items including batch selection
const form = useForm({
    purchaseId: props.Purchase.id,
    status: '1',
    products: props.Purchase.items.map(item => ({
        purchaseItemId: item.id,
        productId: null,
        batchNumber: null,
    })),
});

function submit() {
    form.put(route('purchase.update', props.Purchase.id));
}
</script>

<template>
    <AppLayout title="Purchase Management">
        <template #header>
            <div class="flex items-center justify-between">
                <h2 class="text-2xl font-bold text-gray-900">Purchase #{{ props.Purchase.id }}</h2>
                
            </div>
        </template>

        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 space-y-6">

            <span :class="{
                    'px-3 py-1 rounded-full text-sm': true,
                    'bg-green-100 text-green-800': props.Purchase.status === '1',
                    'bg-red-100 text-red-800': props.Purchase.status === '0'
                }">
                    {{ props.Purchase.status === '1' ? 'Approved' : 'Rejected' }}
                </span>
            <!-- Purchase Summary -->
            <Card class="border-none shadow-sm">
                <CardHeader class="bg-gray-50">
                    <CardTitle class="text-lg font-semibold text-gray-900">Purchase Summary</CardTitle>
                </CardHeader>
                <CardContent class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <Label class="text-sm text-gray-500">Supplier</Label>
                            <p class="text-gray-900 font-medium">{{ props.Purchase.spplrNm }}</p>
                        </div>
                        <div>
                            <Label class="text-sm text-gray-500">Supplier TIN</Label>
                            <p class="text-gray-900 font-medium">{{ props.Purchase.spplrTin }}</p>
                        </div>
                        <div>
                            <Label class="text-sm text-gray-500">Invoice No</Label>
                            <p class="text-gray-900 font-medium">{{ props.Purchase.spplrInvcNo }}</p>
                        </div>
                        <div>
                            <Label class="text-sm text-gray-500">Confirm Date</Label>
                            <p class="text-gray-900 font-medium">{{ props.Purchase.cfmDt }}</p>
                        </div>
                        <div>
                            <Label class="text-sm text-gray-500">Total Items</Label>
                            <p class="text-gray-900 font-medium">{{ props.Purchase.totItemCnt }}</p>
                        </div>
                        <div>
                            <Label class="text-sm text-gray-500">Total Amount</Label>
                            <p class="text-gray-900 font-medium">{{ props.Purchase.totAmt}}</p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Link Products Form -->
            <Card class="border-none shadow-sm">
                <CardHeader class="bg-gray-50">
                    <CardTitle class="text-lg font-semibold text-gray-900">Assign Products & Batches</CardTitle>
                </CardHeader>
                <CardContent class="p-6">
                    <form @submit.prevent="submit" class="space-y-6">
                        <div v-for="(product, index) in form.products" :key="product.purchaseItemId" class="space-y-4 p-4 bg-gray-50 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-900">
                                Item: {{ props.Purchase.items[index].itemNm }} - Qty : {{ props.Purchase.items[index].qty }}
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label :for="'product-' + index" class="text-sm">Product</Label>
                                    <Select v-model="form.products[index].productId">
                                        <SelectTrigger class="bg-white">
                                            <SelectValue placeholder="Select product" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem v-for="product in props.Products" :key="product.id" :value="product.id">
                                                {{ product.name }} ({{ product.itemCode }})
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <p v-if="form.errors[`products.${index}.productId`]" class="text-red-500 text-xs mt-1">
                                        {{ form.errors[`products.${index}.productId`] }}
                                    </p>
                                </div>
                                <div>
                                    <Label :for="'batch-' + index" class="text-sm">Batch</Label>
                                    <Select v-model="form.products[index].batchNumber" :disabled="!form.products[index].productId">
                                        <SelectTrigger class="bg-white">
                                            <SelectValue placeholder="Select batch" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem 
                                                v-for="batch in props.Products.find(p => p.id === form.products[index].productId)?.batchList || []" 
                                                :key="batch.id" 
                                                :value="batch.batchNumber"
                                            >
                                                {{ batch.batchNumber }} (Expires: {{ batch.expireDate }})
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <p v-if="form.errors[`products.${index}.batchNumber`]" class="text-red-500 text-xs mt-1">
                                        {{ form.errors[`products.${index}.batchNumber`] }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-2">
                            <Label for="status" class="text-sm">Purchase Status</Label>
                            <Select v-model="form.status">
                                <SelectTrigger class="bg-white">
                                    <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="1">Approve</SelectItem>
                                    <SelectItem value="0">Reject</SelectItem>
                                </SelectContent>
                            </Select>
                            <p v-if="form.errors.status" class="text-red-500 text-xs mt-1">
                                {{ form.errors.status }}
                            </p>
                        </div>

                        <div class="flex justify-end pt-4">
                            <Button 
                                type="submit" 
                                :disabled="form.processing"
                                class="px-6 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors"
                                :class="{ 'opacity-50 cursor-not-allowed': form.processing }"
                            >
                                {{ form.processing ? 'Processing...' : 'Update Purchase' }}
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
                <template #sidenav />
        <template #footer />
    </AppLayout>
</template>

<style scoped>
.card {
    @apply transition-shadow hover:shadow-md;
}
</style>