{"openapi": "3.0.0", "info": {"title": "Zata EBM Service API", "description": "Zata Point Global EBM Service API", "contact": {"name": "NIYIBIZI HIRWA", "email": "<EMAIL>"}, "version": "1.0"}, "paths": {"/api/v1/company/branch": {"get": {"tags": ["company.branch"], "summary": "company branches", "description": "Get company branches", "operationId": "626517e87d7e0276d3366e0e761cfffa", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "perPage", "in": "query", "description": "Number of items per page", "required": false, "example": 100}, {"name": "page", "in": "query", "description": "Page number", "required": false, "example": 1}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"data": {"0": {"id": 1, "name": "Kicukiro Branch", "phone": "250788888888", "email": "<EMAIL>", "address": "KK 369 ST, Kicukiro", "branchCode": "23"}, "currentPage": 1, "lastPage": 1, "itemsPerPage": 100, "pageItems": 1, "total": 1, "timestamp": "2021-09-09, 12:00:00"}}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}, "put": {"tags": ["company.branch"], "summary": "company branch update", "description": "update company branch", "operationId": "c2a6de794c6c9f150fb01bf58a66844f", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "required": true, "example": 1}], "requestBody": {"description": "Branch Details", "required": true, "content": {"application/json": {"schema": {}, "example": {"name": "hirwa", "phone": "250789121212", "email": "<EMAIL>", "address": "jsdk kjsdksd"}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"message": "Branch created successfully"}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}, "post": {"tags": ["company.branch"], "summary": "company branch store", "description": "Create company branch", "operationId": "b317fc75b3c5c8ef25dc62a6432907ed", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}], "requestBody": {"description": "Company Branches", "required": true, "content": {"application/json": {"schema": {}, "example": {"name": "New Branch", "phone": "250788888899", "email": "<EMAIL>", "address": "sample Address", "branchCategoryID": 1}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"message": "Branch created successfully"}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/company": {"get": {"tags": ["company"], "summary": "all company", "description": "Get All company", "operationId": "6fbff07717cee4723237ce23e3d153ff", "parameters": [{"name": "perPage", "in": "query", "description": "Number of items per page", "required": false, "example": 100}, {"name": "page", "in": "query", "description": "Page number", "required": false, "example": 1}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"data": {"0": {"id": 1, "name": "Zata Point Global Service", "address": "No 1, Zata Point Street, Zata Point", "phone": "08012345678", "email": "<EMAIL>", "tin": "*********", "branchesCount": 1, "isDefault": true}, "currentPage": 1, "lastPage": 1, "itemsPerPage": 100, "pageItems": 1, "total": 1, "timestamp": "2021-09-09, 12:00:00"}}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}, "put": {"tags": ["company"], "summary": "company update", "description": "Update company details", "operationId": "caf310e116f0c473b7b6597741551c49", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}], "requestBody": {"description": "Order Details", "required": true, "content": {"application/json": {"schema": {}, "example": {"name": "Zata Point Global Service", "address": "No 1, Zata Point Street, Zata Point", "phone": "08012345678", "email": "<EMAIL>", "tin": "*********"}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"message": "Company updated successfully"}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}, "post": {"tags": ["company"], "summary": "company store", "description": "Create company", "operationId": "e604827483e04dec2616a9f4aa46b1f0", "requestBody": {"description": "Order Details", "required": true, "content": {"application/json": {"schema": {}, "example": {"name": "Zata Point Global Service", "address": "No 1, Zata Point Street, Zata Point", "phone": "08012345678", "email": "<EMAIL>", "tin": "*********"}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"message": "Company created successfully"}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/company/{companyId}": {"get": {"tags": ["company"], "summary": "company by id", "description": "Get compaby by id", "operationId": "********************************", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "companyId", "in": "path", "required": true, "example": 1}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"id": 1, "name": "Zata Point Global Service", "address": "No 1, Zata Point Street, Zata Point", "phone": "08012345678", "email": "<EMAIL>", "tin": "*********", "updated_at": "2021-09-09, 12:00:00", "insurance": {"name": "RSSB", "code": "*********", "rate": 90}}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/product": {"post": {"tags": ["Product"], "summary": "create product", "description": "Create new product", "operationId": "52fb1b0144b1e3a9805998cd57778f7b", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "required": true, "example": 1}], "requestBody": {"description": "Product Payload", "required": true, "content": {"application/json": {"schema": {}, "example": {"name": "Product Name", "packagingUnitID": 1, "quantityUnitID": 1, "countryID": 1, "taxID": 1, "branchProductCategoryID": 1}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"message": "Product created successfully", "productId": 1}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/product/branch": {"get": {"tags": ["Product"], "summary": "get product", "description": "Get product by branch id", "operationId": "a4c2d99626707c98d4a905fc7b3abb5a", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "required": true, "example": 1}, {"name": "perPage", "in": "query", "description": "Number of items per page", "required": false, "example": 100}, {"name": "page", "in": "query", "description": "Page number", "required": false, "example": 1}, {"name": "searchQuery", "in": "query", "description": "Search Query", "example": "test"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"data": [{"id": 1, "name": "Product Name", "itemCode": "Product Code", "category": "Product Category", "quantityUnit": "Quantity Unit", "packagingUnit": "Packaging Unit", "tax": "Product Tax", "class": "Product Class", "type": "Product Type", "totalStock": 10, "image": "url"}], "currentPage": 1, "lastPage": 1, "itemsPerPage": 10, "pageItems": 2, "total": 2, "timestamp": "2024-12-12, 09:16:26"}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/product/{productId}": {"get": {"tags": ["Product"], "summary": "get product by id", "description": "Get product by id and branch id", "operationId": "2a181a1aab8706b62e9757da325f5951", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "description": "Branch Id", "required": true, "schema": {"type": "integer", "example": 1}}, {"name": "productId", "in": "path", "description": "Product Id", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"id": 1, "name": "Product Name", "itemCode": "Product Code", "category": "Product Category", "categoryID": 1, "quantityUnit": "Quantity Unit", "quantityUnitID": 1, "packagingUnit": "Packaging Unit", "packagingUnitID": 1, "country": "Product Country", "countryID": 1, "tax": "Product Tax", "taxID": 1, "class": "Product Class", "classID": 1, "type": "Product Type", "typeID": 1, "branchProductCategory": "Product Category", "branchProductCategoryID": 1, "totalStock": 10, "soldInSubUnit": false, "conversionFactor": 0, "image": "url", "productDetails": [{"id": 1, "currentStock": 10, "purchasePrice": 10, "salesPrice": 10, "discountRate": 20, "status": "in_stock", "expireDate": "2024-12-12", "batchNumber": "Batch Number"}]}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}, "put": {"tags": ["Product"], "summary": "update product", "description": "Update product", "operationId": "d7f846f52d12edf0267c764ee63079d4", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "required": true, "example": 1}, {"name": "productId", "in": "path", "required": true, "example": 1}], "requestBody": {"description": "Product Payload", "required": true, "content": {"application/json": {"schema": {}, "example": {"name": "Product Name", "branchProductCategoryID": 1}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"message": "Product updated successfully"}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/product/increase-quantity/{productId}": {"put": {"tags": ["Product"], "summary": "increase product quantity", "description": "increase product quantity", "operationId": "ff2055a34174512524d7ddbe859ff51b", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "required": true, "example": 1}, {"name": "productId", "in": "path", "required": true, "example": 1}], "requestBody": {"description": "Product Payload", "required": true, "content": {"application/json": {"schema": {}, "example": {"description": "reason for increasing quantity", "quantity": 10, "salePrice": 10, "batchNumber": "Batch Number", "discountRate": 0}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"message": "Product quantity updated successfully"}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/product/reduce-quantity/{productId}": {"put": {"tags": ["Product"], "summary": "reduce product", "description": "reduce product quantity", "operationId": "8dbecfd70e3f34b4137c0c719cce8c26", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "required": true, "example": 1}, {"name": "productId", "in": "path", "required": true, "example": 1}], "requestBody": {"description": "Product Payload", "required": true, "content": {"application/json": {"schema": {}, "example": {"quantity": 10, "description": "reason for reducing quantity", "batchNumber": "Batch Number"}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"message": "Product quantity updated successfully"}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/transaction": {"get": {"tags": ["Transaction"], "summary": "Get all transactions", "description": "Get all transactions", "operationId": "e07ab63d043e17a0ee954ced66bc00e7", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "required": true, "example": 1}, {"name": "perPage", "in": "query", "description": "Number of items per page", "required": false, "example": 100}, {"name": "page", "in": "query", "description": "Page number", "required": false, "example": 1}, {"name": "fromDate", "in": "query", "description": "From date", "required": false, "example": "2023-01-01"}, {"name": "toDate", "in": "query", "description": "To date", "required": false, "example": "2023-01-01"}, {"name": "invoiceType", "in": "query", "description": "Invoice type", "required": false, "example": "sale"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"data": [{"id": 1, "invoiceType": "sale", "invoiceNumber": "INV001", "clientName": "<PERSON>", "salesDate": "2023-01-01", "totalAmount": 1000, "synced": true, "isRefunded": false, "status": "approved", "itemsCount": 2}], "currentPage": 1, "lastpage": 1, "itemsPerPage": 10, "pageItems": 1, "total": 1}}}}}, "security": [{"authentication": []}]}}, "/api/v1/transaction/sale": {"post": {"tags": ["Transaction"], "summary": "Sale transaction creation", "description": "Create a new sale transaction", "operationId": "ed2c9101d36b97aee0161d4d2dd3a458", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "required": true, "example": 1}], "requestBody": {"description": "Data to create a new sale transaction", "required": true, "content": {"application/json": {"schema": {"required": ["paymentModeId", "transactionDate", "products"], "properties": {"purchaseCode": {"type": "string", "maxLength": 200, "example": "43034"}, "paymentMethodID": {"type": "integer", "minimum": 1}, "customerID": {"type": "integer", "minimum": 1, "example": 1}, "transactionDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "note": {"type": "string", "maxLength": 200}, "customerTIN": {"type": "string", "maximum": 9, "minimum": 9, "example": "*********"}, "customerName": {"type": "string", "maxLength": 200, "example": "<PERSON>"}, "customerPhone": {"type": "string", "maxLength": 200, "example": "*********"}, "items": {"type": "array", "items": {"required": ["prductID", "units", "unitPrice", "discountRate"], "properties": {"productID": {"type": "integer", "minimum": 1}, "units": {"type": "number", "minimum": 1, "example": 12.5}, "unitPrice": {"type": "number", "minimum": 1, "example": 1000}, "discountRate": {"type": "number", "maximum": 100, "minimum": 0}, "batchNumber": {"type": "string", "maxLength": 100}}, "type": "object"}}}, "type": "object"}}}}, "responses": {"200": {"description": "Response on success", "content": {"application/json": {"schema": {}, "example": {"message": "Sale transaction created successfully", "invoiceID": 1}}}}, "404": {"description": "Business not found", "content": {"application/json": {"schema": {}, "example": {"message": "Business not found"}}}}, "500": {"description": "Unexpected sale transaction result code", "content": {"application/json": {"schema": {}, "example": {"message": "Something went wrong"}}}}}, "security": [{"authentication": []}]}}, "/api/v1/transaction/refund/{transactionId}": {"post": {"tags": ["Transaction"], "summary": "Refund transaction creation", "description": "Create a new refund transaction", "operationId": "c8062bb78d92fd5b20dba519fa7393dd", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "required": true, "example": 1}, {"name": "transactionId", "in": "path", "required": true, "example": 1}], "requestBody": {"description": "Data to create a new refund transaction", "required": true, "content": {"application/json": {"schema": {"required": ["transactionDate"], "properties": {"transactionDate": {"type": "string", "example": "8943786743"}, "purchaseCode": {"type": "string", "format": "date", "example": "2024-01-01"}, "note": {"type": "string", "maxLength": 200}}, "type": "object"}}}}, "responses": {"200": {"description": "Response on success", "content": {"application/json": {"schema": {}, "example": {"message": "Refund transaction created successfully", "invoiceID": 1}}}}, "404": {"description": "Business not found", "content": {"application/json": {"schema": {}, "example": {"message": "Business not found"}}}}, "500": {"description": "Unexpected refund transaction result code", "content": {"application/json": {"schema": {}, "example": {"message": "Something went wrong"}}}}}, "security": [{"authentication": []}]}}, "/api/v1/transaction/proforma": {"post": {"tags": ["Transaction"], "summary": "Proforma transaction creation", "description": "Create a new proforms transaction", "operationId": "3e6905aa445753754602b8e69f722dcf", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "required": true, "example": 1}], "requestBody": {"description": "Data to create a proforma transaction", "required": true, "content": {"application/json": {"schema": {"required": ["paymentModeId", "transactionDate", "products"], "properties": {"clientTin": {"type": "string", "maximum": 9, "minimum": 9, "example": "*********"}, "purchaseCode": {"type": "string", "maxLength": 200, "example": "43034"}, "paymentMethodID": {"type": "integer", "minimum": 1}, "transactionDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "note": {"type": "string", "maxLength": 200}, "items": {"type": "array", "items": {"required": ["prductID", "units", "unitPrice", "discountRate"], "properties": {"productID": {"type": "integer", "minimum": 1}, "units": {"type": "number", "minimum": 1, "example": 12.5}, "unitPrice": {"type": "number", "minimum": 1, "example": 1000}, "discountRate": {"type": "number", "maximum": 100, "minimum": 0}, "batchNumber": {"type": "string", "maxLength": 100}}, "type": "object"}}}, "type": "object"}}}}, "responses": {"200": {"description": "Response on success", "content": {"application/json": {"schema": {}, "example": {"message": "Proforma transaction created successfully", "invoiceID": 1}}}}, "404": {"description": "Business not found", "content": {"application/json": {"schema": {}, "example": {"message": "Business not found"}}}}, "500": {"description": "Unexpected sale transaction result code", "content": {"application/json": {"schema": {}, "example": {"message": "Something went wrong"}}}}}, "security": [{"authentication": []}]}}, "/api/v1/transaction/calculate": {"post": {"tags": ["Transaction"], "summary": "calculate transaction creation", "description": "calculate a new  transaction", "operationId": "4c796f41bcaaa30fccae1a540bcf316c", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "required": true, "example": 1}], "requestBody": {"description": "Data to calculate a new transaction", "required": true, "content": {"application/json": {"schema": {"required": ["items"], "properties": {"items": {"type": "array", "items": {"required": ["prductID", "units", "unitPrice", "discountRate"], "properties": {"productID": {"type": "integer", "minimum": 1}, "units": {"type": "number", "minimum": 1, "example": 12.5}, "unitPrice": {"type": "number", "minimum": 1, "example": 1000}, "discountRate": {"type": "number", "maximum": 100, "minimum": 0}, "batchNumber": {"type": "string", "maxLength": 100}}, "type": "object"}}}, "type": "object"}}}}, "responses": {"200": {"description": "Response on success", "content": {"application/json": {"schema": {}, "example": [{"id": 1212, "code": "RWAX20200001", "name": "Product Name", "units": 90, "unitPrice": 1000, "discountAmount": 100, "discountRate": 10, "taxAmount": 180, "taxRate": 18, "taxableAmount": 900, "taxName": "B", "batchNumber": "aj9kk89"}]}}}, "404": {"description": "Business not found", "content": {"application/json": {"schema": {}, "example": {"message": "Business not found"}}}}, "500": {"description": "Unexpected sale transaction result code", "content": {"application/json": {"schema": {}, "example": {"message": "Something went wrong"}}}}}, "security": [{"authentication": []}]}}, "/api/v1/transaction/{transactionId}": {"get": {"tags": ["Transaction"], "summary": "get transaction by id", "description": "get a transaction by id", "operationId": "********************************", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "required": true, "example": 1}, {"name": "transactionId", "in": "path", "required": true, "example": 1}], "responses": {"200": {"description": "Response on success", "content": {"application/json": {"schema": {}, "example": {"id": 1, "invoiceNumber": 1, "originalInvoiceNumber": 1, "clientTin": "", "clientName": "", "clientPhoneNumber": "", "salesTypeCode": "", "receiptTypeCode": "", "paymentTypeCode": "", "salesStatusCode": "", "confirmationDate": "", "type": "NS", "isRefunded": true, "taxblAmtA": 300, "taxblAmtB": 300, "taxblAmtC": 300, "taxblAmtD": 300, "taxAmtA": 300, "taxAmtB": 300, "taxAmtC": 300, "taxAmtD": 300, "totTaxblAmt": 300, "totTaxAmt": 300, "totAmt": 300, "salesDate": "2022-01-01 00:00:00", "note": "", "items": [{"productName": "Product Name", "units": 10, "unitPrice": 1000, "taxAmount": 180, "taxRate": 18, "taxName": "B", "totalAmount": 900, "totalDiscount": 100, "discountRate": 100}]}}}}, "404": {"description": "Business not found", "content": {"application/json": {"schema": {}, "example": {"message": "Business not found"}}}}, "500": {"description": "something went wrong", "content": {"application/json": {"schema": {}, "example": {"message": "Something went wrong"}}}}}, "security": [{"authentication": []}]}}, "/api/v1/transaction/download-signature/{transactionId}": {"get": {"tags": ["Transaction"], "summary": "transaction download signature", "description": "get transaction download signature", "operationId": "898515cb4aa2e1580ea8094a8cb7fd6b", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "required": true, "example": 1}, {"name": "transactionId", "in": "path", "required": true, "example": 1}, {"name": "downloadSize", "in": "query", "description": "default pdf download size, option: A4 for normal paper, m80 for 80mm and m54 for 58mm", "required": false, "example": "A4"}], "responses": {"200": {"description": "Response on success", "content": {"application/json": {"schema": {}, "example": {"url": "https://example.com/api/v1/transaction/download?expires=1746382451&id=17&signature=b264d3a5730fdcbf", "expires_at": "2022-01-01 00:00:00"}}}}, "404": {"description": "Business not found", "content": {"application/json": {"schema": {}, "example": {"message": "Business not found"}}}}, "500": {"description": "something went wrong", "content": {"application/json": {"schema": {}, "example": {"message": "Something went wrong"}}}}}, "security": [{"authentication": []}]}}, "/api/v1/transaction/download": {"get": {"tags": ["Transaction"], "summary": "download signed transaction", "description": "Download transaction using signed url", "operationId": "ceb115e8b1ecd913f5c15f82914a967a", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}, {"name": "branchId", "in": "header", "required": true, "example": 1}, {"name": "expires", "in": "query", "required": true, "example": 1}, {"name": "id", "in": "query", "required": true, "example": 1}, {"name": "signature", "in": "query", "required": true, "example": "b264d3a5730fdcbf"}], "responses": {"200": {"description": "Response on success", "content": {"application/pdf": {"schema": {"type": "string", "format": "binary"}}}}, "404": {"description": "Business not found", "content": {"application/json": {"schema": {}, "example": {"message": "Business not found"}}}}, "500": {"description": "something went wrong", "content": {"application/json": {"schema": {}, "example": {"message": "Something went wrong"}}}}}, "security": [{"authentication": []}]}}, "/api/v1/data/product-type": {"get": {"tags": ["App Data"], "summary": "product types", "description": "Get all product types", "operationId": "603b30ad8b5b94a6b24e55d865ba7e6d", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": [{"id": 1, "name": "Product Type 1", "description": "Product Type 1 description"}, {"id": 2, "name": "Product Type 2", "description": "Product Type 2 description"}, {"id": 3, "name": "Product Type 3", "description": "Product Type 3 description"}]}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/data/payment-mode": {"get": {"tags": ["App Data"], "summary": "payment modes", "description": "Get all payment modes", "operationId": "a00f0b756620f75fb06ea66b36f188dd", "parameters": [{"name": "companyId", "in": "header", "required": true, "example": 1}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": [{"id": 1, "name": "Payment Mode 1", "description": "Payment Mode 1 description"}, {"id": 2, "name": "Payment Mode 2", "description": "Payment Mode 2 description"}, {"id": 3, "name": "Payment Mode 3", "description": "Payment Mode 3 description"}]}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/data/product-category": {"get": {"tags": ["App Data"], "summary": "product categories", "description": "Get all product categories", "operationId": "15845ccea8654f33a7b3d99025bc5432", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": [{"id": 1, "name": "Product category 1"}]}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/data/product-class": {"get": {"tags": ["App Data"], "summary": "product classes", "description": "Get all product classes", "operationId": "4d5843502eae14fdde0cff7565e6e92b", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": [{"id": 1, "name": "Product Class 1", "code": "Product Class 1 code"}]}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/data/product-quantity-unit": {"get": {"tags": ["App Data"], "summary": "product quantity units", "description": "Get all product quantity units", "operationId": "09708a2c1f1c55a4469aecd83d3c2234", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": [{"id": 1, "name": "Product Quantity Unit 1", "code": "Product Quantity Unit 1 code"}]}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/data/product-packaging-unit": {"get": {"tags": ["App Data"], "summary": "product packaging units", "description": "Get all product packaging units", "operationId": "93555b9a721e07cdad18ad6b90a9f790", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": [{"id": 1, "name": "Product Packaging Unit 1", "code": "Product Packaging Unit 1 code"}]}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/data/product-tax": {"get": {"tags": ["App Data"], "summary": "product taxes", "description": "Get all product taxes", "operationId": "c2fe9d0638baaebd2e6183c03ca19176", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": [{"id": 1, "name": "Product Tax 1", "rate": 18}]}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/data/product-country-origin": {"get": {"tags": ["App Data"], "summary": "product countries", "description": "Get all product countries of origin", "operationId": "b705e7167fc0ca78efb7742a5d6915c3", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": [{"id": 1, "name": "Country 1", "code": "Country Code 1"}]}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}, "/api/v1/test": {"get": {"tags": ["Test"], "summary": "Test API", "description": "Test API", "operationId": "cd52341d8f9c422cd7cdd22a2d32b561", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {}, "example": {"message": "Zata Point Global Service API", "status": "Connected", "time": "2021-09-29T14:48:00.000000Z"}}}}, "401": {"description": "Unauthenticated", "content": {"application/json": {"schema": {}, "example": {"message": "Unauthenticated."}}}}, "500": {"description": "Server error, something went wrong on the server", "content": {"application/json": {"schema": {}, "example": {"message": "Server error."}}}}}, "security": [{"authentication": []}]}}}, "tags": [{"name": "company.branch", "description": "company.branch"}, {"name": "company", "description": "company"}, {"name": "Product", "description": "Product"}, {"name": "Transaction", "description": "Transaction"}, {"name": "App Data", "description": "App Data"}, {"name": "Test", "description": "Test"}], "components": {"securitySchemes": {"authentication": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter token in format (Bearer <token>)", "name": "Authorization", "in": "header"}}}}