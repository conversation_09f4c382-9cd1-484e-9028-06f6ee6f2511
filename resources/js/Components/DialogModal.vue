<script setup>
import { defineProps, defineEmits } from 'vue';
import Modal from './Modal.vue';

const emit = defineEmits(['close']);

defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    maxWidth: {
        type: String,
        default: '2xl',
    },
    closeable: {
        type: Boolean,
        default: true,
    },
});

const close = () => {
    emit('close');
};
</script>

<template>
    <Modal :show="show" :max-width="maxWidth" :closeable="closeable" @close="close">
        <div class="relative">
            <!-- Close Button -->
            <div class="border-b border-gray-200  px-6 py-6">
                <button @click="close" class="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-lg p-2">
                    <span class="sr-only">Close</span>
                    <svg class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>

                <div class="text-lg font-medium text-gray-900" v-if="$slots.title">
                    <slot name="title" />
                </div>
            </div>


            <div class="mt-4 text-sm text-gray-600 px-6 py-4" v-if="$slots.content">
                <slot name="content" />
            </div>
        </div>

        <div class="flex flex-row justify-end px-6 py-6 border-t border-gray-200 text-end" v-if="$slots.footer">
            <slot name="footer" />
        </div>
    </Modal>
</template>
