<?php

namespace App\Services;

use App\Data\StockItemsData;
use App\Data\StockItemsListData;
use App\Data\StockMasterData;
use App\Models\CompanyBranch;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Models\StockTracking;

class StockTrackingService
{
    protected string $saveStockItemsEndpoint;
    protected string $saveStockMasterEndpoint;

    public function __construct(protected EbmService $ebmService)
    {
        $this->saveStockItemsEndpoint = config('ebm.save_stock_items');
        $this->saveStockMasterEndpoint =  config('ebm.save_stock_master');
    }

    public function stockItemsSave(StockItemsData $data, CompanyBranch $branch): void
    {


        if ($branch->cluster != 'demo') {
            $url =  $this->ebmService->buildUrl(
                $branch->cluster,
                $branch->clusterName,
                $this->saveStockItemsEndpoint
            );

            $response = Http::post($url, $data->toArray());

            if (!$response->successful() || $response->json()['resultCd'] !== '000') {

                Log::error('Failed to send stock I/O to EBM', [
                    'data' => $data,
                    'status' => $response->status(),
                    'payload' => $data,
                    'response' => $response->json() ?? $response->body()
                ]);
            }

            Log::info('Stock I/O sent to EBM', [
                'data' => $data,
                'status' => $response->status(),
                'payload' => $data,
                'response' => $response->json() ?? $response->body()
            ]);
        }
    }

    public function stockMasterSave(StockMasterData $data, CompanyBranch $branch): void
    {


        if ($branch->cluster != 'demo') {
            $url = $this->ebmService->buildUrl(
                $branch->cluster,
                $branch->clusterName,
                $this->saveStockMasterEndpoint
            );
            $httpResponse = Http::post($url, $data->toArray());

            if (!$httpResponse->successful() || $httpResponse->json()['resultCd'] !== '000') {

                Log::error('Failed to send stock master to EBM', [
                    'data' => $data,
                    'status' => $httpResponse->status(),
                    'payload' => $data,
                    'response' => $httpResponse->json() ?? $httpResponse->body()
                ]);
            }

            Log::info('Stock master sent to EBM', [
                'data' => $data,
                'status' => $httpResponse->status(),
                'payload' => $data,
                'response' => $httpResponse->json() ?? $httpResponse->body()
            ]);
        }
    }

    public  function stockIoSave($transaction, CompanyBranch $branch): void
    {


        if ($branch->cluster != 'demo' && $branch->isInitialised) {
            $stockItems = array_map(fn($item) => StockItemsListData::from([
                'itemSeq' => $item['itemSeq'],
                'itemCd' => $item['itemCd'],
                'itemClsCd' => $item['itemClsCd'],
                'itemNm' => $item['itemNm'],
                'bcd' => $item['bcd'],
                'pkgUnitCd' => $item['pkgUnitCd'],
                'pkg' => 1,
                'qtyUnitCd' => 'U',
                'qty' => $item['qty'],
                'itemExprDt' => $item['itemExprDt'],
                'prc' => $item['prc'],
                'splyAmt' => $item['splyAmt'],
                'totDcAmt' => $item['totDcAmt'],
                'taxblAmt' => $item['taxblAmt'],
                'taxTyCd' => $item['taxTyCd'],
                'taxAmt' => $item['taxAmt'],
                'totAmt' => $item['totAmt'],
            ]),  $transaction["itemList"]);

            $stockData = StockItemsData::from([
                'tin' => $transaction['tin'],
                'bhfId' => $transaction['bhfId'],
                'sarNo' =>  $this->stockTracking($branch->id),
                'orgSarNo' => 0,
                'regTyCd' => 'M',
                'custTin' => null,
                'custNm' => null,
                'custBhfId' => null,
                'sarTyCd' => '11',
                'ocrnDt' => Carbon::now()->format('Ymd'),
                'totItemCnt' => count($stockItems),
                'totTaxblAmt' => $transaction["totTaxblAmt"],
                'totTaxAmt' => $transaction["totTaxAmt"],
                'totAmt' => $transaction["totAmt"],
                'remark' => null,
                'regrId' => 'KudiBooks',
                'regrNm' => 'KudiBooks',
                'modrNm' => 'KudiBooks',
                'modrId' => 'KudiBooks',
                'itemList' => $stockItems
            ]);

            $url =  $this->ebmService->buildUrl(
                $branch->cluster,
                $branch->clusterName,
                $this->saveStockItemsEndpoint
            );
            $response = Http::timeout(600)->post($url, $stockData->toArray());

            if (!$response->successful() || $response->json()['resultCd'] !== '000') {

                Log::error('Failed to send stock I/O to EBM', [
                    'data' => $stockData,
                    'status' => $response->status(),
                    'payload' => $stockData,
                    'response' => $response->json() ?? $response->body()
                ]);
            }

            Log::info('Stock I/O sent to EBM', [
                'data' => $stockData,
                'status' => $response->status(),
                'payload' => $stockData,
                'response' => $response->json() ?? $response->body()
            ]);
        }
    }

    private function stockTracking($branchId): int
    {
        $trackNumber = StockTracking::where('branch_id', $branchId)->latest()->first();

        return $trackNumber  ? $trackNumber->trackingNumber + 1 : StockTracking::create(['branch_id' => $branchId, 'trackingNumber' => 1])->trackingNumber;
    }
}
