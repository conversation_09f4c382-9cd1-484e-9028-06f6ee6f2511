<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OrderItems>
 */
class OrderItemsFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [

            'order_id' => Order::factory(),
            'product_id' => Product::factory(),
            'productName' => $this->faker->name,
            'productCode' => $this->faker->word,
            'batchNumber' => $this->faker->word,
            'expireDate' => $this->faker->date(),
            'quantity' => $this->faker->randomFloat(2, 0, 1000),
            'price' => $this->faker->randomFloat(2, 0, 1000),
            'discount' => $this->faker->randomFloat(2, 0, 1000),
            'supplyAmount' => $this->faker->randomFloat(2, 0, 1000),
            'taxAmount' => $this->faker->randomFloat(2, 0, 1000),
            'taxRate' => $this->faker->randomFloat(2, 0, 1000),
            'taxName' => $this->faker->word,
            'totalAmount' => $this->faker->randomFloat(2, 0, 1000),
            'totalDiscount' => $this->faker->randomFloat(2, 0, 1000),

        ];
    }
}
