<?php

use App\Models\User;

test('profile information can be updated', function () {
    $this->actingAs($user = User::factory()->create());

    $data = [
        'name' => fake()->name(),
        'email' => fake()->unique()->safeEmail(), 
    ];
    $this->put('/user/profile-information', $data);

    expect($user->fresh()->name)->toEqual($data['name']);
    expect($user->fresh()->email)->toEqual($data['email']);
});