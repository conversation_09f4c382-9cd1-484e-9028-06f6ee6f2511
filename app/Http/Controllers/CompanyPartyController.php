<?php

namespace App\Http\Controllers;

use App\Data\NewCompanyPartyData;
use App\Data\NewCustomerData;
use App\Enums\PartyTypeEnums;
use App\Http\Requests\NewCompanyPartyRequest;
use App\Http\Requests\NewCustomerRequest;
use App\Services\CompanyPartyService;
use App\Services\InsuranceService;
use Inertia\Inertia;

class CompanyPartyController extends Controller
{
    public function __construct(
        protected CompanyPartyService $service,
        protected InsuranceService $insuranceService
    ) {}

    public function customer()
    {

        $company = companyData();
        $parties = $this->service->companyParties($company->id, PartyTypeEnums::CUSTOMER->value);
        return Inertia::render('Company/Party/Customer', [
            'Parties' => $parties,

        ]);
    }

    public function supplier()
    {

        $company = companyData();
        $parties = $this->service->companyParties($company->id, PartyTypeEnums::SUPPLIER->value);
        return Inertia::render('Company/Party/Supplier', [
            'Parties' => $parties
        ]);
    }

    public function newCustomer()
    {

        $company =  companyData();
        $insurance = $this->insuranceService->supportedInsurance($company->id);
        return Inertia::render('Company/Party/NewCustomer', [
            'Insurance' => $insurance
        ]);
    }

    public function editCustomer(int $customerId){

        $company = companyData();
        $customer = $this->service->partyById($customerId, $company->id);
        $insurance = $this->insuranceService->supportedInsurance($company->id);
        return Inertia::render('Company/Party/EditCustomer', [
            'Customer' => $customer,
            'Insurance' => $insurance
        ]);
    }

    public function updateCustomer(NewCustomerRequest $request, int $customerId)
    {

        $company = companyData();
        $data = NewCustomerData::from($request->validated());
        $this->service->updateCustomer($data, $customerId, $company->id);
        return redirect()->route('customer.index')->banner('Customer Updated Successfully');
    }

    public function newSupplier()
    {

        companyData();
        return Inertia::render('Company/Party/NewSupplier', []);
    }

    public function storeSupplier(NewCompanyPartyRequest $request)
    {

        $company = companyData();
        $data = NewCompanyPartyData::from($request->validated());
        $this->service->storeParty($data, $company->id);
        return  redirect()->route('supplier.index')->banner('Supplier Created Successfully');
    }

    public function storeCustomer(NewCustomerRequest $request)
    {

        $company = companyData();
        $data = NewCustomerData::from($request->validated());
        $this->service->storeCustomer($data, $company->id);
        return  redirect()->route('customer.index')->banner('Customer Created Successfully');
    }

    public function  show(int $partyId)
    {

        $company = companyData();
        $party = $this->service->partyById($partyId, $company->id);
        return Inertia::render('Company/Party/Show', [
            'Party' => $party
        ]);
    }

    public function edit(int $partyId)
    {

        $company = companyData();
        $party = $this->service->partyById($partyId, $company->id);

        $partyTypes = $this->service->partyTypes($company->id);
        return Inertia::render('Company/Party/Edit', [
            'Party' => $party,
            'PartyTypes' => $partyTypes
        ]);
    }

    public function update(NewCompanyPartyRequest $request, int $partyId)
    {

        $company = companyData();
        $data = NewCompanyPartyData::from($request->validated());
        $this->service->updateParty($data, $partyId, $company->id);
        return redirect()->back()->banner('Updated successfully.');
    }
}
