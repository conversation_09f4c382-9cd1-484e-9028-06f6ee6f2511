<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useForm } from "@inertiajs/vue3";
import AppLayout from '@/Layouts/AppLayout.vue';
import axios from 'axios';
import { Button } from '@/components/ui/button';

const props = defineProps({
    StockHistories: { type: Object, required: true },
    Products: { type: Object, required: true },
    Cashiers: { type: Object, required: true },
    Parties: { type: Object, required: true },
    currentPage: Number,
    lastPage: Number,
    itemsPerPage: Number,
    total: Number,
    searchQuery: String,
});

const tableHeaders = [
    "Product Name", "Quantity", "Stock Type", "Transaction", "Date", "User", "Batch", "Exp Date", "Customer / Supplier"
];

const tableRows = computed(() => {
    return props.StockHistories.map(stock => ({
        product: truncateName(stock.product),
        quantity: stock.quantity,
        stockType: stock.stockType,
        orderType: stock.orderType,
        created_at: stock.created_at.split(' ')[0] || 'N/A',
        user: stock.user,
        batchNumber: stock.batchNumber,
        expiryDate: stock.expiryDate ? stock.expiryDate.split(' ')[0] : null,
        customerSupplier: stock.party
    }));
});

const showFilters = ref(false);
const downloading = ref(false);
const errorMessage = ref('');

const filterOptions = reactive({
    products: props.Products.map(product => ({
        value: product.id,
        name: truncateName(product.name),
    })),
    stockTypes: [
        { id: 'in', name: 'In' },
        { id: 'out', name: 'Out' },
        { id: 'in from', name: 'In From' },
        { id: 'out to', name: 'Out To' },
    ],
    cashiers: props.Cashiers.map(cashier => ({
        id: cashier.id,
        name: cashier.name
    })),
    parties: props.Parties.map(party => ({
        id: party.id,
        name: party.name
    }))
});

onMounted(() => {
    fetchFilterOptions();
});

async function fetchFilterOptions() {
    try {
        const response = await axios.get(route('product.stockHistory'));
        Object.assign(filterOptions, response.data);
    } catch (error) {
        console.error('Failed to fetch filter options:', error);
    }
}

const paginationState = reactive({
    currentPage: props.currentPage,
    itemsPerPage: props.itemsPerPage,
    totalPages: props.lastPage,
});

const paginationForm = useForm({
    page: paginationState.currentPage,
    perPage: paginationState.itemsPerPage,
    searchQuery: props.searchQuery || '',
    fromDate: '',
    toDate: '',
    customer: '',
    supplier: '',
    productID: '',
    stockType: '',
    cashierID: '',
    partyID: ''
});

function toggleFilters() {
    showFilters.value = !showFilters.value;
}

function applyFilters() {
    paginationState.currentPage = 1;
    updatePagination();
}

function resetFilters() {
    paginationForm.reset();
    paginationForm.searchQuery = '';
    paginationForm.fromDate = '';
    paginationForm.toDate = '';
    paginationForm.customer = '';
    paginationForm.supplier = '';
    paginationForm.productID = '';
    paginationForm.stockType = '';
    paginationForm.cashierID = '';
    paginationForm.partyID = '';
    paginationState.currentPage = 1;
    updatePagination();
}

function updatePagination() {
    paginationForm.page = paginationState.currentPage;
    paginationForm.perPage = paginationState.itemsPerPage;
    paginationForm.get(route('product.stockHistory'), {
        preserveState: true,
        preserveScroll: true,
    });
}

function handleItemsPerPageChange() {
    paginationState.currentPage = 1;
    updatePagination();
}

async function downloadFile() {
    downloading.value = true;
    errorMessage.value = '';

    try {
        const queryParams = new URLSearchParams(paginationForm.data()).toString();
        const url = `${route('product.stockHistoryDownload')}?${queryParams}`;

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/pdf',
            },
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Download failed');
        }

        const blob = await response.blob();
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `stock-history-${new Date().toISOString().replace(/[:.]/g, '-')}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
        errorMessage.value = error.message;
    } finally {
        downloading.value = false;
    }
}

function prevPage() {
    if (paginationState.currentPage > 1) {
        paginationState.currentPage--;
        updatePagination();
    }
}

function nextPage() {
    if (paginationState.currentPage < paginationState.totalPages) {
        paginationState.currentPage++;
        updatePagination();
    }
}

function truncateName(name, maxLength = 30) {
    if (name.length > maxLength) {
        return name.substring(0, maxLength) + ' ...';
    }
    return name;
}
</script>

<template>
    <AppLayout title="Stock History">
        <div class="py-6 px-4 sm:px-6 lg:px-8">
            <!-- Header Section -->
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                <h3 class="text-2xl font-bold text-gray-900">Stock History</h3>
                <Button
                    @click="downloadFile"
                    :disabled="downloading"
                    variant="default"
                    class="inline-flex items-center"
                >
                    <svg v-if="downloading" class="animate-spin -ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {{ downloading ? 'Downloading...' : 'Download PDF' }}
                </Button>
            </div>

            <!-- Error Message -->
            <transition name="fade">
                <div v-if="errorMessage" class="mb-6 p-4 bg-red-50 text-red-700 rounded-lg">
                    {{ errorMessage }}
                </div>
            </transition>

            <!-- Filters Section -->
            <div class="mb-6">
                <Button
                    @click="toggleFilters"
                    variant="outline"
                    class="inline-flex items-center"
                >
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                    </svg>
                    {{ showFilters ? 'Hide Filters' : 'Show Filters' }}
                </Button>

                <!-- Filter Panel -->
                <transition name="slide-fade">
                    <div v-if="showFilters" class="mt-4 p-6 bg-white rounded-xl shadow-sm border border-gray-100">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <!-- Date Filters -->
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-gray-700">Date Range</label>
                                <div class="flex gap-2">
                                    <input 
                                        type="date" 
                                        v-model="paginationForm.fromDate" 
                                        class="w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500" 
                                    />
                                    <input 
                                        type="date" 
                                        v-model="paginationForm.toDate" 
                                        class="w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500" 
                                    />
                                </div>
                            </div>

                            <!-- Product Filter -->
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-gray-700">Product</label>
                                <select 
                                    v-model="paginationForm.productID" 
                                    class="w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">All Products</option>
                                    <option v-for="product in filterOptions.products" :key="product.value" :value="product.value">
                                        {{ product.name }}
                                    </option>
                                </select>
                            </div>

                            <!-- Stock Type Filter -->
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-gray-700">Stock Type</label>
                                <select 
                                    v-model="paginationForm.stockType" 
                                    class="w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">All Stock Types</option>
                                    <option v-for="type in filterOptions.stockTypes" :key="type.id" :value="type.id">
                                        {{ type.name }}
                                    </option>
                                </select>
                            </div>

                            <!-- Cashier Filter -->
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-gray-700">Cashier</label>
                                <select 
                                    v-model="paginationForm.cashierID" 
                                    class="w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">All Cashiers</option>
                                    <option v-for="cashier in filterOptions.cashiers" :key="cashier.id" :value="cashier.id">
                                        {{ cashier.name }}
                                    </option>
                                </select>
                            </div>

                            <!-- Party Filter -->
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-gray-700">Customer / Supplier</label>
                                <select 
                                    v-model="paginationForm.partyID" 
                                    class="w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="">All Parties</option>
                                    <option v-for="party in filterOptions.parties" :key="party.id" :value="party.id">
                                        {{ party.name }}
                                    </option>
                                </select>
                            </div>

                            <!-- Search -->
                            <div class="space-y-2 md:col-span-2 lg:col-span-3">
                                <label class="block text-sm font-medium text-gray-700">Search</label>
                                <input
                                    type="text"
                                    v-model="paginationForm.searchQuery"
                                    placeholder="Search stock history..."
                                    class="w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                                />
                            </div>
                        </div>

                        <!-- Filter Actions -->
                        <div class="mt-6 flex gap-4">
                            <Button
                                @click="applyFilters"
                                variant="default"
                            >
                                Apply Filters
                            </Button>
                            <Button
                                @click="resetFilters"
                                variant="outline"
                            >
                                Reset Filters
                            </Button>
                        </div>
                    </div>
                </transition>
            </div>

            <!-- Table -->
            <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                <div v-if="props.StockHistories.length > 0" class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th v-for="header in tableHeaders" :key="header" class="px-6 py-3 text-left text-sm font-semibold text-gray-900">
                                    {{ header }}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <tr v-for="row in tableRows" :key="row.created_at" class="hover:bg-gray-50 transition-colors duration-100">
                                <td v-for="cell in row" :key="cell" class="px-6 py-4 text-sm text-gray-700">
                                    {{ cell || '-' }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div v-else class="text-center py-12 text-gray-500">
                    No stock history found
                </div>
            </div>

            <!-- Pagination -->
            <div class="mt-6 flex flex-col sm:flex-row justify-between items-center gap-4">
                <div class="flex items-center gap-2">
                    <label class="text-sm text-gray-700">Show</label>
                    <select
                        v-model="paginationState.itemsPerPage"
                        @change="handleItemsPerPageChange"
                        class="rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                        <option :value="5">5</option>
                        <option :value="10">10</option>
                        <option :value="20">20</option>
                        <option :value="50">50</option>
                        <option :value="100">100</option>
                    </select>
                    <span class="text-sm text-gray-700">per page</span>
                </div>
                <div class="flex items-center gap-4">
                    <Button
                        @click="prevPage"
                        :disabled="paginationState.currentPage === 1"
                        variant="outline"
                    >
                        Previous
                    </Button>
                    <span class="text-sm text-gray-700">
                        Page {{ paginationState.currentPage }} of {{ paginationState.totalPages }}
                    </span>
                    <Button
                        @click="nextPage"
                        :disabled="paginationState.currentPage === paginationState.totalPages"
                        variant="outline"
                    >
                        Next
                    </Button>
                </div>
            </div>
        </div>
        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>