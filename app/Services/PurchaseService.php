<?php

namespace App\Services;

use App\Data\PurchaseUpdateData;
use App\Data\UpdateProductQuantityData;
use App\Enums\PurchaseStatusEnums;
use App\Enums\TransactionTypeEnums;
use App\Exceptions\BadRequest;
use App\Exceptions\NotFound;
use App\Models\CompanyBranch;
use Illuminate\Support\Facades\Log;
use App\Models\Purchase;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

use App\Models\Product;
use App\Models\ProductDetail;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

use App\Jobs\StockMasterSave;
use App\Data\StockMasterData;

class PurchaseService
{

    public function __construct(
        protected EbmService $ebmService,
        protected ProductService $productService
    ) {}
    public function checkPurchase($transactionDate, CompanyBranch $branch)
    {

        $salesList = $this->ebmService->syncPurchase($transactionDate, $branch);

        if (!empty($salesList)) {

            Log::info('Purchase data', ['data' => $salesList]);
            foreach ($salesList as $sale) {
                $purchaseData = [
                    'branch_id' => $branch->id,
                    'spplrTin' => $sale['spplrTin'],
                    'spplrNm' => $sale['spplrNm'],
                    'spplrBhfId' => $sale['spplrBhfId'],
                    'spplrInvcNo' => $sale['spplrInvcNo'],
                    'rcptTyCd' => $sale['rcptTyCd'],
                    'pmtTyCd' => $sale['pmtTyCd'],
                    'cfmDt' => $sale['cfmDt'],
                    'salesDt' => $sale['salesDt'],
                    'stockRlsDt' => $sale['stockRlsDt'],
                    'totItemCnt' => $sale['totItemCnt'],
                    'taxblAmtA' => $sale['taxblAmtA'],
                    'taxblAmtB' => $sale['taxblAmtB'],
                    'taxblAmtC' => $sale['taxblAmtC'],
                    'taxblAmtD' => $sale['taxblAmtD'],
                    'taxRtA' => $sale['taxRtA'],
                    'taxRtB' => $sale['taxRtB'],
                    'taxRtC' => $sale['taxRtC'],
                    'taxRtD' => $sale['taxRtD'],
                    'taxAmtA' => $sale['taxAmtA'],
                    'taxAmtB' => $sale['taxAmtB'],
                    'taxAmtC' => $sale['taxAmtC'],
                    'taxAmtD' => $sale['taxAmtD'],
                    'totTaxblAmt' => $sale['totTaxblAmt'],
                    'totTaxAmt' => $sale['totTaxAmt'],
                    'totAmt' => $sale['totAmt'],
                ];

                $existingPurchase = Purchase::where($purchaseData)->first();
                if (!$existingPurchase) {
                    $purchase = Purchase::create($purchaseData);
                    $purchase->items()->createMany($sale['itemList']);
                }
            }
        }
    }

    public function getPurchase(CompanyBranch $branch, $perPage, $page): LengthAwarePaginator
    {

        return Purchase::where('branch_id', '=', $branch->id)
            ->with('items')
            ->paginate(perPage: $perPage, page: $page)
            ->through(fn($purchase) => [
                'id' => $purchase->id,
                'spplrNm' => $purchase->spplrNm,
                'spplrTin' => $purchase->spplrTin,
                'spplrBhfId' => $purchase->spplrBhfId,
                'spplrInvcNo' => $purchase->spplrInvcNo,
                'cfmDt' => $purchase->cfmDt,
                'salesDt' => $purchase->salesDt,
                'stockRlsDt' => $purchase->stockRlsDt,
                'totItemCnt' => $purchase->totItemCnt,
                'totTaxblAmt' => $purchase->totTaxblAmt,
                'totTaxAmt' => $purchase->totTaxAmt,
                'totAmt' => $purchase->totAmt,
                'status' => $purchase->status
            ]);
    }

    public function getPurchaseById(CompanyBranch $branch, int $purchaseId)
    {

        $purchase =  Purchase::where('id', $purchaseId)
            ->where('branch_id', $branch->id)
            ->with('items')
            ->first();

        throw_if(is_null($purchase), NotFound::class, 'Purchase not found');

        return $purchase;
    }

    public function updatePurchase(PurchaseUpdateData $data, CompanyBranch $branch, int $userId)
    {

        $purchaseOrder = Purchase::where('id', $data->purchaseId)
            ->where('branch_id', $branch->id)
            ->with('items')->first();

        throw_if(is_null($purchaseOrder), NotFound::class, 'Purchase not found');

        // thrwo if purchase is not pending 
        throw_if($purchaseOrder->status != PurchaseStatusEnums::PENDING->value, BadRequest::class, 'Forbidden Action');

        $status = $data->status ? PurchaseStatusEnums::ACCEPTED->value : PurchaseStatusEnums::REJECTED->value;

        $purchaseOrder->status = $status;
        $purchaseOrder->accepted = true;
        $purchaseOrder->save();

        $items = [];

        foreach ($data->products as $product) {

            $selectedItem = $purchaseOrder->items()->where('id', $product->purchaseItemId)->first();
            $selectedProduct = Product::where('id', $product->productId)
                ->where('branch_id', $branch->id)
                ->first();

            throw_if(is_null($selectedItem), NotFound::class, 'Item not found');
            throw_if(is_null($selectedProduct), NotFound::class, 'Product not found');

            $batch = ProductDetail::where('product_id', $product->productId)
                ->where('batchNumber', $product->batchNumber)
                ->first();

            throw_if(is_null($batch), NotFound::class, 'Batch not found');

            $items[] = [
                'itemSeq' => $selectedItem->itemSeq,
                'itemCd' => $selectedProduct->item_code,
                'itemClsCd' => $selectedItem->itemClsCd,
                'itemNm' => $selectedProduct->name,
                'bcd' => $selectedItem->bcd,
                'pkgUnitCd' => $selectedItem->pkgUnitCd,
                'pkg' => $selectedItem->pkg,
                'qtyUnitCd' => $selectedItem->qtyUnitCd,
                'qty' => $selectedItem->qty,
                'prc' => $selectedItem->prc,
                'splyAmt' => $selectedItem->splyAmt,
                'dcRt' => $selectedItem->dcRt,
                'dcAmt' => $selectedItem->dcAmt,
                'taxblAmt' => $selectedItem->taxblAmt,
                'taxTyCd' => $selectedItem->taxTyCd,
                'taxAmt' => $selectedItem->taxAmt,
                'totAmt' => $selectedItem->totAmt,
                'itemExprDt' => null,
                'productId' => $selectedProduct->id,
                'batchNumber' => $product->batchNumber,
                'expireDate' => $batch->expireDate
            ];
        }

        $ebmData = [
            'tin' => $branch->company->tin,
            'bhfId' => $branch->branchCode,
            'invcNo' =>  $purchaseOrder->spplrInvcNo,
            'orgInvcNo' => 0,
            'spplrTin' => $purchaseOrder->spplrTin,
            'spplrBhfId' => $purchaseOrder->spplrBhfId,
            'spplrNm' => $purchaseOrder->spplrNm,
            'spplrInvcNo' => $purchaseOrder->spplrInvcNo,
            'regTyCd' => 'M',
            "pchsTyCd" => "N",
            'rcptTyCd' => 'P',
            'prcOrdCd' => $purchaseOrder->prcOrdCd,
            'pmtTyCd' => '01',
            'pchsSttsCd' => $data->status  ? '02' : '04',
            "cfmDt" => null,
            "pchsDt" => Carbon::now()->format('Ymd'),
            'wrhsDt' => null,
            'cnclReqDt' => null,
            'cnclDt' => null,
            'rfdDt' => null,
            'totItemCnt' => count($items),
            'taxblAmtA' => $purchaseOrder->taxblAmtA,
            'taxblAmtB' => $purchaseOrder->taxblAmtB,
            'taxblAmtC' => $purchaseOrder->taxblAmtC,
            'taxblAmtD' => $purchaseOrder->taxblAmtD,
            'taxRtA' => $purchaseOrder->taxRtA,
            'taxRtB' => $purchaseOrder->taxRtB,
            'taxRtC' => $purchaseOrder->taxRtC,
            'taxRtD' => $purchaseOrder->taxRtD,
            'taxAmtA' => $purchaseOrder->taxAmtA,
            'taxAmtB' => $purchaseOrder->taxAmtB,
            'taxAmtC' => $purchaseOrder->taxAmtC,
            'taxAmtD' => $purchaseOrder->taxAmtD,
            'totTaxblAmt' => $purchaseOrder->totTaxblAmt,
            'totTaxAmt' => $purchaseOrder->totTaxAmt,
            'totAmt' => $purchaseOrder->totAmt,
            'remark' => null,
            'regrNm' => 'Admin',
            'regrId' => 'Admin',
            'modrNm' => 'Admin',
            'modrId' => 'Admin',
            'itemList' => $items,
        ];

        $this->ebmService->savePurchase($branch, $ebmData);
        if ($data->status) {

            foreach ($items as $item) {

                $itemData = UpdateProductQuantityData::from([
                    'quantity' => $item['qty'],
                    'description' => 'Purchase Order',
                    'movingUnit' => 'Main',
                    'orderType' => TransactionTypeEnums::PURCHASE->value,
                    'batchNumber' =>  $item['batchNumber'],
                    'expireDate' =>  $item['expireDate'],
                    'partyId' => 1, // TODO, get party id from purchase order
                ]);

                $this->productService->increaseProductQuantity($itemData, $branch, $item['productId'], $userId);

                $productToUse = ProductDetail::where('product_id', $item['productId'])
                    ->where('batchNumber', $item['batchNumber'])
                    ->first();

                StockMasterSave::dispatch(StockMasterData::from([
                    'tin' => $branch->company->tin,
                    'bhfId' => $branch->branchCode,
                    'itemCd' =>   $productToUse->product->itemCode,
                    'rsdQty' =>   $productToUse->currentStock,
                    'regrId' => 'HiqAfrica',
                    'regrNm' => 'HiqAfrica',
                    'modrNm' => 'HiqAfrica',
                    'modrId' => 'HiqAfrica'
                ]), $branch);
            }
        }
    }
}
