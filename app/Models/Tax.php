<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tax extends Model
{
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'Tax';

    protected $fillable = [
        'code',
        'rate',
        'forEBM',
        'name',
        'description',
    ];

    protected $casts = [
        'forEBM' => 'boolean',
    ];

    /*

    <?php

namespace App\Services;

use App\Data\NewProductData;
use App\Data\SaveItemData;
use App\Models\Country;
use App\Models\ImportProduct;
use App\Models\Party;
use App\Models\PaymentMode;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductClass;
use App\Models\ProductDetail;
use App\Models\ProductPackingUnit;
use App\Models\ProductQuantityUnit;
use App\Models\ProductType;
use App\Models\RequestDate;
use App\Models\Tax;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Services\StockService;
use App\Data\StockMasterSaveData;
use App\Exceptions\NotFound;

class ProductService
{

    public function __construct(protected StockService $stockService) {}

    public function createProduct(NewProductData $data)
    {

        $company = userCompany();

        $category = ProductCategory::where('id', $data->category_id)->exists();
        throw_if(!$category, NotFound::class, 'Category not found');

        $quantityUnit = ProductQuantityUnit::where('id', $data->quantity_unit_id)->exists();
        throw_if(!$quantityUnit, NotFound::class, 'Quantity unit not found');

        $packingUnit = ProductPackingUnit::where('id', $data->packaging_unit_id)->exists();
        throw_if(!$packingUnit, NotFound::class, 'Packing unit not found');

        $country = Country::where('id', $data->country_id)->exists();
        throw_if(!$country, NotFound::class, 'Country not found');

        $tax = Tax::where('id', $data->tax_id)->exists();
        throw_if(!$tax, NotFound::class, 'Tax not found');

        $class = ProductClass::where('id', $data->class_id)->exists();
        throw_if(!$class, NotFound::class, 'Class not found');

        $type = ProductType::where('id', $data->type_id)->exists();
        throw_if(!$type, NotFound::class, 'Type not found');

        DB::beginTransaction();

        $countryCode =  Country::where('id', $data->country_id)->first()->code;
        $productClass = ProductClass::where('id', $data->class_id)->first()->code;
        $packagingUnit = ProductPackingUnit::where('id', $data->packaging_unit_id)->first()->code;
        $quantityUnits = ProductQuantityUnit::where('id', $data->quantity_unit_id)->first()->code;
        $taxName = Tax::where('id', $data->tax_id)->first()->name;
        $productType = ProductType::where('id', $data->type_id)->first()->id;


        $lastProductId = Product::max('id') ?? 0;
        $sequence = str_pad($lastProductId + 1, 7, '0', STR_PAD_LEFT);
        //$itemCodeSuffix = str_pad($lastProductId + 1, 7, '0', STR_PAD_LEFT);
        $itemCode = $countryCode . $productType . $packagingUnit . $quantityUnits . $sequence;
        // dd($itemCode);


        $slug = Str::slug($data->name);

        $product = Product::create([
            'company_id' => $company->id,
            'name' => $data->name,
            'quantity_unit_id' => $data->quantity_unit_id,
            'packaging_unit_id' => $data->packaging_unit_id,
            'category_id' => $data->category_id,
            'type_id' => $data->type_id,
            'tax_id' => $data->tax_id,
            'slug' => $slug,
            'item_code' => $itemCode,
            'country_id' => $data->country_id,
            'class_id' => $data->class_id,
            'discount' => $data->discount,
        ]);

        ProductDetail::create([
            'product_id' => $product->id,
            'current_stock' => $data->opening_stock,
            'purchase_price' => $data->purchase_price,
            'sales_price' => $data->sale_price,
            'stock_quantity_alert' => $data->stock_quantity_alert,
            'status' => $data->opening_stock > 0 ? 'in_stock' : 'out_of_stock',
            'opening_stock' => $data->opening_stock,
        ]);

        $payload = [
            "tin" => (string) $company->tin,
            "bhfId" => $company->branch,
            "itemCd" => $product->item_code,
            "itemClsCd" => $productClass,
            "itemTyCd" => $productType,
            "itemNm" => $product->name,
            "orgnNatCd" => $countryCode,
            "pkgUnitCd" => $packagingUnit,
            "qtyUnitCd" => $quantityUnits,
            "taxTyCd" => $taxName,
            "dftPrc" => $data->purchase_price,
            "useYn" => 'Y',
            "isrcAplcbYn" => "N",
            "regrNm" => "Admin",
            "regrId" => "Admin",
            "modrNm" => "Admin",
            "modrId" => "Admin"
        ];

        $sendPayload = SaveItemData::from($payload);

        $response = Http::post(saveItemUrl(), $sendPayload->toArray());

        if ($response->json('resultCd') == '000') {

            $this->stockService->stockMasterSave(StockMasterSaveData::from([
                'tin' => $company->tin,
                'bhfId' => $company->branch,
                'itemCd' => $product->item_code,
                'rsdQty' => $data->opening_stock,
                'regrId' => 'Admin',
                'regrNm' => 'Admin',
                'modrNm' => 'Admin',
                'modrId' => 'Admin'
            ]));

            DB::commit();
        } else {
            DB::rollBack();

            return ['success' => false,  'message' => $response->json('resultMsg')];
        }

        Log::info($response->json());
        return ['success' => true, 'message' => 'Product created successfully'];
    }

    public function updateProduct(NewProductData $data, int $id): void
    {
        $company = userCompany();

        $product = Product::where('id', $id)->where('company_id', $company->id)->first();
        throw_if(is_null($product), NotFound::class, 'Product not found');

        $category = ProductCategory::where('id', $data->category_id)->exists();
        throw_if(!$category, NotFound::class, 'Category not found');

        $quantityUnit = ProductQuantityUnit::where('id', $data->quantity_unit_id)->exists();
        throw_if(!$quantityUnit, NotFound::class, 'Quantity unit not found');

        $packingUnit = ProductPackingUnit::where('id', $data->packaging_unit_id)->exists();
        throw_if(!$packingUnit, NotFound::class, 'Packing unit not found');

        $country = Country::where('id', $data->country_id)->exists();
        throw_if(!$country, NotFound::class, 'Country not found');

        $tax = Tax::where('id', $data->tax_id)->exists();
        throw_if(!$tax, NotFound::class, 'Tax not found');

        $class = ProductClass::where('id', $data->class_id)->exists();
        throw_if(!$class, NotFound::class, 'Class not found');

        $type = ProductType::where('id', $data->type_id)->exists();
        throw_if(!$type, NotFound::class, 'Type not found');

        Log::info($data);

        $product->name = $data->name;
        $product->quantity_unit_id = $data->quantity_unit_id;
        $product->packaging_unit_id = $data->packaging_unit_id;
        $product->category_id = $data->category_id;
        $product->type_id = $data->type_id;
        $product->tax_id = $data->tax_id;
        $product->country_id = $data->country_id;
        $product->class_id = $data->class_id;
        $product->discount = $data->discount;
        $product->save();

        ProductDetail::updateOrCreate([
            'product_id' => $product->id,
        ], [
            'purchase_price' => $data->purchase_price,
            'sales_price' => $data->sale_price,
            'stock_quantity_alert' => $data->stock_quantity_alert,
            'status' => $data->current_stock > 0 ? 'in_stock' : 'out_of_stock',
            'opening_stock' => $data->opening_stock,
            'current_stock' => $data->current_stock,
        ]);

        $this->stockService->stockMasterSave(StockMasterSaveData::from([
            'tin' => $company->tin,
            'bhfId' => $company->branch,
            'itemCd' => $product->item_code,
            'rsdQty' => $data->current_stock,
            'regrId' => 'Admin',
            'regrNm' => 'Admin',
            'modrNm' => 'Admin',
            'modrId' => 'Admin'
        ]));
    }

    public function getProductFromRRA($company)
    {
        $response = Http::post(getItemsUrl(), [
            'tin' => $company->tin,
            'bhfId' => $company->branch,
            'lastReqDt' => '20180520000000'
        ]);

        throw_if(!$response->successful(), NotFound::class, 'Failed to fetch products.');
        $responseData = $response->json();

        Log::info($responseData);

        if ($responseData['resultCd'] !== '000' || $responseData['resultMsg'] !== 'It is succeeded') {
            return ['message' => 'Something went wrong. Please try again later.'];
        }

        $products = collect($responseData['data']['itemList'] ?? [])
            ->map(fn($item) => [
                'name' => $item['itemNm'],
                'itemCd' => $item['itemCd'],
                'itemClsCd' => $item['itemClsCd'],
                'itemTyCd' => $item['itemTyCd'],
                'itemNm' => $item['itemNm'],
                'orgnNatCd' => $item['orgnNatCd'],
                'pkgUnitCd' => $item['pkgUnitCd'],
                'qtyUnitCd' => $item['qtyUnitCd'],
                'taxTyCd' => $item['taxTyCd'],
                'regBhfId' => $item['regBhfId'],
                'dftPrc' => $item['dftPrc'],
                'sftyQty' => $item['sftyQty'],
                'useYn' => $item['useYn'],
            ])
            ->toArray();

        $this->saveProductsToLocalDB($products);
    }

    public function getAllProduct(int $companyId): array
    {

        $product = Product::where('company_id', $companyId)
            ->with([
                'details',
                'packagingUnit:id,code,description',
                'quantityUnit:id,name,code',
                'category:id,name',
                'type:id,name',
                'tax:id,name,rate'
            ])->get();
        return $product->toArray();
    }

    public function newProductData(): array
    {
        return [
            'packing_units' => ProductPackingUnit::get(['id', 'code', 'description'])->toArray(),
            'categories' => ProductCategory::get(['id', 'name'])->toArray(),
            'types' => ProductType::get(['id', 'name'])->toArray(),
            'taxes' => Tax::get(['id', 'name', 'rate'])->toArray(),
            'countries' => Country::get(['id', 'code', 'name'])->toArray(),
            'classes' => ProductClass::get(['id', 'name', 'level', 'code', 'tax_type'])->toArray(),
            'quantity_units' => ProductQuantityUnit::get(['id', 'name', 'code'])->toArray(),
        ];
    }

    public function getProductById(int $id)
    {
        return Product::where('id', $id)
            ->with([
                'details',
                'packagingUnit:id,code,description',
                'quantityUnit:id,name,code',
                'category:id,name',
                'type:id,name',
                'tax:id,name,rate'
            ])->first();
    }

    public function getProductClassCode()
    {
        $company = userCompany();
        $data = [
            'tin' => $company->tin,
            'bhfId' => $company->branch,
            'lastReqDt' => "20180523000000",
        ];

        $response = Http::timeout(60)->post(getItemsClassUrl(), $data);

        Log::info($response->json());

        if ($response->json('resultCd') == '000') {
            $itemClassList = $response->json('data.itemClsList', []);

            foreach ($itemClassList as $item) {
                ProductClass::updateOrCreate(
                    ['code' => $item['itemClsCd']],
                    [
                        'name' => $item['itemClsNm'],
                        'level' => $item['itemClsLvl'],
                        'tax_type' => $item['taxTyCd'],
                        'useYn' => $item['useYn'],
                        'mjrTgYn' => $item['mjrTgYn'],
                    ]
                );
            }
        }
    }

    public function getPosProduct(int $companyId)
    {
        $products =  Product::where('company_id', $companyId)
            ->with([
                'details',
                'packagingUnit:id,code,description',
                'quantityUnit:id,name,code',
                'category:id,name',
                'type:id,name',
                'tax:id,name,rate',
            ])
            ->whereHas('details', function ($query) {
                $query->where('current_stock', '>', 0);
            })
            ->get();

        $products->map(function ($product) {

            $product->current_stock = $product->details->current_stock;
            $product->purchase_price = $product->details->purchase_price;
            $product->sales_price = $product->details->sales_price;
            $product->stock_quantity_alert = $product->details->stock_quantity_alert;
            $product->status = $product->details->status;

            return $product;
        });

        return $products;
    }

    private function saveProductsToLocalDB($products)
    {
        foreach ($products as $productData) {

            $product = Product::firstWhere('item_code', $productData['itemCd']);

            if (!$product) {

                try {

                    DB::beginTransaction();

                    $tax = Tax::where('name', $productData['taxTyCd'])->first();
                    $country = Country::where('code', $productData['orgnNatCd'])->first();
                    $type = ProductType::where('id', $productData['itemTyCd'])->first();
                    $packingUnit = ProductPackingUnit::where('code', $productData['pkgUnitCd'])->first();
                    $quantityUnit = ProductQuantityUnit::where('code', $productData['qtyUnitCd'])->first();
                    $class = ProductClass::where('code', $productData['itemClsCd'])->first();

                    if (is_null($class)) {
                        $class = ProductClass::where('code', '4320155400')->first();
                    }

                    $product = Product::create([
                        'item_code' => $productData['itemCd'],
                        'name' => $productData['itemNm'],
                        'country_id' => $country->id,
                        'type_id' => $type->id,
                        'packaging_unit_id' => $packingUnit->id,
                        'quantity_unit_id' => $quantityUnit->id,
                        'category_id' => ProductCategory::first()->id,
                        'tax_id' => $tax->id,
                        'class_id' => $class->id,
                        'slug' => strtolower(str_replace(' ', '-', $productData['itemNm'])),
                        'company_id' => userCompany()->id,
                    ]);

                    ProductDetail::create([
                        'product_id' => $product->id,
                        'purchase_price' => $productData['dftPrc'],
                        'stock_quantity_alert' => $productData['sftyQty'],
                        'status' => $productData['useYn'] == 'Y' ? 'in_stock' : 'out_of_stock',
                        'sales_price' => $productData['dftPrc'],
                    ]);

                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error($e->getMessage());

                    dd($e->getMessage());
                }
            }
        }
    }
}

    */
}
