<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('Product', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->nullable()->references('id')->on('CompanyBranch')->onDelete('restrict');
            $table->foreignId('branch_product_category_id')->nullable()->references('id')->on('BranchProductCategory')->onDelete('restrict');
            $table->foreignId('category_id')->nullable()->references('id')->on('ProductCategory')->onDelete('restrict');
            $table->foreignId('quantity_unit_id')->references('id')->on('ProductQuantityUnit')->onDelete('restrict');
            $table->foreignId('packaging_unit_id')->references('id')->on('ProductPackingUnit')->onDelete('restrict');
            $table->foreignId('tax_id')->references('id')->on('Tax')->onDelete('restrict');
            $table->foreignId('type_id')->nullable()->references('id')->on('ProductType')->onDelete('restrict');
            $table->foreignId('country_id')->nullable()->references('id')->on('Country')->onDelete('restrict');
            $table->foreignId('class_id')->references('id')->on('ProductClass')->onDelete('restrict');
            $table->bigInteger('conversionFactor')->default(0);
            $table->boolean('soldInSubUnit')->default(false);
            $table->string('name', 1000);
            $table->string('slug', 1000)->nullable();
            $table->string('itemCode')->nullable();
            $table->string('image')->nullable()->default(null);
            $table->string('price')->nullable();
            $table->text('description')->nullable();
            $table->text('description_two')->nullable();
            $table->text('description_three')->nullable(); // for trackign of product has stock or not 
            $table->bigInteger("created_at")->nullable();
            $table->bigInteger("updated_at")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('Product');
    }
};
