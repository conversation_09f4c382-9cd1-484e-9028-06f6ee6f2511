<?php

namespace App\Http\Controllers\Api;

use App\Data\CompanyBranchUpdateData;
use App\Data\NewCompanyBranchData;
use App\Http\Requests\CompanyBranchUpdateRequest;
use App\Http\Requests\NewCompanyBranchRequest;
use App\Services\CompanyBranchService;
use App\Services\CompanyService;
use Illuminate\Support\Facades\Auth;

use OpenApi\Attributes as OA;
use App\Http\Controllers\Controller;

class CompanyBranchController extends Controller
{

    protected $user;
    public function __construct(protected CompanyBranchService $service, protected CompanyService $companyService)
    {
        $this->user = Auth::user();
    }

    #[OA\Get(
        path: "/api/v1/company/branch",
        description: "Get company branches",
        security: [["authentication" => []]],
        summary: "company branches",
        tags: ["company.branch"]
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\QueryParameter(
        name: "perPage",
        description: "Number of items per page",
        required: false,
        example: 100
    )]
    #[OA\QueryParameter(
        name: "page",
        description: "Page number",
        required: false,
        example: 1
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                'data' => [
                    [
                        'id' => 1,
                        'name' => 'Kicukiro Branch',
                        'phone' => '250788888888',
                        'email' => '<EMAIL>',
                        'address' => 'KK 369 ST, Kicukiro',
                        'branchCode' => '23',
                    ],
                    'currentPage' => 1,
                    'lastPage' => 1,
                    'itemsPerPage' => 100,
                    'pageItems' => 1,
                    'total' => 1,
                    'timestamp' => '2021-09-09, 12:00:00'
                ]

            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function index()
    {

        $company = companyData();
        $perPage = \request()->query('perPage', 100);
        $page = \request()->query('page', 1);
        $response = $this->service->getBranches($company->id, perPage: $perPage, page: $page);
        return $this->jsonResponseWithPagination($response);
    }

    #[OA\Post(
        path: "/api/v1/company/branch",
        description: "Create company branch",
        security: [["authentication" => []]],
        summary: "company branch store",
        tags: ["company.branch"]
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\RequestBody(
        description: "Company Branches",
        required: true,
        content: new OA\JsonContent(
            example: [
                "name" => 'New Branch',
                "phone" => "250788888899",
                "email" => "<EMAIL>",
                "address" => "sample Address",
                "branchCategoryID" => 1,
            ]
        )
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                "message" => "Branch created successfully",
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function store(NewCompanyBranchRequest $request)
    {

        $company = companyData();
        $data = NewCompanyBranchData::from($request->validated());
        $this->service->storeBranch($data, $company);

        return response()->json(['message' => 'Branch created successfully']);
    }

    #[OA\Put(
        path: "/api/v1/company/branch",
        description: "update company branch",
        security: [["authentication" => []]],
        summary: "company branch update",
        tags: ["company.branch"]
    )]
    #[OA\HeaderParameter(
        name: "companyId",
        required: true,
        example: 1
    )]
    #[OA\HeaderParameter(
        name: "branchId",
        required: true,
        example: 1
    )]
    #[OA\RequestBody(
        description: "Branch Details",
        required: true,
        content: new OA\JsonContent(
            example: [
                "name"  => "hirwa",
                "phone" => "250789121212",
                "email" => "<EMAIL>",
                "address" => "jsdk kjsdksd",                
            ]
        )
    )]
    #[OA\Response(
        response: "200",
        description: "Successful operation",
        content: new OA\JsonContent(
            example: [
                "message" => "Branch created successfully",
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: "Unauthenticated",
        content: new OA\JsonContent(
            example: [
                "message" => "Unauthenticated.",
            ]
        )
    )]
    #[OA\Response(
        response: 500,
        description: "Server error, something went wrong on the server",
        content: new OA\JsonContent(
            example: [
                "message" => "Server error.",
            ]
        )
    )]
    public function update(CompanyBranchUpdateRequest $request)
    {

        $company = companyData();
        $defaultBranch = branchData();
        $data = CompanyBranchUpdateData::from($request->validated());
        $this->service->updateBranch($data,$defaultBranch->id, $company);

        return response()->json(['message' => 'Branch updated successfully']);
    }
}
