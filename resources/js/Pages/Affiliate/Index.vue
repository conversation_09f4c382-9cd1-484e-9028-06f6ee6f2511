<script setup>
import ListOption from '@/Components/ListOption.vue';
import PreviewSection from '@/Components/PreviewSection.vue';
import ProfileSection from '@/Components/ProfileSection.vue';
import TableSection from '@/Components/TableSection.vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref } from 'vue';

import "@fortawesome/fontawesome-free/css/all.css";
import "@fortawesome/fontawesome-free/js/all.js";

const props = defineProps({
    affiliateCode: String,

});

const tableHeaders = ["Name", "Email", "Phone", "Address", "Status"];

const tableRows = [

];
const showNotification = ref(false);

const copyAffiliateCode = () => {
    navigator.clipboard.writeText(props.affiliateCode)
        .then(() => {
            showNotification.value = true;
            setTimeout(() => {
                showNotification.value = false;
            }, 2000);
        })
        .catch((err) => {
            console.error("Failed to copy affiliate code: ", err);
        });
};
</script>

<template>
    <AppLayout title="Affilates">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Affilates
            </h2>
        </template>

        <PreviewSection>
            <template #content>
                <ProfileSection :image="$page.props.auth.user.profile_photo_url" :title="$page.props.auth.user.name"
                    :description="$page.props.auth.user.email" />

                <div class="mt-4">
                    <p class="font-semibold">
                        Affiliate code:
                        <span class="text-gray-700 font-normal">{{ affiliateCode }}</span>
                        <button @click="copyAffiliateCode"
                            class="ml-2 px-3 py-1 bg-zata-primary-light text-white text-sm rounded hover:bg-zata-primary-dark focus:outline-none">
                            Copy
                            <i class="fa-solid fa-copy px-2"></i>
                        </button>
                    </p>


                    <div v-if="showNotification" class="mt-2 text-sm text-green-600">
                        code copied!
                    </div>
                </div>
            </template>

            <template #deatils>
                <TableSection :headers="tableHeaders" :rows="tableRows" title="Affilates"
                    notFoundMessage="No affilates found." :showSearch="false" :showPagination="false" />
            </template>
        </PreviewSection>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>
