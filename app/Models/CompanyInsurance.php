<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CompanyInsurance extends Model
{
    /** @use HasFactory<\Database\Factories\CompanyInsuranceFactory> */
    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'CompanyInsurance';

    protected $fillable = [
        'company_id',
        'insurance_id',
        'code'
    ];

    public function details(): BelongsTo
    {
        return $this->belongsTo(Insurance::class, 'insurance_id');
    }
}
