<script setup>
import { <PERSON> } from '@inertiajs/vue3';

const props = defineProps({
    href: String,
    active: Boolean,
});

</script>

<template>
    <Link :href="href"  class="items-center px-4 py-2 bg-zata-primary-light border border-transparent rounded-md font-medium  text-white tracking-widest hover:bg-zata-primary-dark focus:bg-zata-primary-dark active:bg-zata-primary-dark focus:outline-none disabled:opacity-50 transition ease-in-out duration-150 ">
        <slot />
    </Link>
</template>
