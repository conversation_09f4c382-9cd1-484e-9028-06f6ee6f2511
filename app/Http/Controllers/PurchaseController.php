<?php

namespace App\Http\Controllers;

use App\Data\PurchaseUpdateData;
use App\Http\Requests\UpdatePurchasesRequest;
use App\Services\PurchaseService;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
use App\Services\ProductService;
use Illuminate\Support\Facades\Log;
use App\Enums\AccountTypeEnums;

class PurchaseController extends Controller
{
    protected $user;

    public function __construct(protected PurchaseService $service,
    protected ProductService $productService)
    {
        $this->user = Auth::user();
    }
    
    public function index()
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $response = $this->pagination($this->service->getPurchase($branch, perPage: $perPage, page: $page));

        return Inertia::render('Purchase/Index', [
            'Purchases' => $response['data'],
            'currentPage' => $response['currentPage'],
            'lastPage' => $response['lastPage'],
            'itemsPerPage' => $response['itemsPerPage'],
            'pageItems' => $response['pageItems'],
            'total' => $response['total'],
        ]);
    }

    public function checkPurchase()
    {
        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        // TODO, use last request date
        $branch = branchData();
        $this->service->checkPurchase('**************', $branch);

        return redirect()->route('purchase.index')->banner('Purchase Synced');
    }

    public function showPurchaseById(int $purchaseId)
    {
        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $purchase = $this->service->getPurchaseById($branch, $purchaseId);
        $productData  = $this->pagination($this->productService->getProductsForPurchase($branch->id, perPage: 1000, page: 1, searchQuery: null));
        return Inertia::render('Purchase/Show', [
            'Purchase' => $purchase,
            'Products' => $productData['data']
        ]);
    }

    public function update(UpdatePurchasesRequest $request)
    {

        zataAccess([AccountTypeEnums::WAREHOUSE_MANAGER->value]);
        $branch = branchData();
        $data = PurchaseUpdateData::from($request->validated());
        $this->service->updatePurchase($data,  $branch, $this->user->id);

        return redirect()->route('purchase.index')->banner('Purchase update completed');
    }
}
