<!DOCTYPE html>
<html lang="en">
<head>
    <title>Zata Point Invoice</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <style>
        @media print {
            body {
                margin: 0;
                padding: 0;
                font-size: 12px;
            }
            .print-only {
                display: block;
            }
            .no-print {
                display: none;
            }
            .container {
                width: 100%;
                max-width: 1000px;
                margin: 0 auto;
            }
            table {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <section id="print-section" class="print-only flex justify-center p-4 bg-white text-xs">
        <div class="container bg-white rounded mx-auto">
            <div class="border border-gray-400 m-5 p-5 rounded">
                <!-- Logo -->
                <div class="flex justify-center mb-4">
                    <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('zata_logo.png'))) }}"
                         alt="Zata Logo" class="w-44 mx-auto">
                </div>

                <!-- Centered Heading -->
             <p class="mt-2 text-center">Purchase Note</p>

                <!-- Header Section -->
                <section class="flex justify-between mb-4">
                    <div class="flex flex-col">
                        <p class="font-bold text-xs">{{ $company->name }}</p>
                        <p><span class="font-bold">Address:</span> {{ $company->address }}</p>
                        <p><span class="font-bold">Tel:</span> {{ $company->phone }}</p>
                        <p class="hidden"><span class="font-bold">Email:</span> {{ $company->email }}</p>
                        <p><span class="font-bold">TIN:</span> {{ $company->tin }}</p>
                       
                    </div>
                </section>
    <div class="text-center mb-4">
                    <p class="font-semibold">{{ $branch->topMessage }}</p>
                </div>
                <!-- Reference -->
                <div class="mb-4">
                    <p>
                        <span class="font-bold">Reference:</span>
                        Served by {{ $transaction->user->name }}
                    </p>
                </div>

                <!-- Client and Invoice Info -->
                <div class="flex justify-between mb-6">
                    <div>
                        <p class="font-bold mb-2">TO</p>
                        <div class="border border-gray-500 p-3 w-80">
                            <p>TIN: {{ $transaction->clientTin }}</p>
                            <p>Name: {{ $transaction->clientName }}</p>
                            <p class="hidden">Phone: {{ $transaction->clientPhoneNumber }}</p>
                        </div>
                    </div>
                    <div>
                        <p class="font-bold mb-2 invisible">Placeholder</p>
                        <div class="border border-gray-500 p-3 w-80">
                            <p>Purchase No: {{ $transaction->id }}</p>
                            <p>Date: {{ $transaction->salesDate }}</p>
                        </div>
                    </div>
                </div>

                <!-- Items Table -->
                <div class="w-full mb-6">
                    <table class="min-w-full border border-gray-300">
                        <thead class="bg-gray-200">
                            <tr>
                                <th class="text-left px-2 py-1 border border-gray-300 w-1/6">Item Code</th>
                                <th class="text-left px-2 py-1 border border-gray-300 w-2/6">Item Desc</th>
                                <th class="text-left px-2 py-1 border border-gray-300 w-1/6">Qty</th>
                                <th class="text-left px-2 py-1 border border-gray-300 w-1/6">Tax</th>
                                <th class="text-left px-2 py-1 border border-gray-300 w-1/6">Unit Price</th>
                                <th class="text-left px-2 py-1 border border-gray-300 w-1/6">Total Price</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($transaction->items as $item)
                                <tr>
                                    <td class="px-2 py-1 border border-gray-300">{{ $item->productCode }}</td>
                                    <td class="px-2 py-1 border border-gray-300 truncate max-w-xs">{{ $item->productName }}</td>
                                    <td class="px-2 py-1 border border-gray-300">{{ $item->quantity }}</td>
                                    <td class="px-2 py-1 border border-gray-300">{{ $item->taxName }}</td>
                                    <td class="px-2 py-1 border border-gray-300">{{ $item->price }}</td>
                                    <td class="px-2 py-1 border border-gray-300">{{ $item->totalAmount }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Centered Version Info -->
                <div class="text-center mt-12">
                    <p>Version V1.0.0 Powered by RRA_VSDC</p>
                </div>

                <!-- Bottom Message -->
                <div class="text-center mt-3 max-w-lg mx-auto">
                    <p class="font-semibold">{{ $branch->bottomMessage }}</p>
                </div>
            </div>
        </div>
    </section>
</body>
</html>