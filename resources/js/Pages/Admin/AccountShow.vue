<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from '@inertiajs/vue3';
import { watch } from 'vue';
import { addMonths, format } from 'date-fns';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const props = defineProps({
    Account: Object,
});

const form = useForm({
    name: props.Account.name,
    email: props.Account.email,
    phone: props.Account.phone,
    subscriptionType: props.Account.subscription?.subscriptionType || '',
    price: props.Account.subscription?.price || '',
    isActive: props.Account.subscription?.isActive ? '1' : '0',
    startDate: props.Account.subscription?.startDate || '',
    endDate: props.Account.subscription?.endDate || '',
});

watch(
    () => form.startDate,
    (newStartDate) => {
        if (newStartDate) {
            const start = new Date(newStartDate);
            const end = addMonths(start, 1);
            form.endDate = format(end, 'yyyy-MM-dd');
        } else {
            form.endDate = '';
        }
    }
);

function createNewCompany() {
    form.put(route('account.update', props.Account.id));
}
</script>

<template>
    <AppLayout title="Account">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">Account</h2>
        </template>

        <div class="py-12">
            <Card class="max-w-3xl mx-auto">
                <CardHeader>
                    <CardTitle class="text-lg">Account Information</CardTitle>
                </CardHeader>
                <CardContent>
                    <form @submit.prevent="createNewCompany" class="space-y-6">
                        <div v-if="$page.props.errors && Object.keys($page.props.errors).length > 0" class="space-y-2">
                            <p class="font-bold text-red-600">Please fix the following errors:</p>
                            <ul class="text-red-600 text-sm">
                                <li v-for="(error, key) in $page.props.errors" :key="key">{{ error }}</li>
                            </ul>
                        </div>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <div class="space-y-2">
                                <Label for="name">Name</Label>
                                <Input id="name" v-model="form.name" type="text" />
                                <p v-if="form.errors.name" class="text-red-500 text-sm">
                                    {{ form.errors.name }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="email">Email</Label>
                                <Input id="email" v-model="form.email" type="email" />
                                <p v-if="form.errors.email" class="text-red-500 text-sm">
                                    {{ form.errors.email }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="phone">Phone</Label>
                                <Input id="phone" v-model="form.phone" type="text" />
                                <p v-if="form.errors.phone" class="text-red-500 text-sm">
                                    {{ form.errors.phone }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="subscriptionType">Subscription Type</Label>
                                <Select v-model="form.subscriptionType">
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="Basic">Basic</SelectItem>
                                        <SelectItem value="Premium">Premium</SelectItem>
                                        <SelectItem value="Enterprise">Enterprise</SelectItem>
                                    </SelectContent>
                                </Select>
                                <p v-if="form.errors.subscriptionType" class="text-red-500 text-sm">
                                    {{ form.errors.subscriptionType }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="price">Price</Label>
                                <Input id="price" v-model="form.price" type="text" />
                                <p v-if="form.errors.price" class="text-red-500 text-sm">
                                    {{ form.errors.price }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="isActive">Status</Label>
                                <Select v-model="form.isActive">
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="1">Active</SelectItem>
                                        <SelectItem value="0">Inactive</SelectItem>
                                    </SelectContent>
                                </Select>
                                <p v-if="form.errors.isActive" class="text-red-500 text-sm">
                                    {{ form.errors.isActive }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="startDate">Start Date</Label>
                                <Input id="startDate" v-model="form.startDate" type="date" />
                                <p v-if="form.errors.startDate" class="text-red-500 text-sm">
                                    {{ form.errors.startDate }}
                                </p>
                            </div>

                            <div class="space-y-2">
                                <Label for="endDate">End Date</Label>
                                <Input id="endDate" v-model="form.endDate" type="date" readonly />
                                <p v-if="form.errors.endDate" class="text-red-500 text-sm">
                                    {{ form.errors.endDate }}
                                </p>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <Button 
                                type="submit" 
                                :disabled="form.processing"
                                :class="{ 'opacity-25': form.processing }"
                            >
                                Save
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>

        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>