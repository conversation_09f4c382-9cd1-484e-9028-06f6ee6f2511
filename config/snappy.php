<?php

return [

    'pdf' => [
    'enabled' => true,
    'binary' => env('WKHTMLTOPDF_PATH',  '/usr/bin/wkhtmltopdf'),
    'timeout' => 120,
    'options' => [
        'lowquality' => true, // Faster rendering at slightly lower quality
        'disable-smart-shrinking' => true, // Speeds up rendering
        'print-media-type' => true,
        'margin-top' => 0,
        'margin-bottom' => 0,
        'margin-left' => 0,
        'margin-right' => 0,
    ],
],

    'image' => [
        'enabled' => true,
        'binary' => env('WKHTML_IMG_BINARY', '/usr/bin/wkhtmltoimage'),
        'timeout' => 120, // Consistent with PDF
        'options' => [],
        'env' => [],
    ],
];