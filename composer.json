{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "barryvdh/laravel-dompdf": "^3.0", "barryvdh/laravel-snappy": "^1.0", "darkaonline/l5-swagger": "^8.6", "inertiajs/inertia-laravel": "^2.0", "laravel/framework": "^11.31", "laravel/jetstream": "^5.3", "laravel/nightwatch": "^1.11", "laravel/octane": "^2.9", "laravel/sanctum": "^4.0", "laravel/scout": "^10.11", "laravel/socialite": "^5.18", "laravel/telescope": "^5.9", "laravel/tinker": "^2.9", "quarksgroup/paypack-php": "^0.1.20", "simplesoftwareio/simple-qrcode": "~4", "spatie/laravel-backup": "^9.3", "spatie/laravel-data": "^4.11", "spiral/roadrunner-cli": "^2.7", "spiral/roadrunner-http": "^3.5", "tightenco/ziggy": "^2.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "pestphp/pest": "^3.6", "pestphp/pest-plugin-drift": "^3.0", "pestphp/pest-plugin-type-coverage": "^3.2"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/Helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}