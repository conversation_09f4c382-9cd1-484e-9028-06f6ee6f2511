APP_NAME="Zata EBM Main"
APP_ENV=lacal
APP_KEY=base64:UA4Y34fqMsAr8czoda8kvhvzkKR33kZQ7DE3AggujXo=
APP_DEBUG=true
APP_URL=http://localhost
APP_INSTACE_NAME=zata_ebm_main
APP_PORT=4000
OCTANE_SERVER=roadrunner
APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US
SCOUT_DRIVER=database

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

#Sail Localy
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=zata_ebm_main
DB_USERNAME=sail
DB_PASSWORD=password
FORWARD_DB_PORT=33460


SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

WKHTMLTOPDF_PATH=/usr/bin/wkhtmltopdf

PAYPACK_CLIENT_ID=
PAYPACK_CLIENT_SECRET=
PAYPACK_WEBHOOK_SECRET=your-webhook-secret-here

LOG_SLACK_WEBHOOK_URL=
NIGHTWATCH_TOKEN=
NIGHTWATCH_REQUEST_SAMPLE_RATE=0.1

DPO_BASE_URL="https://secure.3gdirectpay.com/API/v6/"
DPO_COMPANY_TOKEN="1000000000000000000000000000000000000000000000000000000000000000"
DPO_DEFAULT_CURRENCY="USD"
DPO_DEFAULT_PTL=5
