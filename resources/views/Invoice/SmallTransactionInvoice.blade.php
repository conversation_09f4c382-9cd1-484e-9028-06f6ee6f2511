<!DOCTYPE html>
<html>
<head>
    <title>Zata Point Invoice - 80mm Standard</title>
    <style>
        @media print {
            body { margin: 0; padding: 0; }
            .no-print { display: none; }
            @page { margin: 0; size: 80mm auto; }
        }
        
        body {
            font-family: 'Courier New', 'DejaVu Sans Mono', monospace;
            font-size: 12px;
            line-height: 1.3;
            margin: 0;
            padding: 0;
            background: white;
            color: #000;
        }
        
        .receipt-container {
            width: 80mm;
            max-width: 80mm;
            margin: 0 auto;
            padding: 4mm;
            box-sizing: border-box;
            background: white;
        }
        
        /* Header Section */
        .header-section {
            text-align: center;
            margin-bottom: 10px;
            border-bottom: 2px dashed #000;
            padding-bottom: 8px;
        }
        
        .logo-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .logo-container img {
            width: 20mm;
            height: auto;
            max-height: 15mm;
        }
        
        .company-info {
            text-align: center;
        }
        
        .company-name {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 4px;
            text-transform: uppercase;
        }
        
        .company-details {
            font-size: 11px;
            margin-bottom: 2px;
            line-height: 1.2;
        }
        
        .company-tin {
            font-weight: bold;
            font-size: 12px;
            margin-top: 4px;
        }
        
        /* Transaction Type */
        .transaction-type {
            text-align: center;
            font-weight: bold;
            font-size: 14px;
            margin: 8px 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .refund-notice {
            background: white;
            border: 2px solid #000;
            padding: 6px;
            text-align: center;
            font-weight: bold;
            font-size: 10px;
            margin: 8px 0;
        }
        
        /* Messages */
        .top-message {
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            margin: 10px 0;
            padding: 6px 0;
            border-top: 1px dashed #000;
            border-bottom: 1px dashed #000;
        }
        
        /* Reference and Staff */
        .reference-section {
            margin: 8px 0;
            font-size: 11px;
        }
        
        .reference-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        
        /* Customer Information */
        .customer-section {
            border: 1px solid #000;
            padding: 6px;
            margin: 10px 0;
            background: white;
        }
        
        .customer-title {
            font-weight: bold;
            font-size: 13px;
            text-align: center;
            margin-bottom: 6px;
            text-transform: uppercase;
        }
        
        .customer-line {
            display: flex;
            margin-bottom: 3px;
            font-size: 11px;
        }
        
        .customer-label {
            font-weight: bold;
            width: 45px;
            flex-shrink: 0;
        }
        
        .customer-value {
            flex-grow: 1;
        }
        
        /* Items Section */
        .items-section {
            margin: 12px 0;
        }
        
        .item {
            margin: 8px 0;
            padding: 4px 0;
            border-bottom: 1px dotted #666;
        }
        
        .item:last-child {
            border-bottom: 2px solid #000;
        }
        
        .item-name {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 4px;
            text-transform: capitalize;
        }
        
        .item-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            margin-bottom: 2px;
        }
        
        .item-price-qty {
            color: #666;
        }
        
        .item-total {
            font-weight: bold;
        }
        
        .item-tax {
            font-size: 10px;
            color: #666;
            margin-left: 4px;
        }
        
        .discount-line {
            display: flex;
            justify-content: space-between;
            font-size: 10px;
            color: #666;
            font-style: italic;
            margin-top: 2px;
        }
        
        /* Notice Sections */
        .not-official-notice {
            text-align: center;
            font-weight: bold;
            font-size: 12px;
            background: white;
            border: 2px solid #000;
            padding: 8px;
            margin: 10px 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .copy-notice {
            text-align: center;
            font-weight: bold;
            font-size: 14px;
            background: white;
            border: 2px dashed #000;
            padding: 6px;
            margin: 8px 0;
            text-transform: uppercase;
        }
        
        .mode-notice {
            text-align: center;
            font-weight: bold;
            font-size: 14px;
            background: white;
            border: 2px solid #000;
            padding: 8px;
            margin: 10px 0;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        /* Totals Section */
        .totals-section {
            border: 2px solid #000;
            padding: 8px;
            margin: 12px 0;
            background: white;
        }
        
        .total-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            font-size: 11px;
        }
        
        .total-line.main-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
            padding: 6px 0;
            margin: 8px 0 4px 0;
        }
        
        .total-label {
            text-transform: uppercase;
        }
        
        .total-value {
            font-weight: bold;
        }
        
        /* Payment Section */
        .payment-section {
            border: 1px solid #000;
            padding: 6px;
            margin: 10px 0;
        }
        
        .payment-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            font-size: 12px;
        }
        
        .payment-method {
            font-weight: bold;
            text-transform: uppercase;
        }
        
        /* Insurance Section */
        .insurance-section {
            border: 2px solid #000;
            background: white;
            padding: 8px;
            margin: 10px 0;
        }
        
        .insurance-title {
            font-weight: bold;
            font-size: 12px;
            text-align: center;
            margin-bottom: 6px;
            color: #000;
            text-transform: uppercase;
        }
        
        .insurance-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 11px;
        }
        
        /* SDC Information Section */
        .sdc-section {
            border: 2px solid #000;
            padding: 6px;
            margin: 12px 0;
            background: white;
        }
        
        .sdc-title {
            text-align: center;
            font-weight: bold;
            font-size: 13px;
            margin-bottom: 8px;
            text-transform: uppercase;
            text-decoration: underline;
        }
        
        .sdc-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            font-size: 10px;
        }
        
        .sdc-label {
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .sdc-data {
            font-size: 9px;
            word-break: break-all;
            margin: 4px 0;
            padding: 2px;
            background: white;
            border: 1px dotted #666;
        }
        
        /* QR Code */
        .qr-container {
            text-align: center;
            margin: 10px 0;
            padding: 6px;
            border: 1px dashed #000;
        }
        
        /* Receipt Footer */
        .receipt-footer {
            border-top: 2px solid #000;
            padding: 6px 0;
            margin: 10px 0;
        }
        
        .footer-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 11px;
        }
        
        /* Thank You Section */
        .thank-you-section {
            text-align: center;
            margin-top: 15px;
            padding: 8px;
            border: 2px dashed #000;
        }
        
        .thank-you-message {
            font-weight: bold;
            font-size: 13px;
            margin-bottom: 4px;
            text-transform: uppercase;
        }
        
        .version-info {
            font-size: 9px;
            color: #666;
            margin-top: 4px;
        }
        
        /* Utility Classes */
        .bold { font-weight: bold; }
        .center { text-align: center; }
        .small { font-size: 10px; }
        .large { font-size: 14px; }
        .uppercase { text-transform: uppercase; }
        
        /* Separators */
        .separator-thick {
            border-top: 2px solid #000;
            margin: 8px 0;
        }
        
        .separator-dashed {
            border-top: 2px dashed #000;
            margin: 6px 0;
        }
        
        .separator-dotted {
            border-top: 1px dotted #666;
            margin: 4px 0;
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="logo-container">
                <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('ebm_logo_2.png'))) }}" alt="EBM Logo 1">
                <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('ebm_logo_1.png'))) }}" alt="EBM Logo 2">
            </div>
            
            <div class="company-info">
                <div class="company-name">{{ $company->name }}</div>
                <div class="company-details">{{ $company->address }}</div>
                <div class="company-details">TEL: {{ $company->phone }}</div>
                <div class="company-details" style="display: none;">EMAIL: {{ $company->email }}</div>
                <div class="company-tin">TIN: {{ $company->tin }}</div>
            </div>
        </div>

        <!-- Transaction Type -->
        <div class="transaction-type">
            @if ($transaction->type == 'NS')
                <span style="display: none;">SALES</span>
            @elseif ($transaction->type == 'TS')
                TRAINING SALES
            @elseif ($transaction->type == 'TR')
                TRAINING REFUND
            @elseif ($transaction->type == 'PS')
                PROFORMA
            @endif
        </div>

        <!-- Refund Information -->
        @if ($transaction->type == 'NR' || $transaction->type == 'TR')
            <div class="refund-notice">
                @if ($transaction->type == 'NR')
                    <div class="bold large">REFUND</div>
                @endif
                <div class="separator-dotted"></div>
                <div>REF. NORMAL RECEIPT: {{ $transaction->originalInvoiceNumber }}</div>
                <div class="separator-dotted"></div>
                <div>REFUND IS APPROVED ONLY FOR ORIGINAL SALES RECEIPT</div>
            </div>
        @endif

        <!-- Top Message -->
        @if (!empty($branch->topMessage))
            <div class="top-message">
                {{ $branch->topMessage }}
            </div>
        @endif

        <!-- Reference Section -->
        <div class="reference-section">
            <div class="reference-line">
                <span class="bold">Reference:</span>
                <span>Served by {{ $transaction->user->name }}</span>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="customer-section">
            <div class="customer-title">Customer Information</div>
            <div class="customer-line">
                <span class="customer-label">TIN:</span>
                <span class="customer-value">{{ $transaction->clientTin ?? 'N/A' }}</span>
            </div>
            <div class="customer-line">
                <span class="customer-label">Name:</span>
                <span class="customer-value">{{ $transaction->clientName ?? 'Walk-in Customer' }}</span>
            </div>
            <div class="customer-line">
                <span class="customer-label">Phone:</span>
                <span class="customer-value">{{ $transaction->clientPhoneNumber ?? 'N/A' }}</span>
            </div>
        </div>

        <!-- Items Section -->
        <div class="items-section">
            @foreach ($transaction->items as $item)
                <div class="item">
                    <div class="item-name">{{ $item->productName }}</div>
                    <div class="item-details">
                        <span class="item-price-qty">
                            {{ number_format($item->price, 2) }} × {{ $item->quantity }}
                        </span>
                        <span class="item-total">
                            @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                            @if ($item->discount > 0)
                                {{ number_format($item->totalAmount + $item->totalDiscount, 2) }}
                            @else
                                {{ number_format($item->totalAmount, 2) }}
                            @endif
                            <span class="item-tax">
                                @if ($item->taxName == 'A')A-EX
                                @elseif ($item->taxName == 'B')B
                                @elseif ($item->taxName == 'C')C  
                                @elseif ($item->taxName == 'D')D
                                @endif
                            </span>
                        </span>
                    </div>
                    @if ($item->discount > 0)
                        <div class="discount-line">
                            <span>Discount {{ $item->discount }}%</span>
                            <span>
                                @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                                {{ number_format($item->totalAmount, 2) }}
                            </span>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>

        <!-- Not Official Notice -->
        @if ($transaction->type == 'PS' || $transaction->type == 'TS' || $transaction->type == 'TR' || $copy == true)
            <div class="not-official-notice">
                THIS IS NOT AN OFFICIAL RECEIPT
            </div>
        @endif

        <!-- Copy Notice -->
        @if ($copy)
            <div class="copy-notice">
                COPY
            </div>
        @endif

        <!-- Training/Proforma Mode -->
        @if ($transaction->salesTypeCode == 'T')
            <div class="mode-notice">
                TRAINING MODE
            </div>
        @endif

        @if ($transaction->salesTypeCode == 'P')
            <div class="mode-notice">
                PROFORMA
            </div>
        @endif

        <!-- Totals Section -->
        <div class="totals-section">
            <div class="total-line main-total">
                <span class="total-label">TOTAL</span>
                <span class="total-value">
                    @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                    {{ number_format($transaction->totAmt, 2, '.', ',') }} Frw
                </span>
            </div>

            @if ($transaction->taxblAmtA > 0)
                <div class="total-line">
                    <span class="total-label">Total A-EX</span>
                    <span class="total-value">
                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                        {{ number_format($transaction->taxblAmtA, 2) }}
                    </span>
                </div>
            @endif

            @if ($transaction->taxblAmtB > 0)
                <div class="total-line">
                    <span class="total-label">Total B-18%</span>
                    <span class="total-value">
                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                        {{ number_format($transaction->taxblAmtB, 2) }}
                    </span>
                </div>
            @endif

            @if ($transaction->taxAmtB > 0 || $transaction->taxAmtB < 0)
                <div class="total-line">
                    <span class="total-label">Total Tax B</span>
                    <span class="total-value">
                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                        {{ number_format($transaction->taxAmtB, 2) }}
                    </span>
                </div>
            @endif

            @if ($transaction->taxblAmtC > 0)
                <div class="total-line">
                    <span class="total-label">Total C</span>
                    <span class="total-value">
                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                        {{ number_format($transaction->taxblAmtC, 2) }}
                    </span>
                </div>
            @endif

            @if ($transaction->taxblAmtD > 0)
                <div class="total-line">
                    <span class="total-label">Total D</span>
                    <span class="total-value">
                        @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                        {{ number_format($transaction->taxblAmtD, 2) }}
                    </span>
                </div>
            @endif

            <div class="total-line">
                <span class="total-label">Total Tax</span>
                <span class="total-value">
                    @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                    {{ number_format($transaction->totTaxAmt, 2) }}
                </span>
            </div>
        </div>

        <!-- Payment Section -->
        <div class="payment-section">
            <div class="payment-line">
                <span class="payment-method">
                    @if ($transaction->paymentTypeCode == '01')CASH
                    @elseif ($transaction->paymentTypeCode == '02')CREDIT
                    @elseif ($transaction->paymentTypeCode == '03')CASH/CREDIT
                    @elseif ($transaction->paymentTypeCode == '04')BANK CHECK
                    @elseif ($transaction->paymentTypeCode == '05')DEBIT & CREDIT CARD
                    @elseif ($transaction->paymentTypeCode == '06')MOBILE MONEY
                    @elseif ($transaction->paymentTypeCode == '07')OTHER
                    @endif
                </span>
                <span class="bold">
                    @if ($transaction->type == 'NR' || $transaction->type == 'TR')-@endif
                    {{ number_format($transaction->totAmt, 2) }}
                </span>
            </div>

            <div class="separator-dotted"></div>
            
            <div class="payment-line">
                <span class="bold">Items Count</span>
                <span class="bold">{{ $transaction->items->count() }}</span>
            </div>
        </div>

        <!-- Insurance Information -->
        @if ($transaction->party?->patient_details != null)
            <div class="insurance-section">
                <div class="insurance-title">Insurance Details</div>
                <div class="insurance-line">
                    <span class="bold">Coverage:</span>
                    <span>{{ $transaction->party->patient_details->percentage }}%</span>
                </div>
                <div class="insurance-line">
                    <span class="bold">Insurance Discount:</span>
                    <span>{{ number_format(($transaction->totAmt * $transaction->party->patient_details->percentage) / 100, 2) }}</span>
                </div>
                <div class="insurance-line">
                    <span class="bold">Amount to be paid:</span>
                    <span class="bold">{{ number_format($transaction->totAmt - ($transaction->totAmt * $transaction->party->patient_details->percentage) / 100, 2) }}</span>
                </div>
            </div>
        @endif

        <!-- SDC Information -->
        @if ($transaction->signature != null && $transaction->signature !== '')
            <div class="sdc-section">
                <div class="sdc-title">SDC INFORMATION</div>
                
                <div class="sdc-line">
                    <span class="sdc-label">Date & Time:</span>
                    <span>
                        {{ date('d/m/Y H:i:s', strtotime($transaction->signature?->vsdcReceiptPublishDate)) }}
                    </span>
                </div>
                
                <div class="sdc-line">
                    <span class="sdc-label">SDC ID:</span>
                    <span>{{ $transaction->signature?->sdcId }}</span>
                </div>
                
                <div class="sdc-line">
                    <span class="sdc-label">RECEIPT NUMBER:</span>
                    <span>{{ $transaction->signature?->receiptNumber }}/{{ $transaction->signature?->totalReceiptNumber }} {{ $transaction->type }}</span>
                </div>

                @if ($transaction->signature?->internalData != null)
                    <div class="sdc-line">
                        <span class="sdc-label">Internal Data:</span>
                    </div>
                    <div class="sdc-data">{{ $transaction->signature?->internalData }}</div>
                @endif

                @if ($transaction->signature?->receiptSignature != null)
                    <div class="sdc-line">
                        <span class="sdc-label">Receipt Signature:</span>
                    </div>
                    <div class="sdc-data">{{ $transaction->signature?->receiptSignature }}</div>
                @endif

                @if ($transaction->salesTypeCode !== 'P' && $transaction->signature != null)
                    <div class="qr-container">
                        {{ QrCode::generate('Demo QR code data for EBM Invoice from HIQ Zata') }}
                    </div>
                @endif
            </div>
        @endif

        <!-- Receipt Footer -->
        @if ($transaction->signature != null)
            <div class="receipt-footer">
                <div class="footer-line">
                    <span class="bold">RECEIPT NUMBER:</span>
                    <span>{{ $transaction->signature?->invoiceNumber }}</span>
                </div>
                
                <div class="footer-line">
                    <span class="bold">Date:</span>
                    <span>{{ date('d-m-Y', strtotime($transaction->signature?->vsdcReceiptPublishDate)) }}</span>
                </div>
                
                <div class="footer-line">
                    <span class="bold">Time:</span>
                    <span>{{ date('h:i A', strtotime($transaction->signature?->vsdcReceiptPublishDate)) }}</span>
                </div>
                
                <div class="footer-line">
                    <span class="bold">MRC:</span>
                    <span>{{ $branch->mrc }}</span>
                </div>
            </div>
        @endif

        <!-- Thank You Section -->
        <div class="thank-you-section">
            <div class="thank-you-message">{{ $branch->bottomMessage ?? 'Thank You for Your Business!' }}</div>
            <div class="version-info">Version V1.0.0 Powered by RRA_VSDC</div>
        </div>
    </div>
</body>
</html>