<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Company extends Model
{

    use HasFactory;

    protected $dateFormat = 'U';

    protected $table = 'Company';

    protected $fillable = [
        "user_id",
        "name",
        "address",
        "tin",
        "phone",
        "email",
        "isActive",
        "isEBM",
    ];

    protected $casts = [
        'isActive' => 'boolean',
        'isEBM' => 'boolean',
        'isInitialised' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function branches(): HasMany
    {
        return $this->hasMany(CompanyBranch::class, 'company_id');
    }

    public function roles(): HasMany
    {
        return $this->hasMany(Role::class, 'company_id', 'id');
    }

    public function insurance(): HasMany
    {
        return $this->hasMany(CompanyInsurance::class, 'company_id');
    }

}
